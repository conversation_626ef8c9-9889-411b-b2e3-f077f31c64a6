/**
 * BusinessUnitsManagement - Admin interface for managing business units
 */

import { useState, useEffect, useCallback } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import Card from '../../components/ui/Card.jsx'
import Button from '../../components/ui/Button.jsx'
import Badge from '../../components/ui/Badge.jsx'
import BusinessUnitsList from '../components/BusinessUnitsList.jsx'
import BusinessUnitEditor from '../components/BusinessUnitEditor.jsx'
import businessUnitService from '../../services/BusinessUnitService.js'
import { useToast } from '../../components/ui/Toast.jsx'

export default function BusinessUnitsManagement() {
  const [view, setView] = useState('list') // list, create, edit
  const [selectedBusinessUnit, setSelectedBusinessUnit] = useState(null)
  const [businessUnits, setBusinessUnits] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const navigate = useNavigate()
  const { id } = useParams()
  const { showToast, ToastContainer } = useToast()

  // Load business units data on component mount
  useEffect(() => {
    loadBusinessUnitsData()
  }, [])

    const loadBusinessUnitsData = async () => {
    setLoading(true)
    setError(null)

    try {
      const data = await businessUnitService.getAllBusinessUnits()
      setBusinessUnits(data)
    } catch (err) {
      console.error('Error loading business units:', err)
      setError('Failed to load business units. Please try again.')
      showToast('Failed to load business units', 'error')
    } finally {
      setLoading(false)
    }
  }

  const fetchBusinessUnitForEdit = useCallback(async (businessUnitId) => {
    try {
      // First try to find in already loaded business units
      let businessUnit = businessUnits.find((unit) => unit.id === businessUnitId)
      
      if (!businessUnit) {
        // If not found, try to fetch from API by slug
        try {
          businessUnit = await businessUnitService.getBusinessUnit(businessUnitId)
        } catch (apiErr) {
          console.error('Business unit not found in API:', businessUnitId)
          showToast('Business unit not found', 'error')
          navigate('/admin/business-units')
          return
        }
      }
      
      setSelectedBusinessUnit(businessUnit)
    } catch (err) {
      console.error('Error fetching business unit for edit:', err)
      showToast('Error loading business unit for editing', 'error')
      navigate('/admin/business-units')
    }
  }, [businessUnits, navigate, showToast])

  // Handle URL-based routing for editing
  useEffect(() => {
    if (id && businessUnits.length > 0) {
      // If there's an ID in the URL, we're in edit mode
      setView('edit')
      // Fetch the actual business unit data
      fetchBusinessUnitForEdit(id)
    } else if (!id) {
      setView('list')
      setSelectedBusinessUnit(null)
    }
  }, [id, businessUnits, fetchBusinessUnitForEdit])

  const handleCreateBusinessUnit = () => {
    setSelectedBusinessUnit(null)
    setView('create')
  }

  const handleEditBusinessUnit = (businessUnit) => {
    // Navigate to the edit route instead of changing state
    navigate(`/admin/business-units/${businessUnit.id}/edit`)
  }

  const handleDeleteBusinessUnit = async (businessUnitId) => {
    try {
      await businessUnitService.deleteBusinessUnit(businessUnitId)

      // Remove from local state
      setBusinessUnits(prev => prev.filter((unit) => unit.id !== businessUnitId))

      showToast('Business unit deleted successfully', 'success')
      console.log('Business unit deleted successfully:', businessUnitId)
    } catch (err) {
      console.error('Error deleting business unit:', err)
      showToast('Failed to delete business unit', 'error')
    }
  }

  const handleSaveBusinessUnit = async (businessUnitData) => {
    try {
      let savedBusinessUnit

      if (businessUnitData.id && businessUnits.find(unit => unit.id === businessUnitData.id)) {
        // Update existing business unit
        savedBusinessUnit = await businessUnitService.updateBusinessUnit(businessUnitData.id, businessUnitData)
        
        // Update local state
        setBusinessUnits(prev => prev.map((unit) => {
        if (unit.id === businessUnitData.id) {
            return savedBusinessUnit
        }
        return unit
        }))
        
        showToast('Business unit updated successfully', 'success')
      } else {
        // Create new business unit
        savedBusinessUnit = await businessUnitService.createBusinessUnit(businessUnitData)
        
        // Add to local state
        setBusinessUnits(prev => [...prev, savedBusinessUnit])
        
        showToast('Business unit created successfully', 'success')
      }

      console.log('Business unit saved successfully:', businessUnitData.name)

      // Navigate back to the business units list after saving
      navigate('/admin/business-units')
    } catch (err) {
      console.error('Error saving business unit:', err)
      showToast('Failed to save business unit', 'error')
      // Don't navigate away on error, let user try again
    }
  }

  const handleCancel = () => {
    // Navigate back to the business units list
    navigate('/admin/business-units')
  }

  if (view === 'create' || view === 'edit') {
    return (
      <BusinessUnitEditor
        businessUnit={selectedBusinessUnit}
        onSave={handleSaveBusinessUnit}
        onCancel={handleCancel}
        mode={view}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Business Units Management</h1>
          <p className="text-zinc-400">Manage your business unit details and content</p>
          {businessUnits.length > 0 && businessUnits[0]._isFallbackData && (
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="warning" size="sm">
                Demo Mode
              </Badge>
              <span className="text-xs text-zinc-500">
                Using fallback data. Start backend server for dynamic features.
              </span>
            </div>
          )}
          {businessUnits.length > 0 && businessUnits[0]._isRealData && (
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="success" size="sm">
                Live Data
              </Badge>
              <span className="text-xs text-zinc-500">
                Connected to backend server. All CRUD operations available.
              </span>
            </div>
          )}
        </div>
        <Button onClick={handleCreateBusinessUnit}>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          New Business Unit
        </Button>
      </div>

      {/* Error Display */}
      {error && (
        <Card padding="lg">
          <div className="flex items-center justify-between p-4 bg-red-600/10 border border-red-600/20 rounded-lg">
            <div className="flex items-center gap-3 text-red-400">
              <svg
                className="w-5 h-5 flex-shrink-0"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <p>{error}</p>
            </div>
            <Button variant="secondary" size="sm" onClick={loadBusinessUnitsData}>
              Retry
            </Button>
          </div>
        </Card>
      )}

      {/* Business Units List */}
      <BusinessUnitsList
        businessUnits={businessUnits}
        onEdit={handleEditBusinessUnit}
        onDelete={handleDeleteBusinessUnit}
        onRefresh={loadBusinessUnitsData}
        loading={loading}
      />

      {/* Toast Notifications */}
      <ToastContainer />
    </div>
  )
}
