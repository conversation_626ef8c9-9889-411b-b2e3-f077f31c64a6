/**
 * ProductFeaturesList - Component for managing product features with add/edit/remove functionality
 */

import { useState, useEffect } from 'react'
import Button from '../../components/ui/Button.jsx'

export default function ProductFeaturesList({ features = [], onChange }) {
  const [featureList, setFeatureList] = useState([])
  const [errors, setErrors] = useState({})

  // Initialize feature list
  useEffect(() => {
    setFeatureList(features.length > 0 ? features : [''])
  }, [features])

  const validateFeature = (index, value) => {
    const newErrors = { ...errors }
    const errorKey = `feature_${index}`

    if (!value || value.trim().length === 0) {
      newErrors[errorKey] = 'Feature text cannot be empty'
    } else if (value.length > 200) {
      newErrors[errorKey] = 'Feature text must be 200 characters or less'
    } else {
      delete newErrors[errorKey]
    }

    setErrors(newErrors)
    return !newErrors[errorKey]
  }

  const handleFeatureChange = (index, value) => {
    const newFeatures = [...featureList]
    newFeatures[index] = value
    setFeatureList(newFeatures)

    // Validate the changed feature
    validateFeature(index, value)

    // Filter out empty features for the parent component
    const filteredFeatures = newFeatures.filter((feature) => feature && feature.trim().length > 0)
    if (onChange) {
      onChange(filteredFeatures)
    }
  }

  const addFeature = () => {
    const newFeatures = [...featureList, '']
    setFeatureList(newFeatures)
  }

  const removeFeature = (index) => {
    if (featureList.length <= 1) return // Keep at least one feature field

    const newFeatures = featureList.filter((_, i) => i !== index)
    setFeatureList(newFeatures)

    // Remove errors for this index
    const newErrors = { ...errors }
    Object.keys(newErrors).forEach((key) => {
      if (key.startsWith(`feature_${index}_`)) {
        delete newErrors[key]
      }
    })

    // Reindex remaining errors
    const reindexedErrors = {}
    Object.keys(newErrors).forEach((key) => {
      if (key.startsWith('feature_')) {
        const oldIndex = parseInt(key.split('_')[1])

        if (oldIndex > index) {
          reindexedErrors[`feature_${oldIndex - 1}`] = newErrors[key]
        } else if (oldIndex < index) {
          reindexedErrors[key] = newErrors[key]
        }
      } else {
        reindexedErrors[key] = newErrors[key]
      }
    })
    setErrors(reindexedErrors)

    // Filter out empty features for the parent component
    const filteredFeatures = newFeatures.filter((feature) => feature && feature.trim().length > 0)
    if (onChange) {
      onChange(filteredFeatures)
    }
  }

  const duplicateFeature = (index) => {
    const featureToDuplicate = featureList[index]
    const newFeatures = [...featureList]
    newFeatures.splice(index + 1, 0, featureToDuplicate)
    setFeatureList(newFeatures)
  }

  const moveFeature = (fromIndex, toIndex) => {
    if (fromIndex === toIndex) return

    const newFeatures = [...featureList]
    const [movedFeature] = newFeatures.splice(fromIndex, 1)
    newFeatures.splice(toIndex, 0, movedFeature)
    setFeatureList(newFeatures)

    // Filter out empty features for the parent component
    const filteredFeatures = newFeatures.filter((feature) => feature && feature.trim().length > 0)
    if (onChange) {
      onChange(filteredFeatures)
    }
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h5 className="text-sm font-medium text-white">
          Features ({featureList.filter((f) => f.trim()).length})
        </h5>
        <Button size="sm" variant="secondary" onClick={addFeature}>
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          Add Feature
        </Button>
      </div>

      <div className="space-y-2">
        {featureList.map((feature, index) => (
          <div key={index} className="flex items-start gap-2">
            {/* Feature Number */}
            <div className="flex-shrink-0 w-6 h-6 bg-amber-600/20 text-amber-400 rounded-full flex items-center justify-center text-xs font-medium mt-1">
              {index + 1}
            </div>

            {/* Feature Input */}
            <div className="flex-1">
              <input
                type="text"
                value={feature}
                onChange={(e) => handleFeatureChange(index, e.target.value)}
                placeholder="Enter feature description..."
                className={`w-full px-3 py-2 bg-zinc-700 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors text-sm ${
                  errors[`feature_${index}`]
                    ? 'border-red-500 focus:border-red-400'
                    : 'border-zinc-600 focus:border-amber-500'
                }`}
              />
              <div className="flex items-center justify-between mt-1">
                {errors[`feature_${index}`] ? (
                  <p className="text-xs text-red-400">{errors[`feature_${index}`]}</p>
                ) : (
                  <p className="text-xs text-zinc-500">{feature.length}/200 characters</p>
                )}
              </div>
            </div>

            {/* Controls */}
            <div className="flex-shrink-0 flex gap-1 mt-1">
              {/* Move Up */}
              <button
                type="button"
                onClick={() => moveFeature(index, Math.max(0, index - 1))}
                disabled={index === 0}
                className="p-1 text-zinc-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                title="Move up"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 15l7-7 7 7"
                  />
                </svg>
              </button>

              {/* Move Down */}
              <button
                type="button"
                onClick={() => moveFeature(index, Math.min(featureList.length - 1, index + 1))}
                disabled={index === featureList.length - 1}
                className="p-1 text-zinc-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                title="Move down"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>

              {/* Duplicate */}
              <button
                type="button"
                onClick={() => duplicateFeature(index)}
                className="p-1 text-zinc-400 hover:text-white transition-colors"
                title="Duplicate feature"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </svg>
              </button>

              {/* Remove */}
              <button
                type="button"
                onClick={() => removeFeature(index)}
                disabled={featureList.length <= 1}
                className="p-1 text-red-400 hover:text-red-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                title="Remove feature"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                  />
                </svg>
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Features Preview */}
      {featureList.some((feature) => feature && feature.trim().length > 0) && (
        <div className="p-3 bg-zinc-900 border border-zinc-600 rounded-lg">
          <h6 className="text-xs font-medium text-white mb-2">Features Preview</h6>
          <div className="space-y-1">
            {featureList
              .filter((feature) => feature && feature.trim().length > 0)
              .map((feature, index) => (
                <div key={index} className="flex items-start gap-2 text-xs">
                  <svg
                    className="w-3 h-3 text-green-400 flex-shrink-0 mt-0.5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  <span className="text-zinc-300">{feature}</span>
                </div>
              ))}
          </div>
        </div>
      )}

      {/* Help Text */}
      <div className="text-xs text-zinc-500 bg-zinc-900 p-2 rounded">
        <p className="font-medium mb-1">Feature Tips:</p>
        <ul className="list-disc list-inside space-y-0.5 text-xs">
          <li>Highlight key benefits and unique selling points</li>
          <li>Keep features concise and easy to understand</li>
          <li>Use action-oriented language when possible</li>
          <li>Order features by importance (most important first)</li>
        </ul>
      </div>

      {/* Validation Summary */}
      {Object.keys(errors).length > 0 && (
        <div className="p-2 bg-red-600/10 border border-red-600/30 rounded">
          <div className="flex items-start gap-2">
            <svg
              className="w-3 h-3 text-red-400 flex-shrink-0 mt-0.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <div>
              <p className="text-xs font-medium text-red-400 mb-1">Feature validation errors:</p>
              <ul className="text-xs text-red-300 space-y-0.5">
                {Object.values(errors).map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
