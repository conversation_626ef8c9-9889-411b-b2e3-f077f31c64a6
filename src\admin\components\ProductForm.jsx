/**
 * ProductForm - Form component for editing individual product details
 */

import { useState, useEffect } from 'react'
import ProductFeaturesList from './ProductFeaturesList.jsx'

export default function ProductForm({ product, index, onChange, errors = {} }) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    image: '',
    price: '',
    features: [],
  })

  // Initialize form data
  useEffect(() => {
    if (product) {
      setFormData({
        title: product.title || '',
        description: product.description || '',
        image: product.image || '',
        price: product.price || '',
        features: product.features || [],
      })
    }
  }, [product])

  const handleInputChange = (field, value) => {
    const newFormData = { ...formData, [field]: value }
    setFormData(newFormData)

    if (onChange) {
      onChange(index, field, value)
    }
  }

  const handleFeaturesChange = (features) => {
    const newFormData = { ...formData, features }
    setFormData(newFormData)

    if (onChange) {
      onChange(index, 'features', features)
    }
  }

  return (
    <div className="space-y-4 p-4 bg-zinc-800 rounded-lg">
      <div className="flex items-center gap-2 mb-4">
        <svg
          className="w-5 h-5 text-amber-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
          />
        </svg>
        <h4 className="text-white font-medium">Edit Product {index + 1}</h4>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Product Title */}
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-white mb-2">Product Title *</label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            placeholder="Enter product title"
            className={`w-full px-3 py-2 bg-zinc-700 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors ${
              errors.title
                ? 'border-red-500 focus:border-red-400'
                : 'border-zinc-600 focus:border-amber-500'
            }`}
          />
          <div className="flex items-center justify-between mt-1">
            {errors.title ? (
              <p className="text-sm text-red-400">{errors.title}</p>
            ) : (
              <p className="text-sm text-zinc-500">{formData.title.length}/100 characters</p>
            )}
          </div>
        </div>

        {/* Product Price */}
        <div>
          <label className="block text-sm font-medium text-white mb-2">Price</label>
          <input
            type="text"
            value={formData.price}
            onChange={(e) => handleInputChange('price', e.target.value)}
            placeholder="e.g., Rp 25.000/kg"
            className={`w-full px-3 py-2 bg-zinc-700 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors ${
              errors.price
                ? 'border-red-500 focus:border-red-400'
                : 'border-zinc-600 focus:border-amber-500'
            }`}
          />
          {errors.price && <p className="text-sm text-red-400 mt-1">{errors.price}</p>}
        </div>

        {/* Product Image */}
        <div>
          <label className="block text-sm font-medium text-white mb-2">Image URL</label>
          <input
            type="url"
            value={formData.image}
            onChange={(e) => handleInputChange('image', e.target.value)}
            placeholder="https://example.com/image.jpg"
            className={`w-full px-3 py-2 bg-zinc-700 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors ${
              errors.image
                ? 'border-red-500 focus:border-red-400'
                : 'border-zinc-600 focus:border-amber-500'
            }`}
          />
          {errors.image && <p className="text-sm text-red-400 mt-1">{errors.image}</p>}
        </div>

        {/* Product Description */}
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-white mb-2">Description</label>
          <textarea
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Enter product description"
            rows={3}
            className={`w-full px-3 py-2 bg-zinc-700 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors resize-vertical ${
              errors.description
                ? 'border-red-500 focus:border-red-400'
                : 'border-zinc-600 focus:border-amber-500'
            }`}
          />
          <div className="flex items-center justify-between mt-1">
            {errors.description ? (
              <p className="text-sm text-red-400">{errors.description}</p>
            ) : (
              <p className="text-sm text-zinc-500">{formData.description.length}/500 characters</p>
            )}
          </div>
        </div>
      </div>

      {/* Image Preview */}
      {formData.image && (
        <div>
          <label className="block text-sm font-medium text-white mb-2">Image Preview</label>
          <div className="relative">
            <img
              src={formData.image}
              alt={formData.title || 'Product preview'}
              className="w-full max-w-md h-48 object-cover rounded-lg"
              onError={(e) => {
                e.target.style.display = 'none'
                e.target.nextSibling.style.display = 'flex'
              }}
            />
            <div className="hidden w-full max-w-md h-48 bg-zinc-700 rounded-lg items-center justify-center">
              <div className="text-center">
                <svg
                  className="w-12 h-12 text-zinc-500 mx-auto mb-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
                <p className="text-zinc-500 text-sm">Failed to load image</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Product Features */}
      <div>
        <label className="block text-sm font-medium text-white mb-2">Product Features</label>
        <ProductFeaturesList features={formData.features} onChange={handleFeaturesChange} />
      </div>

      {/* Product Preview */}
      {(formData.title || formData.description || formData.price) && (
        <div>
          <label className="block text-sm font-medium text-white mb-2">Product Preview</label>
          <div className="p-4 bg-zinc-900 border border-zinc-600 rounded-lg">
            <div className="flex gap-4">
              {formData.image && (
                <div className="flex-shrink-0">
                  <img
                    src={formData.image}
                    alt={formData.title}
                    className="w-20 h-20 object-cover rounded-lg"
                    onError={(e) => {
                      e.target.style.display = 'none'
                    }}
                  />
                </div>
              )}
              <div className="flex-1">
                {formData.title && (
                  <h5 className="text-white font-medium mb-1">{formData.title}</h5>
                )}
                {formData.description && (
                  <p className="text-zinc-400 text-sm mb-2 line-clamp-2">{formData.description}</p>
                )}
                {formData.price && (
                  <p className="text-amber-400 font-medium text-sm mb-2">{formData.price}</p>
                )}
                {formData.features && formData.features.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {formData.features.slice(0, 3).map((feature, featureIndex) => (
                      <span
                        key={featureIndex}
                        className="inline-flex items-center px-2 py-1 text-xs bg-zinc-700 text-zinc-300 rounded"
                      >
                        {feature}
                      </span>
                    ))}
                    {formData.features.length > 3 && (
                      <span className="inline-flex items-center px-2 py-1 text-xs bg-zinc-700 text-zinc-300 rounded">
                        +{formData.features.length - 3} more
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Form Tips */}
      <div className="text-sm text-zinc-500 bg-zinc-900 p-3 rounded-lg">
        <p className="font-medium mb-1">Tips:</p>
        <ul className="list-disc list-inside space-y-1 text-xs">
          <li>Use descriptive titles that clearly identify your product</li>
          <li>Include pricing information to help customers make decisions</li>
          <li>Add high-quality images for better visual appeal</li>
          <li>List key features that highlight product benefits</li>
          <li>Keep descriptions concise but informative</li>
        </ul>
      </div>
    </div>
  )
}
