# Cigi Global - Aplikasi React Modern

Aplikasi React modern untuk PT Cimande Girang Global, dengan desain UI/UX kontemporer, gaya profesional, dan pola React modern. Aplikasi ini menyediakan platform perusahaan dengan sistem manajemen konten berita dan antarmuka administrasi.

## 🚀 Fitur

### Aplikasi Frontend Modern

- **Desain Visual Kontemporer**: Skema warna modern, tipografi, dan spasi
- **Gaya Profesional**: Sistem desain konsisten dengan hierarki visual yang baik
- **Desain Responsif**: Dioptimalkan untuk semua ukuran perangkat dengan pendekatan mobile-first
- **Animasi Halus**: Animasi dan transisi berbasis AOS (Animate On Scroll)
- **Efek Glass Morphism**: Efek blur latar belakang dan transparansi modern

### Sistem Admin & CMS

- **Dashboard Admin**: Interface admin komprehensif untuk manajemen konten
- **Manajemen Berita**: Sistem CRUD lengkap untuk artikel dan berita
- **Editor Markdown**: Editor berita dengan dukungan markdown dan media
- **Autentikasi Aman**: Sistem login dengan manajemen sesi yang aman
- **Pengaturan Situs**: Konfigurasi situs dan preferensi pengguna

### Peningkatan Teknis

- **Pola React Modern**: Komponen fungsional dengan hooks dan Suspense
- **Lazy Loading**: Pemisahan kode untuk performa optimal
- **Error Boundaries**: Penanganan kesalahan yang komprehensif
- **Aksesibilitas**: Sesuai WCAG dengan manajemen fokus yang tepat
- **SEO Optimized**: Struktur HTML semantik dan meta tag

## 🛠️ Teknologi yang Digunakan

### Frontend

- **React 19** - Framework JavaScript modern dengan concurrent features
- **Vite** - Build tool cepat dengan HMR
- **Tailwind CSS 4** - Framework CSS berbasis utilitas
- **React Router DOM 6** - Routing sisi klien
- **Lucide React** - Pustaka ikon modern
- **AOS** - Pustaka animasi saat scroll

### Backend & Database

- **Express.js** - Server web Node.js
- **SQLite3** - Database lokal dengan Better SQLite3
- **bcryptjs** - Hashing password yang aman
- **express-session** - Manajemen sesi
- **CORS** - Cross-origin resource sharing

### Development Tools

- **ESLint** - Linting dan analisis kode
- **Prettier** - Code formatting
- **Concurrently** - Menjalankan multiple scripts

## 📁 Struktur Proyek

```
Cigi-Global/
├── src/
│   ├── components/           # Komponen UI reusable
│   │   ├── ui/              # Komponen UI dasar
│   │   │   ├── Button.jsx
│   │   │   ├── Card.jsx
│   │   │   ├── Badge.jsx
│   │   │   ├── Container.jsx
│   │   │   ├── Section.jsx
│   │   │   └── AnimatedElement.jsx
│   │   ├── news/            # Komponen khusus berita
│   │   │   ├── NewsArticle.jsx
│   │   │   └── NewsList.jsx
│   │   ├── ErrorBoundary.jsx
│   │   ├── Footer.jsx
│   │   ├── Navbar.jsx
│   │   └── PreLoader.jsx
│   ├── admin/               # Sistem administrasi
│   │   ├── auth/           # Autentikasi admin
│   │   │   └── AuthManager.js
│   │   ├── components/     # Komponen admin
│   │   │   ├── AdminLayout.jsx
│   │   │   ├── AdminLogin.jsx
│   │   │   └── NewsEditor.jsx
│   │   └── pages/          # Halaman admin
│   │       ├── AdminDashboard.jsx
│   │       ├── NewsManagement.jsx
│   │       └── SiteSettings.jsx
│   ├── Pages/              # Halaman publik
│   │   ├── Home.jsx
│   │   ├── About.jsx
│   │   ├── Contact.jsx
│   │   ├── News.jsx
│   │   ├── Usaha.jsx
│   │   ├── NotFound.jsx
│   │   └── Detailusaha/    # Halaman detail unit usaha
│   │       ├── cigifarm/
│   │       │   ├── index.jsx
│   │       │   ├── about.jsx
│   │       │   ├── product.jsx
│   │       │   ├── contact.jsx
│   │       │   └── data.js
│   │       ├── cigimart/
│   │       │   ├── index.jsx
│   │       │   ├── about.jsx
│   │       │   ├── product.jsx
│   │       │   ├── contact.jsx
│   │       │   └── data.js
│   │       ├── ciginet/
│   │       │   ├── index.jsx
│   │       │   ├── about.jsx
│   │       │   ├── product.jsx
│   │       │   ├── contact.jsx
│   │       │   └── data.js
│   │       ├── cigifood/
│   │       │   ├── index.jsx
│   │       │   ├── about.jsx
│   │       │   ├── product.jsx
│   │       │   ├── contact.jsx
│   │       │   └── data.js
│   │       ├── pbcigi/
│   │       │   ├── index.jsx
│   │       │   ├── about.jsx
│   │       │   ├── product.jsx
│   │       │   ├── contact.jsx
│   │       │   └── data.js
│   │       ├── cigifc/
│   │       │   ├── index.jsx
│   │       │   ├── about.jsx
│   │       │   ├── product.jsx
│   │       │   └── contact.jsx
│   │       └── cigiarchery/
│   │           ├── index.jsx
│   │           ├── about.jsx
│   │           ├── product.jsx
│   │           └── contact.jsx
│   ├── server/             # Backend Express.js
│   │   ├── server.js       # Main server file
│   │   └── SessionManager.js
│   ├── services/           # Services layer
│   │   ├── NewsService.js
│   │   └── SettingsService.js
│   ├── hooks/              # Custom React hooks
│   │   ├── useIntersectionObserver.js
│   │   └── useScrollAnimation.js
│   ├── styles/
│   │   └── design-system.css
│   ├── contexts/           # React contexts
│   ├── utils/              # Utility functions
│   ├── data.js             # Static data
│   ├── App.jsx             # Root component
│   └── main.jsx            # Entry point
├── data/                   # SQLite databases
│   ├── sessions.db         # User sessions
│   └── modular_system.db   # Application data
├── public/                 # Static assets
│   └── assets/
└── package.json
```

## 🎨 Sistem Desain

### Palet Warna

- **Utama**: Amber (600-700) untuk CTA dan sorotan
- **Netral**: Zinc (50-900) untuk latar belakang dan teks
- **Semantik**: Sukses (hijau), Peringatan (kuning), Kesalahan (merah), Info (biru)

### Skala Tipografi

- Tipografi responsif menggunakan `clamp()` untuk penskalaan yang fleksibel
- Berat font: 400 (reguler), 600 (semi tebal), 700 (tebal)
- Tinggi baris dioptimalkan untuk keterbacaan

### Variasi Komponen

- **Tombol**: Primary, Secondary, Outline, Ghost, Danger
- **Kartu**: Default, Hover effects, Glass morphism
- **Lencana**: Default, Primary, Success, Warning, Danger, Info

## 🚀 Memulai

### Prasyarat

- Node.js 18+
- npm atau yarn

### Instalasi

1. Clone repositori

```bash
git clone https://github.com/MuhammadAnggaNugraha/cigi-global.git
cd cigi-global
```

2. Install dependensi

```bash
npm install
```

3. Jalankan development server

```bash
npm run dev
```

4. Jalankan full stack (Frontend + Backend)

```bash
npm run dev:full
```

5. Build untuk produksi

```bash
npm run build
```

## 🔧 Scripts Tersedia

```bash
npm run dev          # Jalankan frontend development server
npm run dev:server   # Jalankan backend server saja
npm run dev:full     # Jalankan frontend + backend bersamaan
npm run build        # Build untuk produksi
npm run preview      # Preview build produksi
npm run lint         # Jalankan ESLint
npm run format       # Format kode dengan Prettier
```

## 📱 Halaman & Fitur

### Halaman Publik

- **Beranda**: Hero section, company overview, portfolio showcase
- **Tentang**: Company profile, vision/mission, business units
- **Unit Usaha**: Business units dengan detail pages
- **Berita**: News listing dan artikel detail
- **Kontak**: Contact information dan form

### Panel Admin

- **Dashboard**: Overview statistik dan aktivitas terbaru
- **Manajemen Berita**: CRUD operations untuk articles
- **Editor**: Rich text editor dengan markdown support
- **Pengaturan**: Site settings dan konfigurasi

## 🛡️ Keamanan

- **Autentikasi**: Secure login dengan bcrypt password hashing
- **Sesi**: Server-side session management dengan expiration
- **Validasi**: Input validation dan sanitization
- **CORS**: Configured untuk development dan production

## 🎯 Optimisasi yang Telah Dilakukan

### 1. Arsitektur Kode

- ✅ Menghapus sistem modular yang tidak terpakai (~15 files)
- ✅ Menyederhanakan struktur menjadi arsitektur React standar
- ✅ Membersihkan import yang tidak digunakan
- ✅ Menstandarisasi pola React hooks dan functional components

### 2. Performa

- ✅ Lazy loading untuk semua halaman
- ✅ Code splitting dengan React.lazy()
- ✅ Optimized AOS initialization
- ✅ Efficient bundle size

### 3. Developer Experience

- ✅ Consistent code formatting dengan Prettier
- ✅ ESLint rules untuk code quality
- ✅ Structured project organization
- ✅ Clear separation of concerns

## 🔮 Pengembangan Selanjutnya

- [ ] Implementasi TypeScript untuk type safety
- [ ] Unit testing dengan Jest dan React Testing Library
- [ ] End-to-end testing dengan Playwright
- [ ] PWA implementation
- [ ] Dark mode toggle
- [ ] Internationalization (i18n)
- [ ] Advanced SEO dengan React Helmet
- [ ] Image optimization dan CDN integration

## 🤝 Kontribusi

1. Fork repositori
2. Buat feature branch (`git checkout -b feature/amazing-feature`)
3. Commit perubahan (`git commit -m 'Add amazing feature'`)
4. Push ke branch (`git push origin feature/amazing-feature`)
5. Buka Pull Request

## 📄 Lisensi

Proyek ini dilisensikan di bawah MIT License - lihat file [LICENSE](LICENSE) untuk detailnya.

## 📞 Kontak

PT Cimande Girang Global

- **Website**: [cigiglobal.com](https://cigiglobal.com)
- **Email**: <EMAIL>
- **Telepon**: +62 21 1234 5678
- **Alamat**: Cimande, Bogor, Jawa Barat

---

Dibuat dengan ❤️ oleh tim pengembang Cigi Global
