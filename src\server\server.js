/**
 * Express server for session management and API endpoints
 */

import express from 'express'
import cors from 'cors'
import sessionManager from './SessionManager.js'
import BusinessUnitService from './services/BusinessUnitService.js'
// Note: DataPersistence will be imported dynamically to avoid module loading issues

const app = express()
const PORT = globalThis.process?.env?.PORT || 3002

// Initialize services
const businessUnitService = new BusinessUnitService()

// Middleware
app.use(
  cors({
    origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:5173'],
    credentials: true,
  })
)
app.use(express.json())

// Session validation middleware
const validateSession = (req, res, next) => {
  const sessionId = req.headers.authorization?.replace('Bearer ', '')

  if (!sessionId) {
    return res.status(401).json({ error: 'No session token provided' })
  }

  const validation = sessionManager.validateSession(sessionId)

  if (!validation.valid) {
    return res.status(401).json({ error: validation.error })
  }

  req.user = validation.user
  req.sessionId = sessionId
  next()
}

// Auth endpoints
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body

    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password required' })
    }

    const result = await sessionManager.login(username, password)

    if (result.success) {
      res.json({
        success: true,
        sessionId: result.sessionId,
        user: result.user,
        expiresAt: result.expiresAt,
      })
    } else {
      res.status(401).json({ error: result.error })
    }
  } catch (error) {
    console.error('Login endpoint error:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
})

app.post('/api/auth/logout', validateSession, (req, res) => {
  try {
    const success = sessionManager.logout(req.sessionId)

    if (success) {
      res.json({ success: true })
    } else {
      res.status(400).json({ error: 'Logout failed' })
    }
  } catch (error) {
    console.error('Logout endpoint error:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
})

app.get('/api/auth/validate', validateSession, (req, res) => {
  try {
    // Refresh session on validation
    sessionManager.refreshSession(req.sessionId)

    res.json({
      valid: true,
      user: req.user,
    })
  } catch (error) {
    console.error('Validation endpoint error:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
})

app.post('/api/auth/refresh', validateSession, (req, res) => {
  try {
    const success = sessionManager.refreshSession(req.sessionId)

    if (success) {
      res.json({ success: true })
    } else {
      res.status(400).json({ error: 'Session refresh failed' })
    }
  } catch (error) {
    console.error('Refresh endpoint error:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
})

app.post('/api/auth/change-password', validateSession, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body

    if (!currentPassword || !newPassword) {
      return res.status(400).json({ error: 'Current and new password required' })
    }

    const result = await sessionManager.changePassword(req.user.id, currentPassword, newPassword)

    if (result.success) {
      res.json({ success: true })
    } else {
      res.status(400).json({ error: result.error })
    }
  } catch (error) {
    console.error('Change password endpoint error:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
})

// Business Units API endpoints

// GET /api/business-units - List all business units
app.get('/api/business-units', async (req, res) => {
  try {
    const businessUnits = await businessUnitService.getAllBusinessUnits()
    res.json({
      success: true,
      data: businessUnits,
    })
  } catch (error) {
    console.error('Get business units error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve business units',
    })
  }
})

// GET /api/business-units/:slug - Get specific business unit data
app.get('/api/business-units/:slug', async (req, res) => {
  try {
    const { slug } = req.params
    const businessUnit = await businessUnitService.getBusinessUnit(slug)

    res.json({
      success: true,
      data: businessUnit,
    })
  } catch (error) {
    console.error('Get business unit error:', error)
    const statusCode = error.message.includes('not found') ? 404 : 500
    res.status(statusCode).json({
      success: false,
      error: error.message,
    })
  }
})

// POST /api/business-units - Create new business unit (admin only)
app.post('/api/business-units', validateSession, async (req, res) => {
  try {
    const businessUnit = await businessUnitService.createBusinessUnit(req.body)

    res.status(201).json({
      success: true,
      data: businessUnit,
      message: 'Business unit created successfully',
    })
  } catch (error) {
    console.error('Create business unit error:', error)
    const statusCode = error.message.includes('already exists') ? 409 : 400
    res.status(statusCode).json({
      success: false,
      error: error.message,
    })
  }
})

// PUT /api/business-units/:id - Update business unit (admin only)
app.put('/api/business-units/:id', validateSession, async (req, res) => {
  try {
    const { id } = req.params
    const businessUnit = await businessUnitService.updateBusinessUnit(id, req.body)

    res.json({
      success: true,
      data: businessUnit,
      message: 'Business unit updated successfully',
    })
  } catch (error) {
    console.error('Update business unit error:', error)
    const statusCode = error.message.includes('not found') ? 404 : 400
    res.status(statusCode).json({
      success: false,
      error: error.message,
    })
  }
})

// DELETE /api/business-units/:id - Delete business unit (admin only)
app.delete('/api/business-units/:id', validateSession, async (req, res) => {
  try {
    const { id } = req.params
    await businessUnitService.deleteBusinessUnit(id)

    res.json({
      success: true,
      message: 'Business unit deleted successfully',
    })
  } catch (error) {
    console.error('Delete business unit error:', error)
    const statusCode = error.message.includes('not found') ? 404 : 500
    res.status(statusCode).json({
      success: false,
      error: error.message,
    })
  }
})

// Products Management API endpoints

// GET /api/business-units/:id/products - Get products for business unit
app.get('/api/business-units/:id/products', async (req, res) => {
  try {
    const { id } = req.params
    const products = await businessUnitService.getBusinessUnitProducts(id)

    res.json({
      success: true,
      data: products,
    })
  } catch (error) {
    console.error('Get products error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve products',
    })
  }
})

// POST /api/business-units/:id/products - Add product to business unit (admin only)
app.post('/api/business-units/:id/products', validateSession, async (req, res) => {
  try {
    const { id } = req.params
    const product = await businessUnitService.addProduct(id, req.body)

    res.status(201).json({
      success: true,
      data: product,
      message: 'Product added successfully',
    })
  } catch (error) {
    console.error('Add product error:', error)
    res.status(400).json({
      success: false,
      error: error.message,
    })
  }
})

// PUT /api/products/:id - Update individual product (admin only)
app.put('/api/products/:id', validateSession, async (req, res) => {
  try {
    const { id } = req.params
    const product = await businessUnitService.updateProduct(id, req.body)

    res.json({
      success: true,
      data: product,
      message: 'Product updated successfully',
    })
  } catch (error) {
    console.error('Update product error:', error)
    const statusCode = error.message.includes('not found') ? 404 : 400
    res.status(statusCode).json({
      success: false,
      error: error.message,
    })
  }
})

// DELETE /api/products/:id - Remove product (admin only)
app.delete('/api/products/:id', validateSession, async (req, res) => {
  try {
    const { id } = req.params
    await businessUnitService.deleteProduct(id)

    res.json({
      success: true,
      message: 'Product deleted successfully',
    })
  } catch (error) {
    console.error('Delete product error:', error)
    const statusCode = error.message.includes('not found') ? 404 : 500
    res.status(statusCode).json({
      success: false,
      error: error.message,
    })
  }
})

// Statistics Management API endpoints

// PUT /api/business-units/:id/statistics - Update statistics for business unit (admin only)
app.put('/api/business-units/:id/statistics', validateSession, async (req, res) => {
  try {
    const { id } = req.params
    const statistics = await businessUnitService.updateStatistics(id, req.body)

    res.json({
      success: true,
      data: statistics,
      message: 'Statistics updated successfully',
    })
  } catch (error) {
    console.error('Update statistics error:', error)
    res.status(400).json({
      success: false,
      error: error.message,
    })
  }
})

// Gallery Management API endpoints

// GET /api/business-units/:id/gallery - Get gallery images for business unit
app.get('/api/business-units/:id/gallery', async (req, res) => {
  try {
    const { id } = req.params
    const images = await businessUnitService.getBusinessUnitGallery(id)

    res.json({
      success: true,
      data: images,
    })
  } catch (error) {
    console.error('Get gallery images error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve gallery images',
    })
  }
})

// POST /api/business-units/:id/gallery - Add gallery image (admin only)
app.post('/api/business-units/:id/gallery', validateSession, async (req, res) => {
  try {
    const { id } = req.params
    const image = await businessUnitService.addGalleryImage(id, req.body)

    res.status(201).json({
      success: true,
      data: image,
      message: 'Gallery image added successfully',
    })
  } catch (error) {
    console.error('Add gallery image error:', error)
    res.status(400).json({
      success: false,
      error: error.message,
    })
  }
})

// PUT /api/gallery/:id - Update gallery image metadata (admin only)
app.put('/api/gallery/:id', validateSession, async (req, res) => {
  try {
    const { id } = req.params
    const image = await businessUnitService.updateGalleryImage(id, req.body)

    res.json({
      success: true,
      data: image,
      message: 'Gallery image updated successfully',
    })
  } catch (error) {
    console.error('Update gallery image error:', error)
    const statusCode = error.message.includes('not found') ? 404 : 400
    res.status(statusCode).json({
      success: false,
      error: error.message,
    })
  }
})

// DELETE /api/gallery/:id - Delete gallery image (admin only)
app.delete('/api/gallery/:id', validateSession, async (req, res) => {
  try {
    const { id } = req.params
    await businessUnitService.deleteGalleryImage(id)

    res.json({
      success: true,
      message: 'Gallery image deleted successfully',
    })
  } catch (error) {
    console.error('Delete gallery image error:', error)
    const statusCode = error.message.includes('not found') ? 404 : 500
    res.status(statusCode).json({
      success: false,
      error: error.message,
    })
  }
})

// News API endpoints
app.get('/api/news', async (req, res) => {
  try {
    // Future news API implementation
    res.json({ articles: [] })
  } catch (error) {
    console.error('Get news error:', error)
    res.status(500).json({ error: 'Failed to get news articles' })
  }
})

app.post('/api/news', validateSession, async (req, res) => {
  try {
    // Future news creation implementation
    res.json({ success: true })
  } catch (error) {
    console.error('Create news error:', error)
    res.status(500).json({ error: 'Failed to create news article' })
  }
})

// Admin endpoints
app.get('/api/admin/stats', validateSession, (req, res) => {
  try {
    const sessionStats = sessionManager.getSessionStats()
    const dataStats = { adapter: 'sqlite', status: 'connected' }

    res.json({
      sessions: sessionStats,
      data: dataStats,
    })
  } catch (error) {
    console.error('Stats endpoint error:', error)
    res.status(500).json({ error: 'Failed to get statistics' })
  }
})

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() })
})

// Error handling middleware
app.use((error, req, res) => {
  console.error('Server error:', error)
  res.status(500).json({ error: 'Internal server error' })
})

// Start server
app.listen(PORT, () => {
  console.log(`Session management server running on port ${PORT}`)
})

// Graceful shutdown
globalThis.process?.on('SIGINT', () => {
  console.log('Shutting down server...')
  sessionManager.close()
  globalThis.process.exit(0)
})

export default app
