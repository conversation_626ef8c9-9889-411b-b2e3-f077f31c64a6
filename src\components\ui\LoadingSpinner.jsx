/**
 * LoadingSpinner - Reusable loading spinner component
 */

const LoadingSpinner = ({ size = 'md', className = '' }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16',
  }

  return (
    <div
      className={`animate-spin rounded-full border-2 border-zinc-600 border-t-blue-500 ${sizeClasses[size]} ${className}`}
    >
      <span className="sr-only">Loading...</span>
    </div>
  )
}

export default LoadingSpinner
