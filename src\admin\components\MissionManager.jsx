/**
 * MissionManager - Component for managing mission statements with add/remove/reorder functionality
 */

import { useState, useEffect } from 'react'
import Button from '../../components/ui/Button.jsx'

export default function MissionManager({ missions = [], onChange }) {
  const [missionList, setMissionList] = useState([])
  const [errors, setErrors] = useState({})

  // Initialize mission list
  useEffect(() => {
    setMissionList(missions.length > 0 ? missions : [''])
  }, [missions])

  const validateMission = (index, value) => {
    const newErrors = { ...errors }
    const errorKey = `mission_${index}`

    if (!value || value.trim().length === 0) {
      newErrors[errorKey] = 'Mission statement cannot be empty'
    } else if (value.length > 500) {
      newErrors[errorKey] = 'Mission statement must be 500 characters or less'
    } else {
      delete newErrors[errorKey]
    }

    setErrors(newErrors)
    return !newErrors[errorKey]
  }

  const handleMissionChange = (index, value) => {
    const newMissions = [...missionList]
    newMissions[index] = value
    setMissionList(newMissions)

    // Validate the changed mission
    validateMission(index, value)

    // Filter out empty missions for the parent component
    const filteredMissions = newMissions.filter((mission) => mission.trim().length > 0)
    if (onChange) {
      onChange(filteredMissions)
    }
  }

  const addMission = () => {
    const newMissions = [...missionList, '']
    setMissionList(newMissions)
  }

  const removeMission = (index) => {
    if (missionList.length <= 1) return // Keep at least one mission field

    const newMissions = missionList.filter((_, i) => i !== index)
    setMissionList(newMissions)

    // Remove error for this index
    const newErrors = { ...errors }
    delete newErrors[`mission_${index}`]

    // Reindex remaining errors
    const reindexedErrors = {}
    Object.keys(newErrors).forEach((key) => {
      if (key.startsWith('mission_')) {
        const oldIndex = parseInt(key.split('_')[1])
        if (oldIndex > index) {
          reindexedErrors[`mission_${oldIndex - 1}`] = newErrors[key]
        } else if (oldIndex < index) {
          reindexedErrors[key] = newErrors[key]
        }
      } else {
        reindexedErrors[key] = newErrors[key]
      }
    })
    setErrors(reindexedErrors)

    // Filter out empty missions for the parent component
    const filteredMissions = newMissions.filter((mission) => mission.trim().length > 0)
    if (onChange) {
      onChange(filteredMissions)
    }
  }

  const moveMissionUp = (index) => {
    if (index === 0) return

    const newMissions = [...missionList]
    const temp = newMissions[index]
    newMissions[index] = newMissions[index - 1]
    newMissions[index - 1] = temp
    setMissionList(newMissions)

    // Swap errors as well
    const newErrors = { ...errors }
    const currentError = newErrors[`mission_${index}`]
    const previousError = newErrors[`mission_${index - 1}`]

    if (currentError) {
      newErrors[`mission_${index - 1}`] = currentError
      delete newErrors[`mission_${index}`]
    }
    if (previousError) {
      newErrors[`mission_${index}`] = previousError
      delete newErrors[`mission_${index - 1}`]
    }
    setErrors(newErrors)

    // Filter out empty missions for the parent component
    const filteredMissions = newMissions.filter((mission) => mission.trim().length > 0)
    if (onChange) {
      onChange(filteredMissions)
    }
  }

  const moveMissionDown = (index) => {
    if (index === missionList.length - 1) return

    const newMissions = [...missionList]
    const temp = newMissions[index]
    newMissions[index] = newMissions[index + 1]
    newMissions[index + 1] = temp
    setMissionList(newMissions)

    // Swap errors as well
    const newErrors = { ...errors }
    const currentError = newErrors[`mission_${index}`]
    const nextError = newErrors[`mission_${index + 1}`]

    if (currentError) {
      newErrors[`mission_${index + 1}`] = currentError
      delete newErrors[`mission_${index}`]
    }
    if (nextError) {
      newErrors[`mission_${index}`] = nextError
      delete newErrors[`mission_${index + 1}`]
    }
    setErrors(newErrors)

    // Filter out empty missions for the parent component
    const filteredMissions = newMissions.filter((mission) => mission.trim().length > 0)
    if (onChange) {
      onChange(filteredMissions)
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-base font-medium text-white">Mission Statements</h4>
        <Button size="sm" variant="secondary" onClick={addMission}>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          Add Mission
        </Button>
      </div>

      <div className="space-y-3">
        {missionList.map((mission, index) => (
          <div key={index} className="relative">
            <div className="flex items-start gap-2">
              {/* Mission Number */}
              <div className="flex-shrink-0 w-8 h-8 bg-amber-600/20 text-amber-400 rounded-full flex items-center justify-center text-sm font-medium mt-2">
                {index + 1}
              </div>

              {/* Mission Input */}
              <div className="flex-1">
                <textarea
                  value={mission}
                  onChange={(e) => handleMissionChange(index, e.target.value)}
                  placeholder={`Enter mission statement ${index + 1}...`}
                  rows={3}
                  className={`w-full px-3 py-2 bg-zinc-800 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors resize-vertical ${
                    errors[`mission_${index}`]
                      ? 'border-red-500 focus:border-red-400'
                      : 'border-zinc-700 focus:border-amber-500'
                  }`}
                />
                <div className="flex items-center justify-between mt-1">
                  {errors[`mission_${index}`] ? (
                    <p className="text-sm text-red-400">{errors[`mission_${index}`]}</p>
                  ) : (
                    <p className="text-sm text-zinc-500">{mission.length}/500 characters</p>
                  )}
                </div>
              </div>

              {/* Controls */}
              <div className="flex-shrink-0 flex flex-col gap-1 mt-2">
                {/* Move Up */}
                <button
                  type="button"
                  onClick={() => moveMissionUp(index)}
                  disabled={index === 0}
                  className="p-1 text-zinc-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  title="Move up"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 15l7-7 7 7"
                    />
                  </svg>
                </button>

                {/* Move Down */}
                <button
                  type="button"
                  onClick={() => moveMissionDown(index)}
                  disabled={index === missionList.length - 1}
                  className="p-1 text-zinc-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  title="Move down"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </button>

                {/* Remove */}
                <button
                  type="button"
                  onClick={() => removeMission(index)}
                  disabled={missionList.length <= 1}
                  className="p-1 text-red-400 hover:text-red-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  title="Remove mission"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Help Text */}
      <div className="text-sm text-zinc-500">
        <p>• Add multiple mission statements to describe your business unit's goals</p>
        <p>• Use the up/down arrows to reorder missions</p>
        <p>• At least one mission statement is required</p>
      </div>

      {/* Validation Summary */}
      {Object.keys(errors).length > 0 && (
        <div className="p-3 bg-red-600/10 border border-red-600/30 rounded-lg">
          <div className="flex items-start gap-2">
            <svg
              className="w-4 h-4 text-red-400 flex-shrink-0 mt-0.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <div>
              <p className="text-sm font-medium text-red-400 mb-1">Mission validation errors:</p>
              <ul className="text-sm text-red-300 space-y-1">
                {Object.values(errors).map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
