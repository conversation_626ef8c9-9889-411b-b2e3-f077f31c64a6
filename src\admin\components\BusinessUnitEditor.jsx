/**
 * BusinessUnitEditor - Main component for editing business unit data
 */

import { useState, useEffect, useCallback } from 'react'
import Card from '../../components/ui/Card.jsx'
import Button from '../../components/ui/Button.jsx'
import Badge from '../../components/ui/Badge.jsx'
import BasicInfoForm from './BasicInfoForm.jsx'
import AboutSectionForm from './AboutSectionForm.jsx'
import ProductManager from './ProductManager.jsx'
import StatisticsManager from './StatisticsManager.jsx'
import GalleryManager from './GalleryManager.jsx'
import ContactInfoForm from './ContactInfoForm.jsx'
import PreviewModal from './PreviewModal.jsx'
import { useToast } from '../../components/ui/Toast.jsx'
// import businessUnitApiService from '../../services/BusinessUnitApiService.js'
import { BusinessUnitEditorSkeleton } from '../../components/ui/AdminSkeletons.jsx'
import { InlineLoader } from '../../components/ui/LoadingIndicator.jsx'

export default function BusinessUnitEditor({ businessUnit, onSave, onCancel, mode = 'edit' }) {
  const { showToast, ToastContainer } = useToast()
  const [activeTab, setActiveTab] = useState('basic')
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    badgeText: '',
    badgeVariant: 'primary',
    badgeIcon: '',
    themeColor: {
      from: '#3b82f6',
      to: '#1d4ed8',
      primary: '#3b82f6',
    },
    layoutConfig: {
      productsLayout: 'grid',
    },
    aboutImage: '',
    aboutContent: '',
    highlightText: '',
    highlightClass: '',
    vision: '',
    mission: [],
    badges: [],
    products: {
      title: 'Produk Unggulan',
      description: 'Produk berkualitas tinggi dari unit usaha kami',
      items: [],
      cta: { text: 'Lihat Semua Produk' },
    },
    statistics: {
      title: '',
      description: '',
      items: [],
    },
    gallery: {
      title: 'Galeri',
      description: 'Dokumentasi kegiatan dan fasilitas',
      images: [],
    },
    contact: {
      address: '',
      phone: '',
      email: '',
      hours: '',
    },
  })
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [loading, setLoading] = useState(false)
  const [initialLoading, setInitialLoading] = useState(!businessUnit)
  const [error, setError] = useState(null)
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true)
  const [showPreview, setShowPreview] = useState(false)
  const [isPublishing, setIsPublishing] = useState(false)

  // Initialize form data when businessUnit prop changes
  useEffect(() => {
    if (businessUnit) {
      setFormData({
        ...businessUnit,
        themeColor: businessUnit.themeColor || {
          from: '#3b82f6',
          to: '#1d4ed8',
          primary: '#3b82f6',
        },
        layoutConfig: businessUnit.layoutConfig || {
          productsLayout: 'grid',
        },
        products: businessUnit.products || {
          title: 'Produk Unggulan',
          description: 'Produk berkualitas tinggi dari unit usaha kami',
          items: [],
          cta: { text: 'Lihat Semua Produk' },
        },
        statistics: businessUnit.statistics || {
          title: '',
          description: '',
          items: [],
        },
        gallery: businessUnit.gallery || {
          title: 'Galeri',
          description: 'Dokumentasi kegiatan dan fasilitas',
          images: [],
        },
        contact: businessUnit.contact || {
          address: '',
          phone: '',
          email: '',
          hours: '',
        },
      })
      setInitialLoading(false)
    }
  }, [businessUnit])

  // Define handleAutoSave before using it in useEffect
  const handleAutoSave = useCallback(async () => {
    if (!hasUnsavedChanges) return

    try {
      // For now, just log the auto-save action
      // In the future, this will call the actual API
      console.log('Auto-saving business unit data:', formData)
      setHasUnsavedChanges(false)
    } catch (err) {
      console.error('Auto-save failed:', err)
      // Don't show error for auto-save failures to avoid interrupting user
    }
  }, [hasUnsavedChanges, formData])

  // Auto-save functionality
  useEffect(() => {
    if (hasUnsavedChanges && autoSaveEnabled) {
      const autoSaveTimer = setTimeout(() => {
        handleAutoSave()
      }, 30000) // Auto-save after 30 seconds of inactivity

      return () => clearTimeout(autoSaveTimer)
    }
  }, [hasUnsavedChanges, autoSaveEnabled, handleAutoSave])

  // Warn user about unsaved changes before leaving
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (hasUnsavedChanges) {
        e.preventDefault()
        e.returnValue = ''
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [hasUnsavedChanges])

  const tabs = [
    {
      id: 'basic',
      name: 'Basic Info',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
      ),
    },
    {
      id: 'about',
      name: 'About',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          />
        </svg>
      ),
    },
    {
      id: 'products',
      name: 'Products',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
          />
        </svg>
      ),
    },
    {
      id: 'statistics',
      name: 'Statistics',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
          />
        </svg>
      ),
    },
    {
      id: 'gallery',
      name: 'Gallery',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
      ),
    },
    {
      id: 'contact',
      name: 'Contact',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
          />
        </svg>
      ),
    },
  ]

  const handleFormDataChange = (section, data) => {
    setFormData((prev) => ({
      ...prev,
      [section]: data,
    }))
    setHasUnsavedChanges(true)
  }

  const handleSave = async () => {
    setLoading(true)
    setError(null)

    try {
      // Validate required fields
      if (!formData.name || formData.name.trim().length < 3) {
        throw new Error('Business unit name must be at least 3 characters long')
      }

      if (!formData.description || formData.description.trim().length < 10) {
        throw new Error('Description must be at least 10 characters long')
      }

      // Call the onSave callback
      if (onSave) {
        await onSave(formData)
      }

      // Business unit saved successfully
      if (businessUnit?.slug) {
        console.log(`[BusinessUnitEditor] Business unit saved: ${businessUnit.slug}`)
      }

      setHasUnsavedChanges(false)
      showToast('Business unit saved successfully!', 'success')
    } catch (err) {
      console.error('Error saving business unit:', err)
      setError(err.message || 'Failed to save business unit. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    if (hasUnsavedChanges) {
      if (window.confirm('You have unsaved changes. Are you sure you want to cancel?')) {
        if (onCancel) {
          onCancel()
        }
      }
    } else {
      if (onCancel) {
        onCancel()
      }
    }
  }

  const handlePreview = () => {
    setShowPreview(true)
  }

  const handleClosePreview = () => {
    setShowPreview(false)
  }

  const handlePublish = async () => {
    setIsPublishing(true)
    try {
      // Validate data before publishing
      if (!formData.name || formData.name.trim().length < 3) {
        throw new Error('Business unit name must be at least 3 characters long')
      }

      if (!formData.description || formData.description.trim().length < 10) {
        throw new Error('Description must be at least 10 characters long')
      }

      // First save the changes
      if (onSave) {
        await onSave(formData)
      }

      // Then publish (for now, just simulate the publish process)
      // In the future, this will call the actual publish API
      console.log('Publishing business unit:', formData)

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1500))

      // Business unit published successfully
      if (businessUnit?.slug) {
        console.log(`[BusinessUnitEditor] Business unit published: ${businessUnit.slug}`)
      }

      // Close preview and show success message
      setShowPreview(false)
      setHasUnsavedChanges(false)
      showToast(`${formData.name} has been published successfully!`, 'success', 6000)
    } catch (err) {
      console.error('Error publishing business unit:', err)
      const errorMessage = err.message || 'Failed to publish business unit. Please try again.'
      setError(errorMessage)
      showToast(errorMessage, 'error', 8000)
    } finally {
      setIsPublishing(false)
    }
  }

  const handleReturnToEdit = () => {
    setShowPreview(false)
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'basic':
        return (
          <BasicInfoForm
            data={formData}
            onChange={(data) => handleFormDataChange('basic', data)}
            onFieldChange={(field, value) => {
              setFormData((prev) => ({
                ...prev,
                [field]: value,
              }))
              setHasUnsavedChanges(true)
            }}
          />
        )
      case 'about':
        return (
          <AboutSectionForm
            data={formData}
            onFieldChange={(field, value) => {
              setFormData((prev) => ({
                ...prev,
                [field]: value,
              }))
              setHasUnsavedChanges(true)
            }}
          />
        )
      case 'products':
        return (
          <ProductManager
            products={formData.products}
            onChange={(productsData) => {
              setFormData((prev) => ({
                ...prev,
                products: productsData,
              }))
              setHasUnsavedChanges(true)
            }}
          />
        )
      case 'statistics':
        return (
          <StatisticsManager
            statistics={formData.statistics}
            onChange={(statisticsData) => {
              setFormData((prev) => ({
                ...prev,
                statistics: statisticsData,
              }))
              setHasUnsavedChanges(true)
            }}
          />
        )
      case 'gallery':
        return (
          <GalleryManager
            gallery={formData.gallery}
            onChange={(galleryData) => {
              setFormData((prev) => ({
                ...prev,
                gallery: galleryData,
              }))
              setHasUnsavedChanges(true)
            }}
          />
        )
      case 'contact':
        return (
          <ContactInfoForm
            contact={formData.contact}
            onChange={(contactData) => {
              setFormData((prev) => ({
                ...prev,
                contact: contactData,
              }))
              setHasUnsavedChanges(true)
            }}
          />
        )
      default:
        return null
    }
  }

  // Show skeleton loading while initial data is loading
  if (initialLoading) {
    return <BusinessUnitEditorSkeleton />
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">
            {mode === 'create'
              ? 'Create Business Unit'
              : `Edit ${businessUnit?.name || 'Business Unit'}`}
          </h1>
          <div className="flex items-center gap-4 mt-2">
            <p className="text-zinc-400">
              {mode === 'create'
                ? 'Create a new business unit'
                : 'Edit business unit details and content'}
            </p>
            {hasUnsavedChanges && (
              <Badge variant="warning" size="sm">
                Unsaved Changes
              </Badge>
            )}
            {autoSaveEnabled && (
              <Badge variant="info" size="sm">
                Auto-save Enabled
              </Badge>
            )}
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="secondary" onClick={handlePreview}>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
              />
            </svg>
            Preview
          </Button>
          <Button variant="secondary" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave} loading={loading}>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"
              />
            </svg>
            Save Changes
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <Card padding="lg">
          <div className="flex items-center gap-3 text-red-400">
            <svg
              className="w-5 h-5 flex-shrink-0"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <p>{error}</p>
          </div>
        </Card>
      )}

      {/* Tabs Navigation */}
      <Card padding="none">
        <div className="border-b border-zinc-700">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-amber-500 text-amber-400'
                    : 'border-transparent text-zinc-400 hover:text-zinc-300 hover:border-zinc-300'
                }`}
              >
                {tab.icon}
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">{renderTabContent()}</div>
      </Card>

      {/* Auto-save Settings */}
      <Card padding="lg">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-sm font-medium text-white">Auto-save Settings</h3>
            <p className="text-xs text-zinc-400">Automatically save changes every 30 seconds</p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={autoSaveEnabled}
              onChange={(e) => setAutoSaveEnabled(e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-zinc-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-amber-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-amber-600"></div>
          </label>
        </div>
      </Card>

      {/* Preview Modal */}
      <PreviewModal
        isOpen={showPreview}
        onClose={handleClosePreview}
        businessUnitData={formData}
        onPublish={handlePublish}
        onReturnToEdit={handleReturnToEdit}
        isPublishing={isPublishing}
      />

      {/* Toast Notifications */}
      <ToastContainer />
    </div>
  )
}
