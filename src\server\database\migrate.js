/**
 * Data migration script to convert existing static data files to database records
 */

import Database from 'better-sqlite3'
import path from 'path'
import { fileURLToPath } from 'url'
import { v4 as uuidv4 } from 'uuid'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Database path
const DB_PATH = path.join(__dirname, '../../../data/modular_system.db')

/**
 * Note: Static data files have been removed. 
 * This migration script is now deprecated as all data should be managed through the database.
 * Use the admin interface to create and manage business units instead.
 */
async function importStaticData() {
  console.log('⚠️  Static data files have been removed. Migration from files is no longer supported.')
  console.log('📝 Use the admin interface to create and manage business units in the database.')
  return {}
}

/**
 * Migrate business unit data to database
 * Note: This function is now deprecated since static data files have been removed
 */
export async function migrateBusinessUnits() {
  console.log('⚠️  Migration from static files is no longer supported.')
  console.log('📝 Static data files have been removed. Use the admin interface to manage business units.')
  console.log('💡 If you need to restore data, use the database backup or recreate through the admin panel.')
  
  return 0
}

/**
 * Verify migration by counting records
 */
export function verifyMigration() {
  const db = new Database(DB_PATH)

  try {
    const businessUnitsCount = db
      .prepare('SELECT COUNT(*) as count FROM business_units')
      .get().count
    const productsCount = db.prepare('SELECT COUNT(*) as count FROM products').get().count
    const statisticsCount = db.prepare('SELECT COUNT(*) as count FROM statistics').get().count
    const galleryCount = db.prepare('SELECT COUNT(*) as count FROM gallery_images').get().count

    const results = {
      businessUnits: businessUnitsCount,
      products: productsCount,
      statistics: statisticsCount,
      galleryImages: galleryCount,
    }

    console.log('📊 Migration verification results:', results)
    return results
  } catch (error) {
    console.error('❌ Error verifying migration:', error)
    throw error
  } finally {
    db.close()
  }
}

/**
 * Clear all business unit data (for testing purposes)
 */
export function clearBusinessUnitsData() {
  const db = new Database(DB_PATH)

  try {
    db.exec('DELETE FROM gallery_images')
    db.exec('DELETE FROM statistics')
    db.exec('DELETE FROM products')
    db.exec('DELETE FROM business_units')

    console.log('🧹 Cleared all business units data')
  } catch (error) {
    console.error('❌ Error clearing data:', error)
    throw error
  } finally {
    db.close()
  }
}

// Run migration if this file is executed directly
if (typeof process !== 'undefined' && typeof window === 'undefined' && import.meta.url === `file://${process.argv[1]}`) {
  console.log('🚀 Starting business units data migration...')

  migrateBusinessUnits()
    .then(() => verifyMigration())
    .then(() => console.log('✅ Migration completed successfully'))
    .catch((error) => {
      console.error('❌ Migration failed:', error)
      process.exit(1)
    })
}
