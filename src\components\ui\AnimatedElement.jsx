import { forwardRef } from 'react'
import { useIntersectionObserver } from '../../hooks/useIntersectionObserver'

// Helper to merge multiple refs (callback or object refs)
function mergeRefs(...refs) {
  return (value) => {
    refs.forEach((ref) => {
      if (!ref) return
      if (typeof ref === 'function') {
        ref(value)
      } else {
        ref.current = value
      }
    })
  }
}

const AnimatedElement = forwardRef(
  (
    {
      children,
      animation = 'fadeInUp',
      delay = 0,
      duration = 600,
      className = '',
      once = true,
      threshold = 0.1,
      ...props
    },
    ref
  ) => {
    const {
      ref: observerRef,
      hasIntersected,
      isIntersecting,
    } = useIntersectionObserver({
      threshold,
      rootMargin: '50px',
    })

    const shouldAnimate = once ? hasIntersected : isIntersecting

    const animationClasses = {
      fadeInUp: shouldAnimate ? 'animate-fade-in-up' : 'opacity-0 translate-y-8',
      fadeInScale: shouldAnimate ? 'animate-fade-in-scale' : 'opacity-0 scale-95',
      slideInRight: shouldAnimate ? 'animate-slide-in-right' : 'opacity-0 translate-x-8',
      slideInLeft: shouldAnimate ? 'animate-slide-in-left' : 'opacity-0 -translate-x-8',
      fadeIn: shouldAnimate ? 'opacity-100' : 'opacity-0',
    }

    const animationStyle = {
      animationDelay: `${delay}ms`,
      animationDuration: `${duration}ms`,
      animationFillMode: 'both',
    }

    const classes = `transition-all duration-700 ${animationClasses[animation]} ${className}`

    return (
      <div
        ref={mergeRefs(observerRef, ref)}
        className={classes}
        style={shouldAnimate ? animationStyle : {}}
        {...props}
      >
        {children}
      </div>
    )
  }
)

AnimatedElement.displayName = 'AnimatedElement'

export default AnimatedElement
