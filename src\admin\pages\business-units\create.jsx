/**
 * BusinessUnitsCreate - Page for creating new business units
 */

import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import Card from '../../../components/ui/Card.jsx'
import Button from '../../../components/ui/Button.jsx'
import Badge from '../../../components/ui/Badge.jsx'
import BusinessUnitEditor from '../../components/BusinessUnitEditor.jsx'
import businessUnitService from '../../../services/BusinessUnitService.js'
import { useToast } from '../../../components/ui/Toast.jsx'

export default function BusinessUnitsCreate() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const navigate = useNavigate()
  const { showToast, ToastContainer } = useToast()

  const handleSaveBusinessUnit = async (businessUnitData) => {
    setLoading(true)
    setError(null)

    try {
      // Validate required fields
      if (!businessUnitData.name || businessUnitData.name.trim().length < 3) {
        throw new Error('Business unit name must be at least 3 characters long')
      }

      if (!businessUnitData.description || businessUnitData.description.trim().length < 10) {
        throw new Error('Description must be at least 10 characters long')
      }

      // Create new business unit
      const savedBusinessUnit = await businessUnitService.createBusinessUnit(businessUnitData)
      
      showToast('Business unit created successfully', 'success')
      console.log('Business unit created successfully:', savedBusinessUnit.name)

      // Navigate back to the business units list after saving
      navigate('/admin/business-units')
    } catch (err) {
      console.error('Error creating business unit:', err)
      setError(err.message || 'Failed to create business unit. Please try again.')
      showToast('Failed to create business unit', 'error')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    navigate('/admin/business-units')
  }

  return (
    <div className="space-y-6">
      {/* Cancel Button */}
      <div className="flex justify-end">
        <Button variant="secondary" onClick={handleCancel}>
          Cancel
        </Button>
      </div>

      {/* Error Display */}
      {error && (
        <Card padding="lg">
          <div className="flex items-center gap-3 text-red-400">
            <svg
              className="w-5 h-5 flex-shrink-0"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <p>{error}</p>
          </div>
        </Card>
      )}

      {/* Business Unit Editor */}
      <BusinessUnitEditor
        businessUnit={null}
        onSave={handleSaveBusinessUnit}
        onCancel={handleCancel}
        mode="create"
      />

      {/* Toast Notifications */}
      <ToastContainer />
    </div>
  )
}
