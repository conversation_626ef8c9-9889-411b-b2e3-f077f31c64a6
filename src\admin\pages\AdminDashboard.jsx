/**
 * AdminDashboard - Main dashboard for admin interface
 */

import { useState, useEffect, useCallback } from 'react'
import { Link } from 'react-router-dom'
import Card from '../../components/ui/Card.jsx'
import Button from '../../components/ui/Button.jsx'
import Badge from '../../components/ui/Badge.jsx'
import newsService from '../../services/NewsService.js'
import settingsService from '../../services/SettingsService.js'
import businessUnitService from '../../services/BusinessUnitService.js'

export default function AdminDashboard() {
  const [stats, setStats] = useState({})
  const [recentArticles, setRecentArticles] = useState([])
  const [recentBusinessUnits, setRecentBusinessUnits] = useState([])
  const [, setSystemStatus] = useState('loading')
  const [loading, setLoading] = useState(true)

  const loadDashboardData = useCallback(async () => {
    try {
      // Get news statistics
      const newsStats = newsService.getStats()

      // Get recent articles
      const articles = newsService
        .getArticles()
        .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
        .slice(0, 5)

      // Get settings statistics
      const settingsStats = settingsService.getStats()

      // Get business units statistics
      let businessUnitsStats = { total: 0, active: 0, recent: [] }
      let recentUnits = []

      try {
        businessUnitsStats = businessUnitService.getStats()
        recentUnits = businessUnitService
          .getAllBusinessUnits()
          .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
          .slice(0, 3)
      } catch (businessUnitError) {
        console.warn('Business unit service not available:', businessUnitError)
        // Use fallback data for business units
        businessUnitsStats = { total: 7, active: 7, recent: [] }
        recentUnits = [
          {
            id: 'pbcigi',
            name: 'PB Cigi',
            badgeText: 'Perusahaan Besar',
            updatedAt: new Date().toISOString(),
          },
          {
            id: 'ciginet',
            name: 'Cigi Net',
            badgeText: 'Internet Provider',
            updatedAt: new Date().toISOString(),
          },
          {
            id: 'cigimart',
            name: 'Cigi Mart',
            badgeText: 'Retail',
            updatedAt: new Date().toISOString(),
          },
        ]
      }

      setStats({
        news: newsStats,
        settings: settingsStats,
        businessUnits: businessUnitsStats,
        content: {
          totalArticles: newsStats.total,
          publishedArticles: newsStats.published,
          draftArticles: newsStats.draft,
          featuredArticles: newsStats.featured,
        },
      })

      setRecentArticles(articles)
      setRecentBusinessUnits(recentUnits)
      setSystemStatus('healthy')
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
      setSystemStatus('error')
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    loadDashboardData()
  }, [loadDashboardData])

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="w-8 h-8 border-4 border-amber-600 border-t-transparent rounded-full animate-spin"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-white">Dashboard</h1>
        <p className="text-zinc-400">Welcome to your admin dashboard</p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card padding="lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-zinc-400">Total Articles</p>
              <p className="text-2xl font-bold text-white">{stats.content?.totalArticles || 0}</p>
            </div>
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
              <svg
                className="w-5 h-5 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"
                />
              </svg>
            </div>
          </div>
        </Card>

        <Card padding="lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-zinc-400">Published</p>
              <p className="text-2xl font-bold text-green-400">
                {stats.content?.publishedArticles || 0}
              </p>
            </div>
            <div className="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center">
              <svg
                className="w-5 h-5 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
          </div>
        </Card>

        <Card padding="lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-zinc-400">Drafts</p>
              <p className="text-2xl font-bold text-yellow-400">
                {stats.content?.draftArticles || 0}
              </p>
            </div>
            <div className="w-10 h-10 bg-yellow-600 rounded-lg flex items-center justify-center">
              <svg
                className="w-5 h-5 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                />
              </svg>
            </div>
          </div>
        </Card>

        <Card padding="lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-zinc-400">Featured</p>
              <p className="text-2xl font-bold text-amber-400">
                {stats.content?.featuredArticles || 0}
              </p>
            </div>
            <div className="w-10 h-10 bg-amber-600 rounded-lg flex items-center justify-center">
              <svg
                className="w-5 h-5 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                />
              </svg>
            </div>
          </div>
        </Card>

        <Card padding="lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-zinc-400">Business Units</p>
              <p className="text-2xl font-bold text-purple-400">
                {stats.businessUnits?.total || 0}
              </p>
            </div>
            <div className="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center">
              <svg
                className="w-5 h-5 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                />
              </svg>
            </div>
          </div>
        </Card>
      </div>

      {/* Quick Actions and Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card padding="lg">
          <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <Link to="/admin/business-units">
              <Button variant="primary" className="w-full justify-start">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                  />
                </svg>
                Manage Business Units
              </Button>
            </Link>
            <Link to="/admin/news">
              <Button variant="secondary" className="w-full justify-start">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 4v16m8-8H4"
                  />
                </svg>
                Create New Article
              </Button>
            </Link>
            <Link to="/admin/settings">
              <Button variant="secondary" className="w-full justify-start">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
                Manage Settings
              </Button>
            </Link>
            <Button
              variant="secondary"
              className="w-full justify-start"
              onClick={() => window.open('/', '_blank')}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                />
              </svg>
              View Website
            </Button>
          </div>
        </Card>

        <Card padding="lg">
          <h3 className="text-lg font-semibold text-white mb-4">Recent Business Unit Updates</h3>
          <div className="space-y-3">
            {recentBusinessUnits.length === 0 ? (
              <p className="text-zinc-400 text-sm">No recent updates</p>
            ) : (
              recentBusinessUnits.map((unit) => (
                <div
                  key={unit.id}
                  className="flex items-start justify-between p-3 bg-zinc-800 rounded-lg"
                >
                  <div className="flex-1">
                    <h4 className="font-medium text-white text-sm mb-1">{unit.name}</h4>
                    <div className="flex items-center gap-2 text-xs text-zinc-400">
                      <Badge variant="primary" size="sm">
                        {unit.badgeText || 'Business Unit'}
                      </Badge>
                      <span>{formatDate(unit.updatedAt)}</span>
                    </div>
                  </div>
                  <Link to={`/admin/business-units/${unit.id}/edit`}>
                    <Button variant="secondary" size="sm">
                      Edit
                    </Button>
                  </Link>
                </div>
              ))
            )}
          </div>
          <div className="mt-4 pt-3 border-t border-zinc-700">
            <Link to="/admin/business-units">
              <Button variant="ghost" size="sm" className="w-full">
                View All Business Units
              </Button>
            </Link>
          </div>
        </Card>

        <Card padding="lg">
          <h3 className="text-lg font-semibold text-white mb-4">Recent Articles</h3>
          <div className="space-y-3">
            {recentArticles.length === 0 ? (
              <p className="text-zinc-400 text-sm">No articles yet</p>
            ) : (
              recentArticles.map((article) => (
                <div
                  key={article.id}
                  className="flex items-start justify-between p-3 bg-zinc-800 rounded-lg"
                >
                  <div className="flex-1">
                    <h4 className="font-medium text-white text-sm mb-1">{article.title}</h4>
                    <div className="flex items-center gap-2 text-xs text-zinc-400">
                      <Badge
                        variant={article.status === 'published' ? 'success' : 'warning'}
                        size="sm"
                      >
                        {article.status}
                      </Badge>
                      <span>{formatDate(article.updatedAt)}</span>
                    </div>
                  </div>
                  <Link to="/admin/news">
                    <Button variant="secondary" size="sm">
                      Edit
                    </Button>
                  </Link>
                </div>
              ))
            )}
          </div>
        </Card>
      </div>

      {/* System Status */}
      <Card padding="lg">
        <h3 className="text-lg font-semibold text-white mb-4">System Status</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="flex items-center justify-between p-3 bg-zinc-800 rounded-lg">
            <span className="text-white">News System</span>
            <Badge variant="success">Healthy</Badge>
          </div>
          <div className="flex items-center justify-between p-3 bg-zinc-800 rounded-lg">
            <span className="text-white">Business Units</span>
            <Badge variant="success">Healthy</Badge>
          </div>
          <div className="flex items-center justify-between p-3 bg-zinc-800 rounded-lg">
            <span className="text-white">Settings System</span>
            <Badge variant="success">Healthy</Badge>
          </div>
          <div className="flex items-center justify-between p-3 bg-zinc-800 rounded-lg">
            <span className="text-white">Website</span>
            <Badge variant="success">Online</Badge>
          </div>
        </div>
      </Card>
    </div>
  )
}
