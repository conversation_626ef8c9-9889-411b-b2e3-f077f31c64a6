/**
 * SiteSettings - Admin interface for managing site-wide settings
 */

import { useState, useEffect } from 'react'
import Card from '../../components/ui/Card.jsx'
import Button from '../../components/ui/Button.jsx'
import Badge from '../../components/ui/Badge.jsx'
import settingsService from '../../services/SettingsService.js'

export default function SiteSettings() {
  const [settings, setSettings] = useState({})
  const [activeTab, setActiveTab] = useState('site')
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState(null)
  const [stats, setStats] = useState({})

  const tabs = [
    { id: 'site', name: 'Site Configuration', icon: '🌐' },
    { id: 'theme', name: 'Theme & Appearance', icon: '🎨' },
    { id: 'navigation', name: 'Navigation', icon: '🧭' },
    { id: 'footer', name: 'Footer', icon: '📄' },
    { id: 'seo', name: 'SEO Settings', icon: '🔍' },
    { id: 'features', name: 'Features', icon: '⚡' },
    { id: 'performance', name: 'Performance', icon: '🚀' },
    { id: 'security', name: 'Security', icon: '🔒' },
  ]

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = () => {
    setLoading(true)
    try {
      const settingsData = settingsService.getSettings()
      const statsData = settingsService.getStats()
      setSettings(settingsData || {})
      setStats(statsData)
    } catch (error) {
      console.error('Error loading settings:', error)
      setMessage({ type: 'error', text: 'Failed to load settings' })
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async (category, categoryData) => {
    setSaving(true)
    try {
      settingsService.updateSettingCategory(category, categoryData)
      setSettings((prev) => ({
        ...prev,
        [category]: { ...prev[category], ...categoryData },
      }))
      setMessage({ type: 'success', text: 'Settings saved successfully!' })
      setTimeout(() => setMessage(null), 3000)
    } catch (error) {
      console.error('Error saving settings:', error)
      setMessage({ type: 'error', text: 'Failed to save settings' })
    } finally {
      setSaving(false)
    }
  }

  const handleReset = async (category) => {
    if (window.confirm(`Are you sure you want to reset ${category} settings to default?`)) {
      try {
        const resetData = settingsService.resetSettingCategory(category)
        setSettings((prev) => ({ ...prev, [category]: resetData }))
        setMessage({ type: 'success', text: `${category} settings reset to default` })
        setTimeout(() => setMessage(null), 3000)
      } catch (error) {
        console.error('Error resetting settings:', error)
        setMessage({ type: 'error', text: 'Failed to reset settings' })
      }
    }
  }

  const handleExport = () => {
    try {
      settingsService.exportSettings()
      setMessage({ type: 'success', text: 'Settings exported successfully!' })
      setTimeout(() => setMessage(null), 3000)
    } catch (error) {
      console.error('Error exporting settings:', error)
      setMessage({ type: 'error', text: 'Failed to export settings' })
    }
  }

  const handleImport = async (event) => {
    const file = event.target.files[0]
    if (!file) return

    try {
      const importedSettings = await settingsService.importSettings(file)
      setSettings(importedSettings)
      setMessage({ type: 'success', text: 'Settings imported successfully!' })
      setTimeout(() => setMessage(null), 3000)
    } catch (error) {
      console.error('Error importing settings:', error)
      setMessage({ type: 'error', text: 'Failed to import settings: ' + error.message })
    }

    // Reset file input
    event.target.value = ''
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="w-8 h-8 border-4 border-amber-600 border-t-transparent rounded-full animate-spin"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Site Settings</h1>
          <p className="text-zinc-400">Configure your website settings and preferences</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="secondary" onClick={handleExport}>
            Export Settings
          </Button>
          <label className="cursor-pointer">
            <Button variant="secondary" as="span">
              Import Settings
            </Button>
            <input type="file" accept=".json" onChange={handleImport} className="hidden" />
          </label>
        </div>
      </div>

      {/* Message */}
      {message && (
        <div
          className={`p-4 rounded-lg ${
            message.type === 'success'
              ? 'bg-green-900/50 text-green-400 border border-green-700'
              : 'bg-red-900/50 text-red-400 border border-red-700'
          }`}
        >
          {message.text}
        </div>
      )}

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card padding="lg">
          <div className="text-center">
            <div className="text-2xl font-bold text-white">{stats.categoriesCount || 0}</div>
            <div className="text-sm text-zinc-400">Setting Categories</div>
          </div>
        </Card>
        <Card padding="lg">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-400">{stats.featuresEnabled || 0}</div>
            <div className="text-sm text-zinc-400">Features Enabled</div>
          </div>
        </Card>
        <Card padding="lg">
          <div className="text-center">
            <div className="text-2xl font-bold text-amber-400">{stats.version || 'N/A'}</div>
            <div className="text-sm text-zinc-400">Settings Version</div>
          </div>
        </Card>
        <Card padding="lg">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400">
              {stats.lastUpdated ? new Date(stats.lastUpdated).toLocaleDateString() : 'N/A'}
            </div>
            <div className="text-sm text-zinc-400">Last Updated</div>
          </div>
        </Card>
      </div>

      {/* Settings Interface */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Tabs */}
        <div className="lg:col-span-1">
          <Card padding="lg">
            <h3 className="text-lg font-semibold text-white mb-4">Settings Categories</h3>
            <div className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors ${
                    activeTab === tab.id
                      ? 'bg-amber-600 text-white'
                      : 'text-zinc-300 hover:text-white hover:bg-zinc-800'
                  }`}
                >
                  <span className="text-lg">{tab.icon}</span>
                  <span className="font-medium">{tab.name}</span>
                </button>
              ))}
            </div>
          </Card>
        </div>

        {/* Settings Content */}
        <div className="lg:col-span-3">
          <Card padding="lg">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-white">
                {tabs.find((tab) => tab.id === activeTab)?.name}
              </h3>
              <Button variant="secondary" size="sm" onClick={() => handleReset(activeTab)}>
                Reset to Default
              </Button>
            </div>

            {/* Dynamic Settings Form */}
            <SettingsForm
              category={activeTab}
              settings={settings[activeTab] || {}}
              onSave={(data) => handleSave(activeTab, data)}
              saving={saving}
            />
          </Card>
        </div>
      </div>
    </div>
  )
}

// Settings Form Component
function SettingsForm({ settings, onSave, saving }) {
  const [formData, setFormData] = useState(settings)

  useEffect(() => {
    setFormData(settings)
  }, [settings])

  const handleSubmit = (e) => {
    e.preventDefault()
    onSave(formData)
  }

  const handleChange = (key, value) => {
    setFormData((prev) => ({ ...prev, [key]: value }))
  }

  const handleNestedChange = (parentKey, childKey, value) => {
    setFormData((prev) => ({
      ...prev,
      [parentKey]: { ...prev[parentKey], [childKey]: value },
    }))
  }

  const renderField = (key, value, label) => {
    if (typeof value === 'boolean') {
      return (
        <div key={key} className="flex items-center justify-between">
          <label className="text-sm font-medium text-white">{label || key}</label>
          <input
            type="checkbox"
            checked={value}
            onChange={(e) => handleChange(key, e.target.checked)}
            className="w-4 h-4 text-amber-600 bg-zinc-800 border-zinc-700 rounded focus:ring-amber-500"
          />
        </div>
      )
    }

    if (typeof value === 'string') {
      return (
        <div key={key}>
          <label className="block text-sm font-medium text-white mb-2">{label || key}</label>
          <input
            type="text"
            value={value}
            onChange={(e) => handleChange(key, e.target.value)}
            className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:border-amber-500"
          />
        </div>
      )
    }

    if (typeof value === 'number') {
      return (
        <div key={key}>
          <label className="block text-sm font-medium text-white mb-2">{label || key}</label>
          <input
            type="number"
            value={value}
            onChange={(e) => handleChange(key, parseInt(e.target.value) || 0)}
            className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:border-amber-500"
          />
        </div>
      )
    }

    if (typeof value === 'object' && value !== null) {
      return (
        <div key={key} className="space-y-4">
          <h4 className="text-md font-semibold text-white border-b border-zinc-700 pb-2">
            {label || key}
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pl-4">
            {Object.entries(value).map(([childKey, childValue]) =>
              renderNestedField(key, childKey, childValue)
            )}
          </div>
        </div>
      )
    }

    return null
  }

  const renderNestedField = (parentKey, key, value) => {
    if (typeof value === 'boolean') {
      return (
        <div key={key} className="flex items-center justify-between">
          <label className="text-sm font-medium text-white">{key}</label>
          <input
            type="checkbox"
            checked={value}
            onChange={(e) => handleNestedChange(parentKey, key, e.target.checked)}
            className="w-4 h-4 text-amber-600 bg-zinc-800 border-zinc-700 rounded focus:ring-amber-500"
          />
        </div>
      )
    }

    return (
      <div key={key}>
        <label className="block text-sm font-medium text-white mb-2">{key}</label>
        <input
          type="text"
          value={value}
          onChange={(e) => handleNestedChange(parentKey, key, e.target.value)}
          className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:border-amber-500"
        />
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-6">
        {Object.entries(formData).map(([key, value]) =>
          renderField(key, value, key.charAt(0).toUpperCase() + key.slice(1))
        )}
      </div>

      <div className="flex justify-end pt-6 border-t border-zinc-700">
        <Button type="submit" loading={saving}>
          Save Settings
        </Button>
      </div>
    </form>
  )
}
