/**
 * MySQL Database Configuration and Connection Pool Setup
 * Provides connection management for business units data
 */

import mysql from 'mysql2/promise'
import { fileURLToPath } from 'url'
import { dirname } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Database configuration
const dbConfig = {
  host: import.meta.env?.VITE_DB_HOST || 'localhost',
  port: import.meta.env?.VITE_DB_PORT || 3306,
  user: import.meta.env?.VITE_DB_USER || 'root',
  password: import.meta.env?.VITE_DB_PASSWORD || '',
  database: import.meta.env?.VITE_DB_NAME || 'cigi_global',
  charset: 'utf8mb4',
  timezone: '+00:00',
  // Enable JSON support
  supportBigNumbers: true,
  bigNumberStrings: true,
  dateStrings: false,
}

// Create connection pool
const pool = mysql.createPool({
  ...dbConfig,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  // Enable multiple statements for migrations
  multipleStatements: true,
})

/**
 * Get a connection from the pool
 */
export async function getConnection() {
  try {
    const connection = await pool.getConnection()
    return connection
  } catch (error) {
    console.error('Failed to get database connection:', error)
    throw error
  }
}

/**
 * Execute a query with automatic connection management
 */
export async function executeQuery(sql, params = []) {
  const connection = await getConnection()
  try {
    const [results] = await connection.execute(sql, params)
    return results
  } catch (error) {
    console.error('Query execution failed:', error)
    throw error
  } finally {
    connection.release()
  }
}

/**
 * Execute multiple queries in a transaction
 */
export async function executeTransaction(queries) {
  const connection = await getConnection()
  try {
    await connection.beginTransaction()

    const results = []
    for (const { sql, params = [] } of queries) {
      const [result] = await connection.execute(sql, params)
      results.push(result)
    }

    await connection.commit()
    return results
  } catch (error) {
    await connection.rollback()
    console.error('Transaction failed:', error)
    throw error
  } finally {
    connection.release()
  }
}

/**
 * Test database connection
 */
export async function testConnection() {
  try {
    const connection = await getConnection()
    await connection.ping()
    connection.release()
    console.log('✅ MySQL database connection successful')
    return true
  } catch (error) {
    console.error('❌ MySQL database connection failed:', error)
    return false
  }
}

/**
 * Create database if it doesn't exist
 */
export async function createDatabase() {
  try {
    // Connect without specifying database
    const tempConfig = { ...dbConfig }
    delete tempConfig.database

    const tempConnection = await mysql.createConnection(tempConfig)

    // Create database if it doesn't exist
    await tempConnection.execute(
      `CREATE DATABASE IF NOT EXISTS \`${dbConfig.database}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`
    )

    await tempConnection.end()
    console.log(`✅ Database '${dbConfig.database}' created or already exists`)
    return true
  } catch (error) {
    console.error('❌ Failed to create database:', error)
    throw error
  }
}

/**
 * Close all connections in the pool
 */
export async function closePool() {
  try {
    await pool.end()
    console.log('✅ Database connection pool closed')
  } catch (error) {
    console.error('❌ Failed to close connection pool:', error)
    throw error
  }
}

// Export the pool for direct access if needed
export { pool }

// Export configuration for reference
export { dbConfig }
