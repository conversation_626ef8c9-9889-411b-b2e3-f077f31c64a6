/**
 * MySQL Database utilities for business units management
 * Combines initialization, migration, and utility functions
 */

import {
  initializeBusinessUnitsSchema,
  verifySchema,
  dropAllTables,
  getTableStats,
} from './mysql-init.js'
import { migrateBusinessUnits, verifyMigration, clearBusinessUnitsData } from './mysql-migrate.js'
import { testConnection, createDatabase, closePool } from './mysql-config.js'

/**
 * Complete MySQL database setup: initialize schema and migrate data
 */
export async function setupBusinessUnitsDatabase() {
  try {
    console.log('🚀 Setting up MySQL business units database...')

    // Step 1: Test connection and create database if needed
    console.log('🔌 Testing database connection...')
    const connectionOk = await testConnection()
    if (!connectionOk) {
      console.log('📋 Creating database...')
      await createDatabase()
    }

    // Step 2: Initialize schema
    console.log('📋 Initializing database schema...')
    await initializeBusinessUnitsSchema()

    // Step 3: Verify schema
    console.log('🔍 Verifying schema...')
    const schemaResults = await verifySchema()

    // Check if all required tables exist
    const requiredTables = ['business_units', 'products', 'statistics', 'gallery_images']
    const missingTables = requiredTables.filter((table) => !schemaResults[table])

    if (missingTables.length > 0) {
      throw new Error(`Missing required tables: ${missingTables.join(', ')}`)
    }

    // Step 4: Migrate data
    console.log('📦 Migrating static data to database...')
    const migratedCount = await migrateBusinessUnits()

    // Step 5: Verify migration
    console.log('✅ Verifying migration...')
    const migrationResults = await verifyMigration()

    console.log('🎉 MySQL business units database setup completed successfully!')
    console.log(`   - Migrated ${migratedCount} business units`)
    console.log(`   - Total records: ${JSON.stringify(migrationResults, null, 2)}`)

    return {
      success: true,
      migratedCount,
      records: migrationResults,
    }
  } catch (error) {
    console.error('❌ MySQL database setup failed:', error)
    throw error
  }
}

/**
 * Reset MySQL database: clear data and re-migrate
 */
export async function resetBusinessUnitsDatabase() {
  try {
    console.log('🔄 Resetting MySQL business units database...')

    // Clear existing data
    await clearBusinessUnitsData()

    // Re-migrate data
    const migratedCount = await migrateBusinessUnits()

    // Verify
    const results = await verifyMigration()

    console.log('✅ MySQL database reset completed successfully!')
    return {
      success: true,
      migratedCount,
      records: results,
    }
  } catch (error) {
    console.error('❌ MySQL database reset failed:', error)
    throw error
  }
}

// Export individual functions for flexibility
export {
  initializeBusinessUnitsSchema,
  verifySchema,
  migrateBusinessUnits,
  verifyMigration,
  clearBusinessUnitsData,
  dropAllTables,
  getTableStats,
  testConnection,
  createDatabase,
  closePool,
}

// CLI interface - only run in Node.js environment
if (typeof process !== 'undefined' && typeof window === 'undefined' && import.meta.url === `file://${process.argv[1]}`) {
  const command = process.argv[2]

  switch (command) {
    case 'setup':
      setupBusinessUnitsDatabase()
        .then(() => process.exit(0))
        .catch(() => process.exit(1))
      break

    case 'reset':
      resetBusinessUnitsDatabase()
        .then(() => process.exit(0))
        .catch(() => process.exit(1))
      break

    case 'init':
      initializeBusinessUnitsSchema()
        .then(() => verifySchema())
        .then(() => process.exit(0))
        .catch(() => process.exit(1))
      break

    case 'migrate':
      migrateBusinessUnits()
        .then(() => verifyMigration())
        .then(() => process.exit(0))
        .catch(() => process.exit(1))
      break

    case 'verify':
      verifySchema()
        .then(() => verifyMigration())
        .then(() => process.exit(0))
        .catch(() => process.exit(1))
      break

    case 'test':
      testConnection()
        .then(() => process.exit(0))
        .catch(() => process.exit(1))
      break

    case 'drop':
      dropAllTables()
        .then(() => process.exit(0))
        .catch(() => process.exit(1))
      break

    default:
      console.log(`
Usage: node src/server/database/index.js <command>

Commands:
  setup   - Complete setup (init + migrate)
  reset   - Clear data and re-migrate
  init    - Initialize schema only
  migrate - Migrate data only
  verify  - Verify schema and data
  test    - Test database connection
  drop    - Drop all tables
      `)
      break
  }
}
