/**
 * Test script for MySQL database setup
 * Run this to verify the database configuration works
 */

import {
  setupBusinessUnitsDatabase,
  testConnection,
  closePool,
} from './src/server/database/index.js'

async function testSetup() {
  try {
    console.log('🧪 Testing MySQL database setup...')

    // Test connection first
    console.log('1. Testing database connection...')
    const connectionOk = await testConnection()

    if (!connectionOk) {
      console.log('❌ Database connection failed. Please check your MySQL configuration.')
      console.log(
        'Make sure MySQL is running and create a .env file with your database credentials.'
      )
      return
    }

    // Run full setup
    console.log('2. Running full database setup...')
    const result = await setupBusinessUnitsDatabase()

    if (result.success) {
      console.log('✅ Database setup test completed successfully!')
      console.log(`   - Migrated ${result.migratedCount} business units`)
      console.log(`   - Records: ${JSON.stringify(result.records, null, 2)}`)
    } else {
      console.log('❌ Database setup test failed')
    }
  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    // Close database connections
    await closePool()
    if (typeof process !== 'undefined' && typeof window === 'undefined') {
      process.exit(0)
    }
  }
}

// Run the test
testSetup()
