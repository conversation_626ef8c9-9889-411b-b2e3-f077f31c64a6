/**
 * SettingsService - Global site settings management
 * Handles site configuration, theme settings, and user preferences
 */

class SettingsService {
  constructor() {
    this.storageKey = 'cigi_site_settings'
    this.init()
  }

  init() {
    // Initialize with default settings if none exist
    if (!this.getSettings()) {
      this.initializeDefaultSettings()
    }
  }

  initializeDefaultSettings() {
    const defaultSettings = {
      // Site Configuration
      site: {
        title: 'Cigi Global',
        description: 'Transforming businesses through innovative technology solutions',
        tagline: 'Innovation • Technology • Excellence',
        logo: '/logo.png',
        favicon: '/favicon.ico',
        language: 'id',
        timezone: 'Asia/Jakarta',
        contactEmail: '<EMAIL>',
        contactPhone: '+62 ************',
        address: 'Jakarta, Indonesia',
        socialMedia: {
          facebook: '',
          twitter: '',
          instagram: '',
          linkedin: '',
          youtube: '',
        },
      },

      // Theme Settings
      theme: {
        primaryColor: '#F59E0B', // Amber
        secondaryColor: '#3B82F6', // Blue
        accentColor: '#10B981', // Green
        backgroundColor: '#000000', // Black
        textColor: '#FFFFFF', // White
        darkMode: true,
        fontFamily: 'Inter, system-ui, sans-serif',
        borderRadius: '8px',
      },

      // Navigation Settings
      navigation: {
        showLogo: true,
        showSearch: false,
        stickyHeader: true,
        mobileMenuStyle: 'slide', // slide, overlay, push
        maxMenuItems: 6,
      },

      // Footer Settings
      footer: {
        showSocialLinks: true,
        showNewsletter: true,
        showSitemap: true,
        copyrightText: '© 2024 Cigi Global. All rights reserved.',
        showBackToTop: true,
      },

      // SEO Settings
      seo: {
        metaTitle: 'Cigi Global - Innovation Technology Excellence',
        metaDescription:
          'Cigi Global provides innovative technology solutions for businesses across various industries including agriculture, digital solutions, marketplace, and more.',
        metaKeywords:
          'technology, innovation, digital solutions, agriculture, marketplace, business',
        ogImage: '/og-image.jpg',
        twitterCard: 'summary_large_image',
        googleAnalyticsId: '',
        googleTagManagerId: '',
        facebookPixelId: '',
      },

      // Performance Settings
      performance: {
        enableLazyLoading: true,
        enableImageOptimization: true,
        enableCaching: true,
        compressionLevel: 'medium', // low, medium, high
        enableServiceWorker: false,
      },

      // Security Settings
      security: {
        enableCSP: false,
        enableHTTPS: true,
        sessionTimeout: 3600, // seconds
        maxLoginAttempts: 5,
        enableTwoFactor: false,
      },

      // Feature Flags
      features: {
        enableNews: true,
        enableBlog: false,
        enableEcommerce: false,
        enableMultiLanguage: false,
        enableUserRegistration: false,
        enableComments: false,
        enableRatings: false,
        enableSearch: true,
      },

      // Email Settings
      email: {
        smtpHost: '',
        smtpPort: 587,
        smtpUser: '',
        smtpPassword: '',
        fromEmail: '<EMAIL>',
        fromName: 'Cigi Global',
        enableNewsletter: true,
        newsletterProvider: 'internal', // internal, mailchimp, sendgrid
      },

      // Backup Settings
      backup: {
        enableAutoBackup: false,
        backupFrequency: 'daily', // daily, weekly, monthly
        retentionDays: 30,
        backupLocation: 'local', // local, cloud
      },

      // Last updated timestamp
      lastUpdated: new Date().toISOString(),
      version: '1.0.0',
    }

    this.saveSettings(defaultSettings)
  }

  // Get all settings
  getSettings() {
    try {
      const settings = localStorage.getItem(this.storageKey)
      return settings ? JSON.parse(settings) : null
    } catch (error) {
      console.error('Error loading settings:', error)
      return null
    }
  }

  // Get specific setting category
  getSettingCategory(category) {
    const settings = this.getSettings()
    return settings ? settings[category] : null
  }

  // Get specific setting value
  getSetting(category, key) {
    const categorySettings = this.getSettingCategory(category)
    return categorySettings ? categorySettings[key] : null
  }

  // Update entire settings object
  updateSettings(newSettings) {
    try {
      const currentSettings = this.getSettings() || {}
      const updatedSettings = {
        ...currentSettings,
        ...newSettings,
        lastUpdated: new Date().toISOString(),
      }
      this.saveSettings(updatedSettings)
      return updatedSettings
    } catch (error) {
      console.error('Error updating settings:', error)
      throw error
    }
  }

  // Update specific setting category
  updateSettingCategory(category, categorySettings) {
    try {
      const currentSettings = this.getSettings() || {}
      const updatedSettings = {
        ...currentSettings,
        [category]: {
          ...currentSettings[category],
          ...categorySettings,
        },
        lastUpdated: new Date().toISOString(),
      }
      this.saveSettings(updatedSettings)
      return updatedSettings[category]
    } catch (error) {
      console.error('Error updating setting category:', error)
      throw error
    }
  }

  // Update specific setting value
  updateSetting(category, key, value) {
    try {
      const currentSettings = this.getSettings() || {}
      const updatedSettings = {
        ...currentSettings,
        [category]: {
          ...currentSettings[category],
          [key]: value,
        },
        lastUpdated: new Date().toISOString(),
      }
      this.saveSettings(updatedSettings)
      return value
    } catch (error) {
      console.error('Error updating setting:', error)
      throw error
    }
  }

  // Reset settings to default
  resetSettings() {
    try {
      localStorage.removeItem(this.storageKey)
      this.initializeDefaultSettings()
      return this.getSettings()
    } catch (error) {
      console.error('Error resetting settings:', error)
      throw error
    }
  }

  // Reset specific category to default
  resetSettingCategory(category) {
    try {
      // Re-initialize to get defaults
      const tempKey = this.storageKey + '_temp'
      const originalKey = this.storageKey
      this.storageKey = tempKey
      this.initializeDefaultSettings()
      const defaultSettings = this.getSettings()
      localStorage.removeItem(tempKey)
      this.storageKey = originalKey

      // Update with default category
      if (defaultSettings && defaultSettings[category]) {
        return this.updateSettingCategory(category, defaultSettings[category])
      }
      throw new Error(`Category ${category} not found in defaults`)
    } catch (error) {
      console.error('Error resetting setting category:', error)
      throw error
    }
  }

  // Export settings
  exportSettings() {
    try {
      const settings = this.getSettings()
      const dataStr = JSON.stringify(settings, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)

      const link = document.createElement('a')
      link.href = url
      link.download = `cigi-settings-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      return true
    } catch (error) {
      console.error('Error exporting settings:', error)
      throw error
    }
  }

  // Import settings
  importSettings(file) {
    return new Promise((resolve, reject) => {
      try {
        const reader = new FileReader()
        reader.onload = (e) => {
          try {
            const importedSettings = JSON.parse(e.target.result)

            // Validate imported settings structure
            if (this.validateSettings(importedSettings)) {
              this.saveSettings(importedSettings)
              resolve(importedSettings)
            } else {
              reject(new Error('Invalid settings file format'))
            }
          } catch {
            reject(new Error('Invalid JSON file'))
          }
        }
        reader.onerror = () => reject(new Error('Error reading file'))
        reader.readAsText(file)
      } catch (error) {
        reject(error)
      }
    })
  }

  // Validate settings structure
  validateSettings(settings) {
    const requiredCategories = ['site', 'theme', 'navigation', 'footer', 'seo']
    return requiredCategories.every((category) =>
      Object.prototype.hasOwnProperty.call(settings, category)
    )
  }

  // Save settings to localStorage
  saveSettings(settings) {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(settings))
    } catch (error) {
      console.error('Error saving settings:', error)
      throw error
    }
  }

  // Get settings statistics
  getStats() {
    const settings = this.getSettings()
    if (!settings) return {}

    return {
      lastUpdated: settings.lastUpdated,
      version: settings.version,
      categoriesCount: Object.keys(settings).length - 2, // Exclude lastUpdated and version
      featuresEnabled: Object.values(settings.features || {}).filter(Boolean).length,
      totalFeatures: Object.keys(settings.features || {}).length,
    }
  }
}

// Create and export singleton instance
const settingsService = new SettingsService()
export default settingsService
