/**
 * Verification script for MySQL database setup
 * Verifies the setup without requiring an actual MySQL connection
 */

import { readFileSync } from 'fs'
import { fileURLToPath } from 'url'
import { dirname } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

console.log('🔍 Verifying MySQL database setup...')

// Check if all required files exist
const requiredFiles = [
  'src/server/database/mysql-config.js',
  'src/server/database/mysql-init.js',
  'src/server/database/mysql-migrate.js',
  'src/server/database/index.js',
  '.env.example',
]

let allFilesExist = true

for (const file of requiredFiles) {
  try {
    readFileSync(file)
    console.log(`✅ ${file} exists`)
  } catch {
    console.log(`❌ ${file} missing`)
    allFilesExist = false
  }
}

// Check package.json for mysql2 dependency
try {
  const packageJson = JSON.parse(readFileSync('package.json', 'utf8'))
  if (packageJson.dependencies && packageJson.dependencies.mysql2) {
    console.log('✅ mysql2 dependency installed')
  } else {
    console.log('❌ mysql2 dependency missing')
    allFilesExist = false
  }
} catch {
  console.log('❌ Could not read package.json')
  allFilesExist = false
}

// Check if database scripts are in package.json
try {
  const packageJson = JSON.parse(readFileSync('package.json', 'utf8'))
  const requiredScripts = ['db:setup', 'db:test', 'db:init', 'db:migrate', 'db:verify']

  for (const script of requiredScripts) {
    if (packageJson.scripts && packageJson.scripts[script]) {
      console.log(`✅ npm script '${script}' configured`)
    } else {
      console.log(`❌ npm script '${script}' missing`)
      allFilesExist = false
    }
  }
} catch {
  console.log('❌ Could not verify npm scripts')
  allFilesExist = false
}

// Test import of main modules
try {
  console.log('🧪 Testing module imports...')

  // Test mysql-config import
  await import('./src/server/database/mysql-config.js')
  console.log('✅ mysql-config.js imports successfully')

  // Test mysql-init import
  await import('./src/server/database/mysql-init.js')
  console.log('✅ mysql-init.js imports successfully')

  // Test mysql-migrate import
  await import('./src/server/database/mysql-migrate.js')
  console.log('✅ mysql-migrate.js imports successfully')

  // Test main index import
  await import('./src/server/database/index.js')
  console.log('✅ index.js imports successfully')
} catch (error) {
  console.log('❌ Module import failed:', error.message)
  allFilesExist = false
}

// Check if sample data files exist
console.log('📋 Checking sample data files...')
const sampleDataFiles = ['src/Pages/Detailusaha/cigifarm/data.js']

for (const file of sampleDataFiles) {
  try {
    readFileSync(file)
    console.log(`✅ Sample data file ${file} exists`)
  } catch {
    console.log(`⚠️ Sample data file ${file} missing (migration will use placeholder data)`)
  }
}

// Final result
if (allFilesExist) {
  console.log('\n🎉 MySQL database setup verification completed successfully!')
  console.log('\nNext steps:')
  console.log('1. Install and start MySQL server')
  console.log('2. Create a .env file with your database credentials (see .env.example)')
  console.log('3. Run: npm run db:setup')
  console.log('4. Verify with: npm run db:verify')
} else {
  console.log('\n❌ MySQL database setup verification failed!')
  console.log('Please fix the missing files/configurations above.')
}

console.log('\n📚 Available commands:')
console.log('  npm run db:test    - Test database connection')
console.log('  npm run db:setup   - Complete setup (init + migrate)')
console.log('  npm run db:init    - Initialize schema only')
console.log('  npm run db:migrate - Migrate data only')
console.log('  npm run db:verify  - Verify schema and data')
console.log('  npm run db:reset   - Reset database')
console.log('  npm run test:mysql - Run comprehensive test')
