@import 'tailwindcss';
@import './styles/design-system.css';

@layer base {
  body {
    font-family: 'Inter', 'Poppins', sans-serif;
    background-color: #0a0a0a;
    color: #fafafa;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  * {
    box-sizing: border-box;
  }

  html {
    scroll-behavior: smooth;
  }

  /* Modern focus styles */
  *:focus-visible {
    outline: 2px solid #f59e0b;
    outline-offset: 2px;
    border-radius: 4px;
  }

  /* Selection styles */
  ::selection {
    background-color: #f59e0b;
    color: #000;
  }

  /* Improved text rendering */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    text-rendering: optimizeLegibility;
    font-weight: 700;
    line-height: 1.2;
  }

  p {
    text-rendering: optimizeLegibility;
  }
}
