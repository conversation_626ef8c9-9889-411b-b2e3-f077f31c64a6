#!/usr/bin/env node

/**
 * Development helper script for Cigi Global project
 * Provides useful commands for development workflow
 * Converted to ESM (project uses type: module)
 */

import { execSync } from 'node:child_process'
import fs from 'node:fs'
import process from 'node:process'

const commands = {
  dev: () => {
    console.log('🚀 Starting development server...')
    execSync('npm run dev', { stdio: 'inherit' })
  },

  build: () => {
    console.log('🏗️  Building for production...')
    execSync('npm run build', { stdio: 'inherit' })
  },

  preview: () => {
    console.log('👀 Starting preview server...')
    execSync('npm run preview', { stdio: 'inherit' })
  },

  lint: () => {
    console.log('🔍 Running linter...')
    execSync('npm run lint', { stdio: 'inherit' })
  },

  clean: () => {
    console.log('🧹 Cleaning build artifacts...')
    if (fs.existsSync('dist')) {
      fs.rmSync('dist', { recursive: true, force: true })
      console.log('✅ Cleaned dist directory')
    }
    if (fs.existsSync('node_modules/.vite')) {
      fs.rmSync('node_modules/.vite', { recursive: true, force: true })
      console.log('✅ Cleaned Vite cache')
    }
  },

  help: () => {
    console.log(`
🎯 Cigi Global Development Helper

Available commands:
  dev      - Start development server
  build    - Build for production
  preview  - Preview production build
  lint     - Run ESLint
  clean    - Clean build artifacts and cache
  help     - Show this help message

Usage: node scripts/dev.js <command>
    `)
  },
}

const command = process.argv[2]

if (!command || !commands[command]) {
  commands.help()
  process.exit(1)
}

try {
  commands[command]()
} catch (error) {
  console.error('❌ Command failed:', error.message)
  process.exit(1)
}
