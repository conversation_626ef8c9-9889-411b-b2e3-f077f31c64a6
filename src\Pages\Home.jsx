import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import DataImage from '../data'
import { listProyek, listBusinessDivisions } from '../data'
import Button from '../components/ui/Button'
import Card from '../components/ui/Card'
import Badge from '../components/ui/Badge'
import Section from '../components/ui/Section'
import AnimatedElement from '../components/ui/AnimatedElement'
import NewsList from '../components/news/NewsList'

function Home() {
  const [scrollY, setScrollY] = useState(0)

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])
  return (
    <>
      {/* Background Elements */}
      <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
        {/* Animated gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-zinc-900 via-black to-zinc-900"></div>

        {/* Scroll-Following Spinning Globe Background */}
        <div
          className="absolute inset-0 flex items-center justify-center"
          style={{
            transform: `translateY(${scrollY * 0.3}px)`,
            transition: 'transform 0.1s ease-out',
          }}
        >
          <div className="relative">
            {/* Main Globe */}
            <img
              src="/globe.svg"
              alt="Globe Background"
              className="w-[1000px] h-[1000px] opacity-20 animate-spin"
              style={{
                animationDuration: '66s',
                animationTimingFunction: 'linear',
                animationIterationCount: 'infinite',
                filter: 'brightness(1.2) contrast(1.1)',
              }}
            />

            {/* Secondary Globe for depth */}
            <img
              src="/globe.svg"
              alt="Globe Background Secondary"
              className="absolute inset-0 w-[1000px] h-[1000px] opacity-10 animate-spin"
              style={{
                animationDuration: '120s',
                animationTimingFunction: 'linear',
                animationIterationCount: 'infinite',
                animationDirection: 'reverse',
                filter: 'blur(2px)',
              }}
            />
          </div>
        </div>

        {/* Floating orbs with parallax */}
        <div
          className="absolute top-1/4 left-1/4 w-64 h-64 bg-amber-500/10 rounded-full blur-3xl animate-pulse"
          style={{
            transform: `translateY(${scrollY * 0.2}px)`,
          }}
        ></div>
        <div
          className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-amber-600/5 rounded-full blur-3xl animate-pulse"
          style={{
            animationDelay: '2s',
            transform: `translateY(${scrollY * -0.15}px)`,
          }}
        ></div>

        {/* Grid pattern */}
        <div
          className="absolute inset-0 opacity-30"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: '60px 60px',
            transform: `translateY(${scrollY * 0.1}px)`,
          }}
        ></div>
      </div>

      {/* Hero Section */}
      <Section padding="xl" className="relative z-10">
        <div className="grid md:grid-cols-2 items-center gap-12 xl:gap-16">
          <AnimatedElement animation="fadeInUp" className="text-center md:text-left">
            <div className="flex items-center justify-center md:justify-start gap-3 mb-8">
              <Badge variant="primary" size="lg" className="px-4 py-2">
                <img
                  src={DataImage.CigiGlobal2}
                  alt="Cigi Global Logo"
                  className="w- h-6 rounded-md mr-2"
                  loading="lazy"
                />
                Cimande Girang - Global Solutions, Local Excellence
              </Badge>
            </div>

            <h1 className="text-responsive-xl mb-6 gradient-text">
              Transformasi Digital
              <br />
              <span className="text-white">Masa Depan</span>
            </h1>

            <p className="text-lg text-zinc-300 mb-8 max-w-2xl mx-auto md:mx-0 leading-relaxed">
              PT Cimande Girang Global adalah perusahaan inovatif yang berkomitmen untuk memberikan
              <span className="text-amber-400 font-semibold"> solusi digital terbaik</span> bagi
              bisnis Anda. Dengan pengalaman lebih dari 4 tahun, kami hadir sebagai mitra terpercaya
              dalam transformasi digital.
            </p>

            <div className="flex flex-col sm:flex-row items-center justify-center md:justify-start gap-4">
              <Link to="/about">
                <Button size="lg" className="w-full sm:w-auto">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  Tentang Kami
                </Button>
              </Link>
              <Link to="/news">
                <Button variant="secondary" size="lg" className="w-full sm:w-auto">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                    />
                  </svg>
                  Lihat Berita
                </Button>
              </Link>
            </div>
          </AnimatedElement>

          <AnimatedElement animation="slideInRight" delay={300} className="relative">
            <div className="relative">
              <div className="absolute -inset-4 bg-gradient-to-r from-amber-600 to-amber-400 rounded-2xl blur-2xl opacity-20 animate-pulse"></div>
              <img
                src={DataImage.CigiGlobal2}
                alt="Cigi Global Office"
                className="relative w-full max-w-md md:max-w-lg mx-auto rounded-2xl shadow-2xl"
                loading="lazy"
              />
            </div>
          </AnimatedElement>
        </div>
      </Section>

      {/* Tentang Perusahaan */}
      <Section id="tentang" padding="xl" background="gradient">
        <AnimatedElement animation="fadeInUp">
          <Card className="max-w-5xl mx-auto" padding="xl">
            <div className="flex items-start gap-6 mb-8">
              <div className="hidden sm:block">
                <img
                  src={DataImage.CigiGlobal2}
                  alt="Cigi Global Logo"
                  className="w-15 h-15 rounded-md flex items-center justify-center group-hover:scale-110 transition-transform duration-300"
                  loading="lazy"
                />
              </div>
              <div className="flex-1">
                <h2 className="text-responsive-lg mb-4">Tentang Cigi Global</h2>
                <p className="text-lg text-zinc-300 leading-relaxed">
                  PT Cimande Girang Global (Cigi Global) adalah perusahaan teknologi yang berfokus
                  pada pengembangan
                  <span className="text-amber-400 font-semibold">
                    {' '}
                    solusi digital komprehensif
                  </span>{' '}
                  untuk bisnis. Didirikan dengan visi untuk menjadi pemimpin dalam transformasi
                  digital di Indonesia, kami menyediakan layanan pengembangan website, aplikasi
                  mobile, sistem manajemen, dan konsultasi IT.
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-amber-500 to-amber-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <span className="text-3xl font-bold text-white">150+</span>
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">Proyek Selesai</h3>
                <p className="text-zinc-400">Berbagai solusi digital yang telah kami kerjakan</p>
              </div>

              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-amber-500 to-amber-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <span className="text-3xl font-bold text-white">4+</span>
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">Tahun Pengalaman</h3>
                <p className="text-zinc-400">Pengalaman dalam industri teknologi</p>
              </div>

              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-amber-500 to-amber-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <span className="text-3xl font-bold text-white">100%</span>
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">Kepuasan Klien</h3>
                <p className="text-zinc-400">Komitmen terhadap kualitas dan kepuasan</p>
              </div>
            </div>
          </Card>
        </AnimatedElement>
      </Section>

      {/* Unit Bisnis CIGI Section */}
      <Section padding="xl">
        <div className="text-center mb-16">
          <AnimatedElement animation="fadeInUp">
            <h2 className="text-responsive-lg mb-4">Unit Bisnis CIGI Global</h2>
            <p className="text-lg text-zinc-400 max-w-3xl mx-auto">
              Beragam unit bisnis yang menjadi pilar kekuatan CIGI Global dalam melayani berbagai
              kebutuhan masyarakat, mulai dari agrikultur hingga layanan komunitas
            </p>
          </AnimatedElement>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
          {listBusinessDivisions.map((division, index) => (
            <AnimatedElement key={division.id} animation="fadeInUp" delay={index * 100}>
              <Link to={division.link}>
                <Card
                  className="group cursor-pointer overflow-hidden h-full hover:shadow-xl transition-all duration-300"
                  padding="none"
                >
                  <div className="aspect-[4/3] overflow-hidden">
                    <img
                      src={division.gambar}
                      alt={division.nama}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      loading="lazy"
                    />
                  </div>
                  <div className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-xl">{division.icon}</span>
                      <h4 className="font-bold text-white group-hover:text-amber-400 transition-colors text-base line-clamp-1">
                        {division.nama}
                      </h4>
                    </div>
                    <p className="text-xs text-amber-400 font-semibold mb-2 uppercase tracking-wide">
                      {division.ket}
                    </p>
                    <p className="text-xs text-zinc-400 group-hover:text-zinc-300 transition-colors leading-relaxed line-clamp-2">
                      {division.deskripsi}
                    </p>
                  </div>
                  <div className="px-4 pb-4">
                    <div className="flex items-center text-amber-400 group-hover:text-amber-300 transition-colors">
                      <span className="text-xs font-medium">Selengkapnya</span>
                      <svg
                        className="w-3 h-3 ml-1 group-hover:translate-x-1 transition-transform"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </div>
                  </div>
                </Card>
              </Link>
            </AnimatedElement>
          ))}
        </div>
      </Section>

      {/* Galeri Kegiatan */}
      <Section id="galeri" padding="xl" background="dark">
        <div className="text-center mb-16">
          <AnimatedElement animation="fadeInUp">
            <h2 className="text-responsive-lg mb-4">Galeri Kegiatan</h2>
            <p className="text-lg text-zinc-400 max-w-3xl mx-auto">
              Dokumentasi berbagai kegiatan dan momen penting yang telah kami lakukan bersama
            </p>
          </AnimatedElement>
        </div>

        {/* Data Galeri Kegiatan */}
        {(() => {
          const listKegiatan = [
            { id: 1, gambar: 'https://picsum.photos/seed/foto1/600/400' },
            { id: 2, gambar: 'https://picsum.photos/seed/foto2/600/400' },
            { id: 3, gambar: 'https://picsum.photos/seed/foto3/600/400' },
            { id: 4, gambar: 'https://picsum.photos/seed/foto4/600/400' },
            { id: 5, gambar: 'https://picsum.photos/seed/foto5/600/400' },
            { id: 6, gambar: 'https://picsum.photos/seed/foto6/600/400' },
          ]

          return (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {listKegiatan.map((kegiatan, index) => (
                <AnimatedElement key={kegiatan.id} animation="fadeInUp" delay={index * 150}>
                  <Card className="group overflow-hidden" padding="none">
                    <div className="aspect-video overflow-hidden">
                      <img
                        src={kegiatan.gambar}
                        alt={`Kegiatan ${kegiatan.id}`}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                        loading="lazy"
                      />
                    </div>
                  </Card>
                </AnimatedElement>
              ))}
            </div>
          )
        })()}
      </Section>

      {/* Latest News */}
      <Section id="news" padding="xl" background="dark">
        <div className="text-center mb-16">
          <AnimatedElement animation="fadeInUp">
            <h2 className="text-responsive-lg mb-4">Berita & Update Terbaru</h2>
            <p className="text-lg text-zinc-400 max-w-3xl mx-auto">
              Tetap update dengan berita terbaru, wawasan, dan perkembangan dari Cigi Global dan
              unit bisnis kami
            </p>
          </AnimatedElement>
        </div>

        <AnimatedElement animation="fadeInUp" delay={200}>
          <NewsList featured={true} limit={3} />
        </AnimatedElement>

        <div className="text-center mt-12">
          <AnimatedElement animation="fadeInUp" delay={400}>
            <Link to="/news">
              <Button variant="secondary" size="lg">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"
                  />
                </svg>
                Lihat Semua Berita
              </Button>
            </Link>
          </AnimatedElement>
        </div>
      </Section>

      {/* Kontak Perusahaan */}
      <Section id="kontak" padding="xl" background="gradient">
        <div className="text-center mb-12">
          <AnimatedElement animation="fadeInUp">
            <h2 className="text-responsive-lg mb-4">Hubungi Kami</h2>
            <p className="text-lg text-zinc-400 max-w-2xl mx-auto">
              Siap membantu transformasi digital bisnis Anda dengan solusi terbaik
            </p>
          </AnimatedElement>
        </div>

        <AnimatedElement animation="fadeInUp" delay={300}>
          <Card className="max-w-2xl mx-auto" padding="xl">
            <form
              action="https://formsubmit.co/<EMAIL>"
              method="POST"
              autoComplete="off"
            >
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-6">
                <div>
                  <label className="block text-sm font-semibold text-white mb-2">
                    Nama Perusahaan
                  </label>
                  <input
                    type="text"
                    name="nama"
                    placeholder="Masukkan nama perusahaan"
                    className="w-full px-4 py-3 bg-zinc-700 border border-zinc-600 rounded-lg text-white placeholder-zinc-400 focus:border-amber-500 focus:ring-2 focus:ring-amber-500/20 transition-colors"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-semibold text-white mb-2">
                    Email Perusahaan
                  </label>
                  <input
                    type="email"
                    name="email"
                    placeholder="<EMAIL>"
                    className="w-full px-4 py-3 bg-zinc-700 border border-zinc-600 rounded-lg text-white placeholder-zinc-400 focus:border-amber-500 focus:ring-2 focus:ring-amber-500/20 transition-colors"
                    required
                  />
                </div>
              </div>

              <div className="mb-6">
                <label htmlFor="pesan" className="block text-sm font-semibold text-white mb-2">
                  Pesan/Kebutuhan
                </label>
                <textarea
                  name="pesan"
                  id="pesan"
                  rows="6"
                  placeholder="Jelaskan kebutuhan digital Anda..."
                  className="w-full px-4 py-3 bg-zinc-700 border border-zinc-600 rounded-lg text-white placeholder-zinc-400 focus:border-amber-500 focus:ring-2 focus:ring-amber-500/20 transition-colors resize-none"
                  required
                ></textarea>
              </div>

              <Button type="submit" size="lg" className="w-full">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                  />
                </svg>
                Kirim Pesan
              </Button>
            </form>
          </Card>
        </AnimatedElement>
      </Section>
    </>
  )
}

export default Home
