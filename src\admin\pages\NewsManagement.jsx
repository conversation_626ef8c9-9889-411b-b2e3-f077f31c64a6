/**
 * NewsManagement - Admin interface for managing news articles
 */

import { useState, useEffect } from 'react'
import Card from '../../components/ui/Card.jsx'
import Button from '../../components/ui/Button.jsx'
import Badge from '../../components/ui/Badge.jsx'
import newsService from '../../services/NewsService.js'
import NewsEditor from '../components/NewsEditor.jsx'

export default function NewsManagement() {
  const [articles, setArticles] = useState([])
  const [categories, setCategories] = useState([])
  const [stats, setStats] = useState({})
  const [view, setView] = useState('list') // list, create, edit
  const [selectedArticle, setSelectedArticle] = useState(null)
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')

  useEffect(() => {
    loadData()
  }, [])

  const loadData = () => {
    setLoading(true)
    try {
      const articlesData = newsService.getArticles()
      const categoriesData = newsService.getCategories()
      const statsData = newsService.getStats()

      setArticles(articlesData)
      setCategories(categoriesData)
      setStats(statsData)
    } catch (error) {
      console.error('Error loading news data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateArticle = () => {
    setSelectedArticle(null)
    setView('create')
  }

  const handleEditArticle = (article) => {
    setSelectedArticle(article)
    setView('edit')
  }

  const handleDeleteArticle = async (articleId) => {
    if (window.confirm('Are you sure you want to delete this article?')) {
      try {
        newsService.deleteArticle(articleId)
        loadData()
      } catch (error) {
        console.error('Error deleting article:', error)
      }
    }
  }

  const handleSaveArticle = (articleData) => {
    try {
      if (selectedArticle) {
        newsService.updateArticle(selectedArticle.id, articleData)
      } else {
        newsService.createArticle(articleData)
      }
      setView('list')
      loadData()
    } catch (error) {
      console.error('Error saving article:', error)
    }
  }

  const handleCancel = () => {
    setView('list')
    setSelectedArticle(null)
  }

  const filteredArticles = articles.filter(
    (article) =>
      article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.excerpt.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const getStatusBadge = (status) => {
    const variants = {
      published: 'success',
      draft: 'warning',
      archived: 'secondary',
    }
    return <Badge variant={variants[status] || 'secondary'}>{status}</Badge>
  }

  const getCategoryName = (categoryId) => {
    const category = categories.find((cat) => cat.id === categoryId)
    return category ? category.name : 'Uncategorized'
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  if (view === 'create' || view === 'edit') {
    return (
      <NewsEditor
        article={selectedArticle}
        categories={categories}
        onSave={handleSaveArticle}
        onCancel={handleCancel}
        mode={view}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">News Management</h1>
          <p className="text-zinc-400">Manage your news articles and blog posts</p>
        </div>
        <Button onClick={handleCreateArticle}>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          New Article
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card padding="lg">
          <div className="text-center">
            <div className="text-2xl font-bold text-white">{stats.total || 0}</div>
            <div className="text-sm text-zinc-400">Total Articles</div>
          </div>
        </Card>
        <Card padding="lg">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-400">{stats.published || 0}</div>
            <div className="text-sm text-zinc-400">Published</div>
          </div>
        </Card>
        <Card padding="lg">
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-400">{stats.draft || 0}</div>
            <div className="text-sm text-zinc-400">Drafts</div>
          </div>
        </Card>
        <Card padding="lg">
          <div className="text-center">
            <div className="text-2xl font-bold text-amber-400">{stats.featured || 0}</div>
            <div className="text-sm text-zinc-400">Featured</div>
          </div>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card padding="lg">
        <div className="flex items-center gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search articles..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:border-amber-500"
            />
          </div>
        </div>
      </Card>

      {/* Articles List */}
      <Card padding="lg">
        <div className="space-y-4">
          {loading ? (
            <div className="text-center py-8">
              <div className="w-8 h-8 border-4 border-amber-600 border-t-transparent rounded-full animate-spin mx-auto"></div>
              <p className="text-zinc-400 mt-2">Loading articles...</p>
            </div>
          ) : filteredArticles.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-zinc-400">No articles found</p>
            </div>
          ) : (
            filteredArticles.map((article) => (
              <div
                key={article.id}
                className="flex items-center justify-between p-4 bg-zinc-800 rounded-lg border border-zinc-700"
              >
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="font-semibold text-white">{article.title}</h3>
                    {article.featured && (
                      <Badge variant="warning" size="sm">
                        Featured
                      </Badge>
                    )}
                    {getStatusBadge(article.status)}
                  </div>
                  <p className="text-sm text-zinc-400 mb-2">{article.excerpt}</p>
                  <div className="flex items-center gap-4 text-xs text-zinc-500">
                    <span>Category: {getCategoryName(article.categoryId)}</span>
                    <span>Author: {article.author}</span>
                    <span>Updated: {formatDate(article.updatedAt)}</span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="secondary" size="sm" onClick={() => handleEditArticle(article)}>
                    Edit
                  </Button>
                  <Button
                    variant="danger"
                    size="sm"
                    onClick={() => handleDeleteArticle(article.id)}
                  >
                    Delete
                  </Button>
                </div>
              </div>
            ))
          )}
        </div>
      </Card>
    </div>
  )
}
