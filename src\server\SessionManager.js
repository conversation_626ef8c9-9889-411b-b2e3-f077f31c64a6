/**
 * SessionManager - Server-side session management with SQLite storage
 */

import Database from 'better-sqlite3'
import bcrypt from 'bcryptjs'
import { v4 as uuidv4 } from 'uuid'
import path from 'path'
import fs from 'fs'

class SessionManager {
  constructor() {
    this.db = null
    this.sessionTimeout = 24 * 60 * 60 * 1000 // 24 hours
    this.cleanupInterval = 60 * 60 * 1000 // 1 hour
    this.init()
  }

  /**
   * Initialize the session manager
   */
  init() {
    try {
      // Ensure data directory exists
      const dataDir = path.join(globalThis.process?.cwd() || '.', 'data')
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true })
      }

      // Initialize SQLite database
      const dbPath = path.join(dataDir, 'sessions.db')
      this.db = new Database(dbPath)

      // Create tables
      this.createTables()

      // Start cleanup interval
      this.startCleanupInterval()

      console.log('SessionManager initialized successfully')
    } catch (error) {
      console.error('Failed to initialize SessionManager:', error)
      throw error
    }
  }

  /**
   * Create database tables
   */
  createTables() {
    // Users table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        role TEXT DEFAULT 'admin',
        permissions TEXT DEFAULT '[]',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Sessions table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS sessions (
        id TEXT PRIMARY KEY,
        user_id INTEGER NOT NULL,
        data TEXT NOT NULL,
        expires_at DATETIME NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    `)

    // Create default admin user if it doesn't exist
    this.createDefaultUser()
  }

  /**
   * Create default admin user
   */
  createDefaultUser() {
    const existingUser = this.db.prepare('SELECT id FROM users WHERE username = ?').get('admin')

    if (!existingUser) {
      const passwordHash = bcrypt.hashSync('cigi2024!', 10)
      const permissions = JSON.stringify([
        'read',
        'write',
        'delete',
        'manage_modules',
        'manage_users',
      ])

      this.db
        .prepare(
          `
        INSERT INTO users (username, password_hash, role, permissions)
        VALUES (?, ?, ?, ?)
      `
        )
        .run('admin', passwordHash, 'admin', permissions)

      console.log('Default admin user created')
    }
  }

  /**
   * Authenticate user and create session
   */
  async login(username, password) {
    try {
      // Get user from database
      const user = this.db.prepare('SELECT * FROM users WHERE username = ?').get(username)

      if (!user) {
        return { success: false, error: 'Invalid credentials' }
      }

      // Verify password
      const isValidPassword = bcrypt.compareSync(password, user.password_hash)

      if (!isValidPassword) {
        return { success: false, error: 'Invalid credentials' }
      }

      // Create session
      const sessionId = uuidv4()
      const expiresAt = new Date(Date.now() + this.sessionTimeout)

      const sessionData = {
        userId: user.id,
        username: user.username,
        role: user.role,
        permissions: JSON.parse(user.permissions),
        loginTime: new Date().toISOString(),
      }

      // Store session in database
      this.db
        .prepare(
          `
        INSERT INTO sessions (id, user_id, data, expires_at)
        VALUES (?, ?, ?, ?)
      `
        )
        .run(sessionId, user.id, JSON.stringify(sessionData), expiresAt.toISOString())

      console.log(`User ${username} logged in successfully`)

      return {
        success: true,
        sessionId,
        user: {
          id: user.id,
          username: user.username,
          role: user.role,
          permissions: JSON.parse(user.permissions),
        },
        expiresAt,
      }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, error: 'Login failed' }
    }
  }

  /**
   * Validate session
   */
  validateSession(sessionId) {
    try {
      if (!sessionId) {
        return { valid: false, error: 'No session ID provided' }
      }

      const session = this.db
        .prepare(
          `
        SELECT s.*, u.username, u.role, u.permissions
        FROM sessions s
        JOIN users u ON s.user_id = u.id
        WHERE s.id = ? AND s.expires_at > datetime('now')
      `
        )
        .get(sessionId)

      if (!session) {
        return { valid: false, error: 'Invalid or expired session' }
      }

      const sessionData = JSON.parse(session.data)

      return {
        valid: true,
        user: {
          id: session.user_id,
          username: session.username,
          role: session.role,
          permissions: JSON.parse(session.permissions),
        },
        sessionData,
        expiresAt: new Date(session.expires_at),
      }
    } catch (error) {
      console.error('Session validation error:', error)
      return { valid: false, error: 'Session validation failed' }
    }
  }

  /**
   * Refresh session expiration
   */
  refreshSession(sessionId) {
    try {
      const expiresAt = new Date(Date.now() + this.sessionTimeout)

      const result = this.db
        .prepare(
          `
        UPDATE sessions 
        SET expires_at = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `
        )
        .run(expiresAt.toISOString(), sessionId)

      return result.changes > 0
    } catch (error) {
      console.error('Session refresh error:', error)
      return false
    }
  }

  /**
   * Logout and destroy session
   */
  logout(sessionId) {
    try {
      const result = this.db.prepare('DELETE FROM sessions WHERE id = ?').run(sessionId)
      return result.changes > 0
    } catch (error) {
      console.error('Logout error:', error)
      return false
    }
  }

  /**
   * Cleanup expired sessions
   */
  cleanupExpiredSessions() {
    try {
      const result = this.db
        .prepare(
          `
        DELETE FROM sessions WHERE expires_at <= datetime('now')
      `
        )
        .run()

      if (result.changes > 0) {
        console.log(`Cleaned up ${result.changes} expired sessions`)
      }

      return result.changes
    } catch (error) {
      console.error('Session cleanup error:', error)
      return 0
    }
  }

  /**
   * Start cleanup interval
   */
  startCleanupInterval() {
    setInterval(() => {
      this.cleanupExpiredSessions()
    }, this.cleanupInterval)
  }

  /**
   * Get session statistics
   */
  getSessionStats() {
    try {
      const totalSessions = this.db.prepare('SELECT COUNT(*) as count FROM sessions').get().count
      const activeSessions = this.db
        .prepare(
          `
        SELECT COUNT(*) as count FROM sessions WHERE expires_at > datetime('now')
      `
        )
        .get().count

      return {
        total: totalSessions,
        active: activeSessions,
        expired: totalSessions - activeSessions,
      }
    } catch (error) {
      console.error('Error getting session stats:', error)
      return { total: 0, active: 0, expired: 0 }
    }
  }

  /**
   * Change user password
   */
  async changePassword(userId, currentPassword, newPassword) {
    try {
      const user = this.db.prepare('SELECT password_hash FROM users WHERE id = ?').get(userId)

      if (!user) {
        return { success: false, error: 'User not found' }
      }

      // Verify current password
      const isValidPassword = bcrypt.compareSync(currentPassword, user.password_hash)

      if (!isValidPassword) {
        return { success: false, error: 'Current password is incorrect' }
      }

      if (newPassword.length < 6) {
        return { success: false, error: 'New password must be at least 6 characters' }
      }

      // Hash new password
      const newPasswordHash = bcrypt.hashSync(newPassword, 10)

      // Update password
      this.db
        .prepare(
          `
        UPDATE users 
        SET password_hash = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `
        )
        .run(newPasswordHash, userId)

      console.log(`Password changed for user ID ${userId}`)
      return { success: true }
    } catch (error) {
      console.error('Password change error:', error)
      return { success: false, error: 'Password change failed' }
    }
  }

  /**
   * Close database connection
   */
  close() {
    if (this.db) {
      this.db.close()
      console.log('SessionManager database connection closed')
    }
  }
}

// Create singleton instance
const sessionManager = new SessionManager()

export default sessionManager
