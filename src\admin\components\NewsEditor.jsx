/**
 * NewsEditor - Component for creating and editing news articles
 */

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import Card from '../../components/ui/Card.jsx'
import Button from '../../components/ui/Button.jsx'
import CodeEditor from '@uiw/react-textarea-code-editor'

// Validation schema
const articleSchema = yup.object({
  title: yup.string().required('Title is required').min(3, 'Title must be at least 3 characters'),
  excerpt: yup
    .string()
    .required('Excerpt is required')
    .min(10, 'Excerpt must be at least 10 characters'),
  content: yup
    .string()
    .required('Content is required')
    .min(50, 'Content must be at least 50 characters'),
  categoryId: yup.string().required('Category is required'),
  author: yup.string().required('Author is required'),
  tags: yup.string(),
  status: yup.string().oneOf(['draft', 'published']).required(),
  featured: yup.boolean(),
})

export default function NewsEditor({ article, categories, onSave, onCancel, mode }) {
  const [previewMode, setPreviewMode] = useState(false)
  const [saving, setSaving] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm({
    resolver: yupResolver(articleSchema),
    defaultValues: {
      title: '',
      excerpt: '',
      content: '',
      categoryId: '',
      author: 'Cigi Global Team',
      tags: '',
      status: 'draft',
      featured: false,
    },
  })

  const watchedContent = watch('content')
  const watchedTitle = watch('title')
  const watchedExcerpt = watch('excerpt')

  useEffect(() => {
    if (article && mode === 'edit') {
      reset({
        title: article.title || '',
        excerpt: article.excerpt || '',
        content: article.content || '',
        categoryId: article.categoryId || '',
        author: article.author || 'Cigi Global Team',
        tags: article.tags ? article.tags.join(', ') : '',
        status: article.status || 'draft',
        featured: article.featured || false,
      })
    }
  }, [article, mode, reset])

  const onSubmit = async (data) => {
    setSaving(true)
    try {
      // Process tags
      const tags = data.tags
        ? data.tags
            .split(',')
            .map((tag) => tag.trim())
            .filter((tag) => tag.length > 0)
        : []

      const articleData = {
        ...data,
        tags,
        publishedAt:
          data.status === 'published' && (!article || article.status !== 'published')
            ? new Date().toISOString()
            : article?.publishedAt,
      }

      onSave(articleData)
    } catch (error) {
      console.error('Error saving article:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleContentChange = (value) => {
    setValue('content', value)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">
            {mode === 'edit' ? 'Edit Article' : 'Create New Article'}
          </h1>
          <p className="text-zinc-400">
            {mode === 'edit' ? 'Update your article' : 'Write and publish a new article'}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="secondary" onClick={() => setPreviewMode(!previewMode)}>
            {previewMode ? 'Edit' : 'Preview'}
          </Button>
          <Button variant="secondary" onClick={onCancel}>
            Cancel
          </Button>
        </div>
      </div>

      {previewMode ? (
        /* Preview Mode */
        <Card padding="lg">
          <div className="prose prose-invert max-w-none">
            <h1 className="text-3xl font-bold text-white mb-4">
              {watchedTitle || 'Article Title'}
            </h1>
            <p className="text-lg text-zinc-300 mb-6">{watchedExcerpt || 'Article excerpt...'}</p>
            <div className="text-zinc-200 whitespace-pre-wrap">
              {watchedContent || 'Article content...'}
            </div>
          </div>
        </Card>
      ) : (
        /* Edit Mode */
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Title */}
              <Card padding="lg">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">Title *</label>
                    <input
                      type="text"
                      {...register('title')}
                      className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:border-amber-500"
                      placeholder="Enter article title"
                    />
                    {errors.title && (
                      <p className="text-red-400 text-sm mt-1">{errors.title.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white mb-2">Excerpt *</label>
                    <textarea
                      {...register('excerpt')}
                      rows={3}
                      className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:border-amber-500"
                      placeholder="Brief description of the article"
                    />
                    {errors.excerpt && (
                      <p className="text-red-400 text-sm mt-1">{errors.excerpt.message}</p>
                    )}
                  </div>
                </div>
              </Card>

              {/* Content */}
              <Card padding="lg">
                <div>
                  <label className="block text-sm font-medium text-white mb-2">Content *</label>
                  <div className="border border-zinc-700 rounded-lg overflow-hidden">
                    <CodeEditor
                      value={watchedContent}
                      language="markdown"
                      placeholder="Write your article content in Markdown..."
                      onChange={handleContentChange}
                      padding={15}
                      style={{
                        fontSize: 14,
                        backgroundColor: '#27272a',
                        fontFamily:
                          'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
                        minHeight: '400px',
                      }}
                    />
                  </div>
                  {errors.content && (
                    <p className="text-red-400 text-sm mt-1">{errors.content.message}</p>
                  )}
                  <p className="text-xs text-zinc-500 mt-2">
                    You can use Markdown syntax for formatting
                  </p>
                </div>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Publish Settings */}
              <Card padding="lg">
                <h3 className="text-lg font-semibold text-white mb-4">Publish Settings</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">Status *</label>
                    <select
                      {...register('status')}
                      className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white focus:outline-none focus:border-amber-500"
                    >
                      <option value="draft">Draft</option>
                      <option value="published">Published</option>
                    </select>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      {...register('featured')}
                      className="w-4 h-4 text-amber-600 bg-zinc-800 border-zinc-700 rounded focus:ring-amber-500"
                    />
                    <label className="ml-2 text-sm text-white">Featured Article</label>
                  </div>
                </div>
              </Card>

              {/* Article Details */}
              <Card padding="lg">
                <h3 className="text-lg font-semibold text-white mb-4">Article Details</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">Category *</label>
                    <select
                      {...register('categoryId')}
                      className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white focus:outline-none focus:border-amber-500"
                    >
                      <option value="">Select a category</option>
                      {categories.map((category) => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                    {errors.categoryId && (
                      <p className="text-red-400 text-sm mt-1">{errors.categoryId.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white mb-2">Author *</label>
                    <input
                      type="text"
                      {...register('author')}
                      className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:border-amber-500"
                      placeholder="Author name"
                    />
                    {errors.author && (
                      <p className="text-red-400 text-sm mt-1">{errors.author.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white mb-2">Tags</label>
                    <input
                      type="text"
                      {...register('tags')}
                      className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:border-amber-500"
                      placeholder="tag1, tag2, tag3"
                    />
                    <p className="text-xs text-zinc-500 mt-1">Separate tags with commas</p>
                  </div>
                </div>
              </Card>

              {/* Actions */}
              <Card padding="lg">
                <div className="space-y-3">
                  <Button type="submit" className="w-full" loading={saving}>
                    {mode === 'edit' ? 'Update Article' : 'Create Article'}
                  </Button>
                  <Button type="button" variant="secondary" className="w-full" onClick={onCancel}>
                    Cancel
                  </Button>
                </div>
              </Card>
            </div>
          </div>
        </form>
      )}
    </div>
  )
}
