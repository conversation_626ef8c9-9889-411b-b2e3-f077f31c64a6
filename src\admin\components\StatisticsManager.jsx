/**
 * StatisticsManager - Component for managing business unit statistics
 */

import { useState } from 'react'
import Card from '../../components/ui/Card.jsx'
import Button from '../../components/ui/Button.jsx'

export default function StatisticsManager({ statistics, onChange }) {
  const [errors, setErrors] = useState({})

  const handleTitleChange = (value) => {
    const updatedStatistics = {
      ...statistics,
      title: value,
    }
    onChange(updatedStatistics)

    // Clear title error if it exists
    if (errors.title) {
      setErrors((prev) => ({ ...prev, title: null }))
    }
  }

  const handleDescriptionChange = (value) => {
    const updatedStatistics = {
      ...statistics,
      description: value,
    }
    onChange(updatedStatistics)

    // Clear description error if it exists
    if (errors.description) {
      setErrors((prev) => ({ ...prev, description: null }))
    }
  }

  const handleStatItemChange = (index, field, value) => {
    const updatedItems = [...(statistics.items || [])]
    updatedItems[index] = {
      ...updatedItems[index],
      [field]: value,
    }

    const updatedStatistics = {
      ...statistics,
      items: updatedItems,
    }
    onChange(updatedStatistics)

    // Clear item-specific errors
    const errorKey = `item_${index}_${field}`
    if (errors[errorKey]) {
      setErrors((prev) => ({ ...prev, [errorKey]: null }))
    }
  }

  const addStatItem = () => {
    const updatedItems = [...(statistics.items || []), { value: '', label: '' }]
    const updatedStatistics = {
      ...statistics,
      items: updatedItems,
    }
    onChange(updatedStatistics)
  }

  const removeStatItem = (index) => {
    if (window.confirm('Are you sure you want to remove this statistic item?')) {
      const updatedItems = (statistics.items || []).filter((_, i) => i !== index)
      const updatedStatistics = {
        ...statistics,
        items: updatedItems,
      }
      onChange(updatedStatistics)

      // Clear any errors for this item
      const newErrors = { ...errors }
      delete newErrors[`item_${index}_value`]
      delete newErrors[`item_${index}_label`]
      setErrors(newErrors)
    }
  }

  const moveStatItem = (index, direction) => {
    const items = [...(statistics.items || [])]
    const newIndex = direction === 'up' ? index - 1 : index + 1

    if (newIndex >= 0 && newIndex < items.length) {
      ;[items[index], items[newIndex]] = [items[newIndex], items[index]]

      const updatedStatistics = {
        ...statistics,
        items,
      }
      onChange(updatedStatistics)
    }
  }

  const validateStatistics = () => {
    const newErrors = {}

    // Validate title (optional but if provided should be reasonable length)
    if (statistics.title && statistics.title.trim().length > 100) {
      newErrors.title = 'Title must be 100 characters or less'
    }

    // Validate description (optional but if provided should be reasonable length)
    if (statistics.description && statistics.description.trim().length > 500) {
      newErrors.description = 'Description must be 500 characters or less'
    }

    // Validate statistics items
    if (statistics.items && statistics.items.length > 0) {
      statistics.items.forEach((item, index) => {
        // Validate value
        if (!item.value || item.value.trim().length === 0) {
          newErrors[`item_${index}_value`] = 'Value is required'
        } else if (item.value.trim().length > 50) {
          newErrors[`item_${index}_value`] = 'Value must be 50 characters or less'
        }

        // Validate label
        if (!item.label || item.label.trim().length === 0) {
          newErrors[`item_${index}_label`] = 'Label is required'
        } else if (item.label.trim().length > 100) {
          newErrors[`item_${index}_label`] = 'Label must be 100 characters or less'
        }
      })
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Validate on data changes
  useState(() => {
    validateStatistics()
  }, [statistics])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-xl font-semibold text-white mb-2">Statistics Management</h2>
        <p className="text-zinc-400">
          Manage statistical information and key metrics for this business unit.
        </p>
      </div>

      {/* Statistics Title and Description */}
      <Card padding="lg">
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-white">Section Information</h3>

          <div>
            <label className="block text-sm font-medium text-white mb-2">Statistics Title</label>
            <input
              type="text"
              value={statistics.title || ''}
              onChange={(e) => handleTitleChange(e.target.value)}
              placeholder="e.g., Our Achievements, Key Statistics, Performance Metrics"
              className={`w-full px-3 py-2 bg-zinc-800 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors ${
                errors.title
                  ? 'border-red-500 focus:border-red-400'
                  : 'border-zinc-700 focus:border-amber-500'
              }`}
            />
            {errors.title && <p className="mt-1 text-sm text-red-400">{errors.title}</p>}
            <p className="mt-1 text-sm text-zinc-500">Optional title for the statistics section</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Statistics Description
            </label>
            <textarea
              value={statistics.description || ''}
              onChange={(e) => handleDescriptionChange(e.target.value)}
              placeholder="Brief description of what these statistics represent..."
              rows={3}
              className={`w-full px-3 py-2 bg-zinc-800 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors resize-vertical ${
                errors.description
                  ? 'border-red-500 focus:border-red-400'
                  : 'border-zinc-700 focus:border-amber-500'
              }`}
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-400">{errors.description}</p>
            )}
            <p className="mt-1 text-sm text-zinc-500">
              Optional description to provide context for the statistics
            </p>
          </div>
        </div>
      </Card>

      {/* Statistics Items */}
      <Card padding="lg">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-white">Statistics Items</h3>
            <Button onClick={addStatItem} size="sm">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4v16m8-8H4"
                />
              </svg>
              Add Statistic
            </Button>
          </div>

          {!statistics.items || statistics.items.length === 0 ? (
            <div className="text-center py-8">
              <div className="w-12 h-12 bg-zinc-700 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg
                  className="w-6 h-6 text-zinc-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  />
                </svg>
              </div>
              <h4 className="text-sm font-medium text-white mb-1">No Statistics Added</h4>
              <p className="text-xs text-zinc-400 mb-4">Add your first statistic to get started</p>
              <Button onClick={addStatItem} size="sm" variant="secondary">
                Add First Statistic
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {statistics.items.map((item, index) => (
                <div key={index} className="bg-zinc-800 rounded-lg p-4 border border-zinc-700">
                  <div className="flex items-start gap-4">
                    {/* Move Controls */}
                    <div className="flex flex-col gap-1 pt-2">
                      <button
                        onClick={() => moveStatItem(index, 'up')}
                        disabled={index === 0}
                        className="p-1 text-zinc-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Move up"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 15l7-7 7 7"
                          />
                        </svg>
                      </button>
                      <button
                        onClick={() => moveStatItem(index, 'down')}
                        disabled={index === statistics.items.length - 1}
                        className="p-1 text-zinc-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Move down"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 9l-7 7-7-7"
                          />
                        </svg>
                      </button>
                    </div>

                    {/* Form Fields */}
                    <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-white mb-2">
                          Value <span className="text-red-400">*</span>
                        </label>
                        <input
                          type="text"
                          value={item.value || ''}
                          onChange={(e) => handleStatItemChange(index, 'value', e.target.value)}
                          placeholder="e.g., 1000+, 95%, $2.5M"
                          className={`w-full px-3 py-2 bg-zinc-800 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors ${
                            errors[`item_${index}_value`]
                              ? 'border-red-500 focus:border-red-400'
                              : 'border-zinc-700 focus:border-amber-500'
                          }`}
                        />
                        {errors[`item_${index}_value`] && (
                          <p className="mt-1 text-sm text-red-400">
                            {errors[`item_${index}_value`]}
                          </p>
                        )}
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-white mb-2">
                          Label <span className="text-red-400">*</span>
                        </label>
                        <input
                          type="text"
                          value={item.label || ''}
                          onChange={(e) => handleStatItemChange(index, 'label', e.target.value)}
                          placeholder="e.g., Happy Customers, Success Rate, Revenue"
                          className={`w-full px-3 py-2 bg-zinc-800 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors ${
                            errors[`item_${index}_label`]
                              ? 'border-red-500 focus:border-red-400'
                              : 'border-zinc-700 focus:border-amber-500'
                          }`}
                        />
                        {errors[`item_${index}_label`] && (
                          <p className="mt-1 text-sm text-red-400">
                            {errors[`item_${index}_label`]}
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Remove Button */}
                    <div className="pt-6">
                      <button
                        onClick={() => removeStatItem(index)}
                        className="p-2 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg transition-colors"
                        title="Remove statistic"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </Card>

      {/* Preview */}
      {statistics.items && statistics.items.length > 0 && (
        <Card padding="lg">
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-white">Preview</h3>
            <div className="bg-zinc-900 rounded-lg p-6 border border-zinc-700">
              {statistics.title && (
                <h4 className="text-xl font-semibold text-white mb-2">{statistics.title}</h4>
              )}
              {statistics.description && (
                <p className="text-zinc-400 mb-6">{statistics.description}</p>
              )}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {statistics.items.map((item, index) => (
                  <div key={index} className="text-center">
                    <div className="text-2xl font-bold text-amber-400 mb-1">{item.value}</div>
                    <div className="text-sm text-zinc-400">{item.label}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Validation Summary */}
      {Object.keys(errors).length > 0 && (
        <Card padding="lg">
          <div className="flex items-start gap-3">
            <svg
              className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <div>
              <h4 className="text-sm font-medium text-red-400 mb-2">
                Please fix the following errors:
              </h4>
              <ul className="text-sm text-red-300 space-y-1">
                {Object.entries(errors).map(([key, error]) => (
                  <li key={key}>• {error}</li>
                ))}
              </ul>
            </div>
          </div>
        </Card>
      )}
    </div>
  )
}
