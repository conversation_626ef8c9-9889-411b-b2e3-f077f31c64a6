/**
 * <PERSON>gesManager - Component for managing badges with add/remove functionality and preview
 */

import { useState, useEffect } from 'react'
import Button from '../../components/ui/Button.jsx'
import Badge from '../../components/ui/Badge.jsx'

export default function BadgesManager({ badges = [], onChange }) {
  const [badgeList, setBadgeList] = useState([])
  const [errors, setErrors] = useState({})

  // Initialize badge list
  useEffect(() => {
    setBadgeList(badges.length > 0 ? badges : [{ text: '', variant: 'primary' }])
  }, [badges])

  const badgeVariants = [
    {
      value: 'primary',
      label: 'Primary',
      color: 'bg-amber-600/20 text-amber-400 border-amber-600/30',
    },
    { value: 'secondary', label: 'Secondary', color: 'bg-zinc-700 text-zinc-200 border-zinc-600' },
    {
      value: 'success',
      label: 'Success',
      color: 'bg-green-600/20 text-green-400 border-green-600/30',
    },
    {
      value: 'warning',
      label: 'Warning',
      color: 'bg-yellow-600/20 text-yellow-400 border-yellow-600/30',
    },
    { value: 'info', label: 'Info', color: 'bg-blue-600/20 text-blue-400 border-blue-600/30' },
    { value: 'danger', label: 'Danger', color: 'bg-red-600/20 text-red-400 border-red-600/30' },
  ]

  const validateBadge = (index, field, value) => {
    const newErrors = { ...errors }
    const errorKey = `badge_${index}_${field}`

    if (field === 'text') {
      if (!value || value.trim().length === 0) {
        newErrors[errorKey] = 'Badge text cannot be empty'
      } else if (value.length > 50) {
        newErrors[errorKey] = 'Badge text must be 50 characters or less'
      } else {
        delete newErrors[errorKey]
      }
    }

    setErrors(newErrors)
    return !newErrors[errorKey]
  }

  const handleBadgeChange = (index, field, value) => {
    const newBadges = [...badgeList]
    newBadges[index] = { ...newBadges[index], [field]: value }
    setBadgeList(newBadges)

    // Validate the changed field
    if (field === 'text') {
      validateBadge(index, field, value)
    }

    // Filter out badges with empty text for the parent component
    const filteredBadges = newBadges.filter((badge) => badge.text && badge.text.trim().length > 0)
    if (onChange) {
      onChange(filteredBadges)
    }
  }

  const addBadge = () => {
    const newBadges = [...badgeList, { text: '', variant: 'primary' }]
    setBadgeList(newBadges)
  }

  const removeBadge = (index) => {
    if (badgeList.length <= 1) return // Keep at least one badge field

    const newBadges = badgeList.filter((_, i) => i !== index)
    setBadgeList(newBadges)

    // Remove errors for this index
    const newErrors = { ...errors }
    Object.keys(newErrors).forEach((key) => {
      if (key.startsWith(`badge_${index}_`)) {
        delete newErrors[key]
      }
    })

    // Reindex remaining errors
    const reindexedErrors = {}
    Object.keys(newErrors).forEach((key) => {
      if (key.startsWith('badge_')) {
        const parts = key.split('_')
        const oldIndex = parseInt(parts[1])
        const field = parts[2]

        if (oldIndex > index) {
          reindexedErrors[`badge_${oldIndex - 1}_${field}`] = newErrors[key]
        } else if (oldIndex < index) {
          reindexedErrors[key] = newErrors[key]
        }
      } else {
        reindexedErrors[key] = newErrors[key]
      }
    })
    setErrors(reindexedErrors)

    // Filter out badges with empty text for the parent component
    const filteredBadges = newBadges.filter((badge) => badge.text && badge.text.trim().length > 0)
    if (onChange) {
      onChange(filteredBadges)
    }
  }

  const duplicateBadge = (index) => {
    const badgeToDuplicate = { ...badgeList[index] }
    const newBadges = [...badgeList]
    newBadges.splice(index + 1, 0, badgeToDuplicate)
    setBadgeList(newBadges)
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-base font-medium text-white">Badges</h4>
        <Button size="sm" variant="secondary" onClick={addBadge}>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          Add Badge
        </Button>
      </div>

      <div className="space-y-4">
        {badgeList.map((badge, index) => (
          <div key={index} className="p-4 bg-zinc-900 border border-zinc-700 rounded-lg">
            <div className="flex items-start gap-4">
              {/* Badge Number */}
              <div className="flex-shrink-0 w-8 h-8 bg-amber-600/20 text-amber-400 rounded-full flex items-center justify-center text-sm font-medium">
                {index + 1}
              </div>

              {/* Badge Configuration */}
              <div className="flex-1 space-y-3">
                {/* Badge Text */}
                <div>
                  <label className="block text-sm font-medium text-white mb-2">Badge Text</label>
                  <input
                    type="text"
                    value={badge.text}
                    onChange={(e) => handleBadgeChange(index, 'text', e.target.value)}
                    placeholder="Enter badge text..."
                    className={`w-full px-3 py-2 bg-zinc-800 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors ${
                      errors[`badge_${index}_text`]
                        ? 'border-red-500 focus:border-red-400'
                        : 'border-zinc-700 focus:border-amber-500'
                    }`}
                  />
                  <div className="flex items-center justify-between mt-1">
                    {errors[`badge_${index}_text`] ? (
                      <p className="text-sm text-red-400">{errors[`badge_${index}_text`]}</p>
                    ) : (
                      <p className="text-sm text-zinc-500">{badge.text.length}/50 characters</p>
                    )}
                  </div>
                </div>

                {/* Badge Variant */}
                <div>
                  <label className="block text-sm font-medium text-white mb-2">Badge Style</label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {badgeVariants.map((variant) => (
                      <button
                        key={variant.value}
                        type="button"
                        onClick={() => handleBadgeChange(index, 'variant', variant.value)}
                        className={`p-2 rounded-lg border-2 transition-colors ${
                          badge.variant === variant.value
                            ? 'border-amber-500 bg-amber-500/10'
                            : 'border-zinc-700 hover:border-zinc-600'
                        }`}
                      >
                        <div
                          className={`inline-flex items-center px-2 py-1 text-xs font-medium border rounded ${variant.color}`}
                        >
                          {variant.label}
                        </div>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Badge Preview */}
                {badge.text && (
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">Preview</label>
                    <div className="p-3 bg-zinc-800 rounded-lg">
                      <Badge variant={badge.variant}>{badge.text}</Badge>
                    </div>
                  </div>
                )}
              </div>

              {/* Controls */}
              <div className="flex-shrink-0 flex flex-col gap-1">
                {/* Duplicate */}
                <button
                  type="button"
                  onClick={() => duplicateBadge(index)}
                  className="p-2 text-zinc-400 hover:text-white transition-colors"
                  title="Duplicate badge"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                    />
                  </svg>
                </button>

                {/* Remove */}
                <button
                  type="button"
                  onClick={() => removeBadge(index)}
                  disabled={badgeList.length <= 1}
                  className="p-2 text-red-400 hover:text-red-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  title="Remove badge"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* All Badges Preview */}
      {badgeList.some((badge) => badge.text && badge.text.trim().length > 0) && (
        <div className="p-4 bg-zinc-900 border border-zinc-700 rounded-lg">
          <h5 className="text-sm font-medium text-white mb-3">All Badges Preview</h5>
          <div className="flex flex-wrap gap-2">
            {badgeList
              .filter((badge) => badge.text && badge.text.trim().length > 0)
              .map((badge, index) => (
                <Badge key={index} variant={badge.variant}>
                  {badge.text}
                </Badge>
              ))}
          </div>
        </div>
      )}

      {/* Help Text */}
      <div className="text-sm text-zinc-500">
        <p>• Create badges to highlight key features or categories</p>
        <p>• Use different styles to create visual hierarchy</p>
        <p>• Duplicate badges to quickly create similar ones</p>
      </div>

      {/* Validation Summary */}
      {Object.keys(errors).length > 0 && (
        <div className="p-3 bg-red-600/10 border border-red-600/30 rounded-lg">
          <div className="flex items-start gap-2">
            <svg
              className="w-4 h-4 text-red-400 flex-shrink-0 mt-0.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <div>
              <p className="text-sm font-medium text-red-400 mb-1">Badge validation errors:</p>
              <ul className="text-sm text-red-300 space-y-1">
                {Object.values(errors).map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
