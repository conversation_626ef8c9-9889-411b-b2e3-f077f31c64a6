/**
 * ContactInfoForm - Component for editing business unit contact information
 */

import { useState } from 'react'
import Card from '../../components/ui/Card.jsx'

export default function ContactInfoForm({ contact, onChange }) {
  const [errors, setErrors] = useState({})

  const handleFieldChange = (field, value) => {
    const updatedContact = {
      ...contact,
      [field]: value,
    }
    onChange(updatedContact)

    // Clear field error if it exists
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: null }))
    }
  }

  const validateEmail = (email) => {
    if (!email) return true // Email is optional
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const validatePhone = (phone) => {
    if (!phone) return true // Phone is optional
    // Allow various phone formats: +62, 0, with/without spaces, dashes, parentheses
    const phoneRegex = /^[+]?[0-9\s\-()]{8,20}$/
    return phoneRegex.test(phone.replace(/\s/g, ''))
  }

  const validateContact = () => {
    const newErrors = {}

    // Validate address (optional but if provided should be reasonable length)
    if (contact.address && contact.address.trim().length > 500) {
      newErrors.address = 'Address must be 500 characters or less'
    }

    // Validate phone number format
    if (contact.phone && !validatePhone(contact.phone)) {
      newErrors.phone = 'Please enter a valid phone number'
    }

    // Validate email format
    if (contact.email && !validateEmail(contact.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    // Validate hours (optional but if provided should be reasonable length)
    if (contact.hours && contact.hours.trim().length > 200) {
      newErrors.hours = 'Hours must be 200 characters or less'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Validate on data changes
  useState(() => {
    validateContact()
  }, [contact])

  const formatPhoneNumber = (phone) => {
    // Simple phone number formatting for Indonesian numbers
    if (!phone) return ''

    // Remove all non-digit characters except +
    const cleaned = phone.replace(/[^\d+]/g, '')

    // If it starts with 0, suggest +62 format
    if (cleaned.startsWith('0')) {
      return '+62 ' + cleaned.substring(1)
    }

    return cleaned
  }

  const handlePhoneBlur = () => {
    if (contact.phone) {
      const formatted = formatPhoneNumber(contact.phone)
      if (formatted !== contact.phone) {
        handleFieldChange('phone', formatted)
      }
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-xl font-semibold text-white mb-2">Contact Information</h2>
        <p className="text-zinc-400">
          Manage contact details for this business unit. All fields are optional.
        </p>
      </div>

      {/* Contact Form */}
      <Card padding="lg">
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-white">Contact Details</h3>

          {/* Address */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Address</label>
            <textarea
              value={contact.address || ''}
              onChange={(e) => handleFieldChange('address', e.target.value)}
              placeholder="Enter the complete business address..."
              rows={3}
              className={`w-full px-3 py-2 bg-zinc-800 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors resize-vertical ${
                errors.address
                  ? 'border-red-500 focus:border-red-400'
                  : 'border-zinc-700 focus:border-amber-500'
              }`}
            />
            {errors.address && <p className="mt-1 text-sm text-red-400">{errors.address}</p>}
            <p className="mt-1 text-sm text-zinc-500">
              Full business address including street, city, postal code
            </p>
          </div>

          {/* Phone and Email */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-white mb-2">Phone Number</label>
              <input
                type="text"
                value={contact.phone || ''}
                onChange={(e) => handleFieldChange('phone', e.target.value)}
                onBlur={handlePhoneBlur}
                placeholder="+62 21 1234 5678"
                className={`w-full px-3 py-2 bg-zinc-800 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors ${
                  errors.phone
                    ? 'border-red-500 focus:border-red-400'
                    : 'border-zinc-700 focus:border-amber-500'
                }`}
              />
              {errors.phone && <p className="mt-1 text-sm text-red-400">{errors.phone}</p>}
              <p className="mt-1 text-sm text-zinc-500">Phone number with country code</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-white mb-2">Email Address</label>
              <input
                type="email"
                value={contact.email || ''}
                onChange={(e) => handleFieldChange('email', e.target.value)}
                placeholder="<EMAIL>"
                className={`w-full px-3 py-2 bg-zinc-800 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors ${
                  errors.email
                    ? 'border-red-500 focus:border-red-400'
                    : 'border-zinc-700 focus:border-amber-500'
                }`}
              />
              {errors.email && <p className="mt-1 text-sm text-red-400">{errors.email}</p>}
              <p className="mt-1 text-sm text-zinc-500">Business email address</p>
            </div>
          </div>

          {/* Operating Hours */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Operating Hours</label>
            <textarea
              value={contact.hours || ''}
              onChange={(e) => handleFieldChange('hours', e.target.value)}
              placeholder="Monday - Friday: 9:00 AM - 5:00 PM&#10;Saturday: 9:00 AM - 2:00 PM&#10;Sunday: Closed"
              rows={4}
              className={`w-full px-3 py-2 bg-zinc-800 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors resize-vertical ${
                errors.hours
                  ? 'border-red-500 focus:border-red-400'
                  : 'border-zinc-700 focus:border-amber-500'
              }`}
            />
            {errors.hours && <p className="mt-1 text-sm text-red-400">{errors.hours}</p>}
            <p className="mt-1 text-sm text-zinc-500">Business operating hours and days</p>
          </div>
        </div>
      </Card>

      {/* Contact Preview */}
      {(contact.address || contact.phone || contact.email || contact.hours) && (
        <Card padding="lg">
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-white">Contact Information Preview</h3>
            <div className="bg-zinc-900 rounded-lg p-6 border border-zinc-700">
              <div className="space-y-4">
                {contact.address && (
                  <div className="flex items-start gap-3">
                    <svg
                      className="w-5 h-5 text-amber-400 flex-shrink-0 mt-0.5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                    <div>
                      <h4 className="text-sm font-medium text-white mb-1">Address</h4>
                      <p className="text-sm text-zinc-300 whitespace-pre-line">{contact.address}</p>
                    </div>
                  </div>
                )}

                {contact.phone && (
                  <div className="flex items-start gap-3">
                    <svg
                      className="w-5 h-5 text-amber-400 flex-shrink-0 mt-0.5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                      />
                    </svg>
                    <div>
                      <h4 className="text-sm font-medium text-white mb-1">Phone</h4>
                      <p className="text-sm text-zinc-300">
                        <a
                          href={`tel:${contact.phone.replace(/\s/g, '')}`}
                          className="hover:text-amber-400 transition-colors"
                        >
                          {contact.phone}
                        </a>
                      </p>
                    </div>
                  </div>
                )}

                {contact.email && (
                  <div className="flex items-start gap-3">
                    <svg
                      className="w-5 h-5 text-amber-400 flex-shrink-0 mt-0.5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                      />
                    </svg>
                    <div>
                      <h4 className="text-sm font-medium text-white mb-1">Email</h4>
                      <p className="text-sm text-zinc-300">
                        <a
                          href={`mailto:${contact.email}`}
                          className="hover:text-amber-400 transition-colors"
                        >
                          {contact.email}
                        </a>
                      </p>
                    </div>
                  </div>
                )}

                {contact.hours && (
                  <div className="flex items-start gap-3">
                    <svg
                      className="w-5 h-5 text-amber-400 flex-shrink-0 mt-0.5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    <div>
                      <h4 className="text-sm font-medium text-white mb-1">Operating Hours</h4>
                      <p className="text-sm text-zinc-300 whitespace-pre-line">{contact.hours}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Validation Summary */}
      {Object.keys(errors).length > 0 && (
        <Card padding="lg">
          <div className="flex items-start gap-3">
            <svg
              className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <div>
              <h4 className="text-sm font-medium text-red-400 mb-2">
                Please fix the following errors:
              </h4>
              <ul className="text-sm text-red-300 space-y-1">
                {Object.entries(errors).map(([key, error]) => (
                  <li key={key}>• {error}</li>
                ))}
              </ul>
            </div>
          </div>
        </Card>
      )}

      {/* Help Text */}
      <Card padding="lg">
        <div className="space-y-3">
          <h3 className="text-sm font-medium text-white">Contact Information Tips</h3>
          <ul className="text-sm text-zinc-400 space-y-2">
            <li className="flex items-start gap-2">
              <span className="text-amber-400 mt-1">•</span>
              <span>
                All contact fields are optional - only fill in what's relevant for your business
                unit
              </span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-amber-400 mt-1">•</span>
              <span>
                Phone numbers will be automatically formatted with country code (+62 for Indonesia)
              </span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-amber-400 mt-1">•</span>
              <span>Email addresses will be validated for proper format</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-amber-400 mt-1">•</span>
              <span>
                Operating hours can include multiple lines for different days or special notes
              </span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-amber-400 mt-1">•</span>
              <span>The preview shows how contact information will appear to visitors</span>
            </li>
          </ul>
        </div>
      </Card>
    </div>
  )
}
