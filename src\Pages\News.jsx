/**
 * News - Halaman daftar berita publik
 */

import { useState } from 'react'
import Container from '../components/ui/Container.jsx'
import Section from '../components/ui/Section.jsx'
import NewsList from '../components/news/NewsList.jsx'
import Button from '../components/ui/Button.jsx'

export default function News() {
  const [searchQuery, setSearchQuery] = useState('')

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Bagian Hero */}
      <Section className="pt-24 pb-12">
        <Container>
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Berita & Pembaruan Terkini
            </h1>
            <p className="text-xl text-zinc-300 max-w-3xl mx-auto">
              Tetap terkini dengan berita terbaru, wawasan, dan perkembangan dari <PERSON>igi <PERSON> dan
              berbagai unit bisnis kami.
            </p>
          </div>

          {/* Bar Pencarian */}
          <div className="max-w-2xl mx-auto mb-12">
            <div className="relative">
              <input
                type="text"
                placeholder="Cari artikel berita..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-6 py-4 bg-zinc-900 border border-zinc-700 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:border-amber-500 pr-12"
              />
              <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                <svg
                  className="w-5 h-5 text-zinc-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            </div>
          </div>
        </Container>
      </Section>

      {/* Bagian Artikel Unggulan */}
      <Section className="py-12 bg-zinc-900/50">
        <Container>
          <div className="mb-8">
            <h2 className="text-3xl font-bold text-white mb-4">Artikel Unggulan</h2>
            <p className="text-zinc-400">Berita terpenting dan trending kami</p>
          </div>
          <NewsList featured={true} limit={3} />
        </Container>
      </Section>

      {/* Bagian Semua Artikel */}
      <Section className="py-12">
        <Container>
          <div className="mb-8">
            <h2 className="text-3xl font-bold text-white mb-4">Semua Berita</h2>
            <p className="text-zinc-400">Jelajahi semua artikel berita dan pembaruan kami</p>
          </div>
          <NewsList />
        </Container>
      </Section>

      {/* Pendaftaran Newsletter */}
      <Section className="py-12 bg-zinc-900/50">
        <Container>
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-white mb-4">Tetap Terkini</h2>
            <p className="text-zinc-400 mb-8">
              Berlangganan newsletter kami untuk mendapatkan berita dan pembaruan terbaru langsung
              ke kotak masuk Anda.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Masukkan email Anda"
                className="flex-1 px-4 py-3 bg-zinc-800 border border-zinc-700 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:border-amber-500"
              />
              <Button className="sm:w-auto">Berlangganan</Button>
            </div>
            <p className="text-xs text-zinc-500 mt-4">
              Kami menghormati privasi Anda. Berhenti berlangganan kapan saja.
            </p>
          </div>
        </Container>
      </Section>
    </div>
  )
}
