/**
 * SkeletonLoader - Skeleton loading components for better UX
 */

const SkeletonBox = ({ className = '' }) => (
  <div className={`animate-pulse bg-zinc-700 rounded ${className}`}></div>
)

const SkeletonText = ({ lines = 1, className = '' }) => (
  <div className={`space-y-2 ${className}`}>
    {Array.from({ length: lines }).map((_, index) => (
      <SkeletonBox key={index} className={`h-4 ${index === lines - 1 ? 'w-3/4' : 'w-full'}`} />
    ))}
  </div>
)

const BusinessUnitSkeleton = () => (
  <div className="animate-pulse">
    {/* Hero Section Skeleton */}
    <div className="text-center max-w-4xl mx-auto mb-16 py-20">
      <SkeletonBox className="h-8 w-48 mx-auto mb-6" />
      <SkeletonBox className="h-12 w-96 mx-auto mb-6" />
      <SkeletonText lines={3} className="max-w-2xl mx-auto" />
    </div>

    {/* About Section Skeleton */}
    <div className="grid md:grid-cols-2 gap-12 items-center mb-20">
      <SkeletonBox className="w-full h-64 rounded-2xl" />
      <div className="space-y-4">
        <SkeletonBox className="h-8 w-64" />
        <SkeletonText lines={4} />
        <div className="flex gap-3 mt-6">
          <SkeletonBox className="h-6 w-16 rounded-full" />
          <SkeletonBox className="h-6 w-20 rounded-full" />
          <SkeletonBox className="h-6 w-18 rounded-full" />
        </div>
      </div>
    </div>

    {/* Vision & Mission Skeleton */}
    <div className="bg-zinc-900/50 py-20">
      <div className="text-center mb-16">
        <SkeletonBox className="h-10 w-48 mx-auto mb-4" />
        <SkeletonText lines={2} className="max-w-lg mx-auto" />
      </div>

      <div className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
        <div className="bg-zinc-800 p-8 rounded-xl">
          <div className="flex items-center mb-6">
            <SkeletonBox className="w-12 h-12 rounded-full mr-4" />
            <SkeletonBox className="h-8 w-24" />
          </div>
          <SkeletonText lines={3} />
        </div>

        <div className="bg-zinc-800 p-8 rounded-xl">
          <div className="flex items-center mb-6">
            <SkeletonBox className="w-12 h-12 rounded-full mr-4" />
            <SkeletonBox className="h-8 w-24" />
          </div>
          <div className="space-y-3">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="flex items-start">
                <SkeletonBox className="w-6 h-6 rounded-full mr-3 mt-1 flex-shrink-0" />
                <SkeletonBox className="h-4 flex-1" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>

    {/* Products Section Skeleton */}
    <div className="py-20">
      <div className="text-center mb-16">
        <SkeletonBox className="h-10 w-64 mx-auto mb-4" />
        <SkeletonText lines={2} className="max-w-lg mx-auto" />
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="bg-zinc-800 rounded-xl overflow-hidden">
            <SkeletonBox className="h-48 w-full" />
            <div className="p-6">
              <SkeletonBox className="h-6 w-3/4 mb-3" />
              <SkeletonText lines={2} className="mb-4" />
              <div className="space-y-2">
                {Array.from({ length: 3 }).map((_, featureIndex) => (
                  <div key={featureIndex} className="flex items-center">
                    <SkeletonBox className="w-4 h-4 rounded-full mr-2 flex-shrink-0" />
                    <SkeletonBox className="h-3 flex-1" />
                  </div>
                ))}
              </div>
              <SkeletonBox className="h-8 w-24 mt-4 rounded-lg" />
            </div>
          </div>
        ))}
      </div>
    </div>

    {/* Statistics Section Skeleton */}
    <div className="bg-zinc-900/50 py-20">
      <div className="text-center mb-16">
        <SkeletonBox className="h-10 w-64 mx-auto mb-4" />
        <SkeletonText lines={2} className="max-w-lg mx-auto" />
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-6xl mx-auto">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="text-center">
            <SkeletonBox className="h-12 w-20 mx-auto mb-2" />
            <SkeletonBox className="h-4 w-16 mx-auto" />
          </div>
        ))}
      </div>
    </div>

    {/* Gallery Section Skeleton */}
    <div className="py-20">
      <div className="text-center mb-16">
        <SkeletonBox className="h-10 w-48 mx-auto mb-4" />
        <SkeletonText lines={2} className="max-w-lg mx-auto" />
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
        {Array.from({ length: 6 }).map((_, index) => (
          <SkeletonBox key={index} className="w-full h-64 rounded-xl" />
        ))}
      </div>
    </div>

    {/* Contact Section Skeleton */}
    <div className="bg-zinc-900/50 py-20">
      <div className="text-center mb-16">
        <SkeletonBox className="h-10 w-48 mx-auto mb-4" />
        <SkeletonText lines={2} className="max-w-lg mx-auto" />
      </div>

      <div className="grid md:grid-cols-2 gap-12 max-w-6xl mx-auto">
        <div className="bg-zinc-800 p-8 rounded-xl">
          <SkeletonBox className="h-6 w-40 mb-6" />
          <div className="space-y-4">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="flex items-start">
                <SkeletonBox className="w-12 h-12 rounded-full mr-4 flex-shrink-0" />
                <div className="flex-1">
                  <SkeletonBox className="h-4 w-20 mb-1" />
                  <SkeletonBox className="h-4 w-full" />
                </div>
              </div>
            ))}
          </div>
        </div>

        <SkeletonBox className="w-full h-64 rounded-xl" />
      </div>
    </div>
  </div>
)

export { BusinessUnitSkeleton }
