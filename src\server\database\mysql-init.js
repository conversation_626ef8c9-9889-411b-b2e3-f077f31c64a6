/**
 * MySQL Database Schema Initialization
 * Creates tables for business units, products, statistics, and gallery images
 */

import { executeQuery, createDatabase, testConnection } from './mysql-config.js'

/**
 * SQL statements for creating tables
 */
const createTablesSQL = {
  business_units: `
    CREATE TABLE IF NOT EXISTS business_units (
      id VARCHAR(36) PRIMARY KEY,
      name VARCHAR(255) NOT NULL,
      slug VARCHAR(100) UNIQUE NOT NULL,
      badge_text VARCHAR(255),
      badge_variant VARCHAR(50) DEFAULT 'primary',
      badge_icon VARCHAR(255),
      description TEXT,
      theme_color_from VARCHAR(7),
      theme_color_to VARCHAR(7),
      theme_color_primary VARCHAR(7),
      layout_config JSON,
      about_image VARCHAR(500),
      about_content TEXT,
      highlight_text TEXT,
      highlight_class VARCHAR(255),
      vision TEXT,
      mission JSON,
      badges JSON,
      contact_address TEXT,
      contact_phone VARCHAR(50),
      contact_email VARCHAR(255),
      contact_hours VARCHAR(255),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_slug (slug),
      INDEX idx_name (name),
      INDEX idx_updated_at (updated_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
  `,

  products: `
    CREATE TABLE IF NOT EXISTS products (
      id VARCHAR(36) PRIMARY KEY,
      business_unit_id VARCHAR(36) NOT NULL,
      title VARCHAR(255) NOT NULL,
      description TEXT,
      image VARCHAR(500),
      price VARCHAR(100),
      features JSON,
      sort_order INT DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (business_unit_id) REFERENCES business_units (id) ON DELETE CASCADE,
      INDEX idx_business_unit_sort (business_unit_id, sort_order),
      INDEX idx_business_unit (business_unit_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
  `,

  statistics: `
    CREATE TABLE IF NOT EXISTS statistics (
      id VARCHAR(36) PRIMARY KEY,
      business_unit_id VARCHAR(36) NOT NULL,
      title VARCHAR(255),
      description TEXT,
      items JSON,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (business_unit_id) REFERENCES business_units (id) ON DELETE CASCADE,
      INDEX idx_business_unit (business_unit_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
  `,

  gallery_images: `
    CREATE TABLE IF NOT EXISTS gallery_images (
      id VARCHAR(36) PRIMARY KEY,
      business_unit_id VARCHAR(36) NOT NULL,
      src VARCHAR(500) NOT NULL,
      alt VARCHAR(255),
      title VARCHAR(255),
      description TEXT,
      sort_order INT DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (business_unit_id) REFERENCES business_units (id) ON DELETE CASCADE,
      INDEX idx_business_unit_sort (business_unit_id, sort_order),
      INDEX idx_business_unit (business_unit_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
  `,
}

/**
 * Initialize MySQL database schema
 */
export async function initializeBusinessUnitsSchema() {
  try {
    console.log('🚀 Initializing MySQL database schema...')

    // Test connection first
    const connectionOk = await testConnection()
    if (!connectionOk) {
      // Try to create database if connection fails
      await createDatabase()
    }

    // Create tables in order (business_units first due to foreign key constraints)
    const tableOrder = ['business_units', 'products', 'statistics', 'gallery_images']

    for (const tableName of tableOrder) {
      console.log(`📋 Creating table: ${tableName}`)
      await executeQuery(createTablesSQL[tableName])
      console.log(`✅ Table '${tableName}' created successfully`)
    }

    console.log('🎉 MySQL schema initialization completed successfully!')
    return true
  } catch (error) {
    console.error('❌ Schema initialization failed:', error)
    throw error
  }
}

/**
 * Verify that all required tables exist
 */
export async function verifySchema() {
  try {
    console.log('🔍 Verifying MySQL database schema...')

    const requiredTables = ['business_units', 'products', 'statistics', 'gallery_images']
    const results = {}

    for (const tableName of requiredTables) {
      try {
        const tableInfo = await executeQuery(
          `
          SELECT COUNT(*) as count 
          FROM information_schema.tables 
          WHERE table_schema = DATABASE() 
          AND table_name = ?
        `,
          [tableName]
        )

        results[tableName] = tableInfo[0].count > 0

        if (results[tableName]) {
          // Get column count for additional verification
          const columnInfo = await executeQuery(
            `
            SELECT COUNT(*) as column_count 
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
            AND table_name = ?
          `,
            [tableName]
          )

          console.log(`✅ Table '${tableName}' exists with ${columnInfo[0].column_count} columns`)
        } else {
          console.log(`❌ Table '${tableName}' does not exist`)
        }
      } catch (error) {
        console.error(`❌ Error checking table '${tableName}':`, error.message)
        results[tableName] = false
      }
    }

    const allTablesExist = Object.values(results).every((exists) => exists)

    if (allTablesExist) {
      console.log('✅ All required tables exist')
    } else {
      console.log('❌ Some required tables are missing')
    }

    return results
  } catch (error) {
    console.error('❌ Schema verification failed:', error)
    throw error
  }
}

/**
 * Drop all business unit tables (for testing/reset purposes)
 */
export async function dropAllTables() {
  try {
    console.log('🗑️ Dropping all business unit tables...')

    // Drop in reverse order due to foreign key constraints
    const dropOrder = ['gallery_images', 'statistics', 'products', 'business_units']

    for (const tableName of dropOrder) {
      try {
        await executeQuery(`DROP TABLE IF EXISTS ${tableName}`)
        console.log(`✅ Table '${tableName}' dropped`)
      } catch (error) {
        console.error(`❌ Failed to drop table '${tableName}':`, error.message)
      }
    }

    console.log('✅ All tables dropped successfully')
    return true
  } catch (error) {
    console.error('❌ Failed to drop tables:', error)
    throw error
  }
}

/**
 * Get table statistics
 */
export async function getTableStats() {
  try {
    const tables = ['business_units', 'products', 'statistics', 'gallery_images']
    const stats = {}

    for (const tableName of tables) {
      try {
        const result = await executeQuery(`SELECT COUNT(*) as count FROM ${tableName}`)
        stats[tableName] = result[0].count
      } catch {
        stats[tableName] = 0
      }
    }

    return stats
  } catch (error) {
    console.error('❌ Failed to get table statistics:', error)
    return {}
  }
}
