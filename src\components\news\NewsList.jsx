/**
 * NewsList - Komponen daftar berita untuk publik
 */

import { useState, useEffect, useCallback } from 'react'
import { Link } from 'react-router-dom'
import Card from '../ui/Card.jsx'
import Badge from '../ui/Badge.jsx'
import Button from '../ui/Button.jsx'
import newsService from '../../services/NewsService.js'

export default function NewsList({ featured = false, limit = null, categoryId = null }) {
  const [articles, setArticles] = useState([])
  const [categories, setCategories] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState(categoryId || 'all')

  const loadData = useCallback(() => {
    setLoading(true)
    try {
      let articlesData

      if (featured) {
        articlesData = newsService.getFeaturedArticles()
      } else if (categoryId && categoryId !== 'all') {
        articlesData = newsService.getArticlesByCategory(categoryId)
      } else {
        articlesData = newsService.getPublishedArticles()
      }

      // Urutkan berdasarkan tanggal publikasi (terbaru dulu)
      articlesData.sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt))

      // Terapkan batas jika ditentukan
      if (limit) {
        articlesData = articlesData.slice(0, limit)
      }

      const categoriesData = newsService.getCategories()

      setArticles(articlesData)
      setCategories(categoriesData)
    } catch (error) {
      console.error('Error memuat berita:', error)
    } finally {
      setLoading(false)
    }
  }, [featured, limit, categoryId])

  useEffect(() => {
    loadData()
  }, [loadData])

  const handleCategoryChange = (categoryId) => {
    setSelectedCategory(categoryId)
    setLoading(true)

    try {
      let articlesData

      if (categoryId === 'all') {
        articlesData = newsService.getPublishedArticles()
      } else {
        articlesData = newsService.getArticlesByCategory(categoryId)
      }

      articlesData.sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt))

      if (limit) {
        articlesData = articlesData.slice(0, limit)
      }

      setArticles(articlesData)
    } catch (error) {
      console.error('Error memfilter artikel:', error)
    } finally {
      setLoading(false)
    }
  }

  const getCategoryName = (categoryId) => {
    const category = categories.find((cat) => cat.id === categoryId)
    return category ? category.name : 'Tanpa Kategori'
  }

  const getCategoryColor = (categoryId) => {
    const category = categories.find((cat) => cat.id === categoryId)
    return category ? category.color : '#6B7280'
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  const truncateText = (text, maxLength = 150) => {
    if (text.length <= maxLength) return text
    return text.substr(0, maxLength) + '...'
  }

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="w-8 h-8 border-4 border-amber-600 border-t-transparent rounded-full animate-spin mx-auto"></div>
        <p className="text-zinc-400 mt-4">Memuat berita...</p>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Filter Kategori (hanya tampil jika tidak dibatasi kategori tertentu) */}
      {!categoryId && categories.length > 0 && (
        <div className="flex flex-wrap gap-2">
          <Button
            variant={selectedCategory === 'all' ? 'primary' : 'secondary'}
            size="sm"
            onClick={() => handleCategoryChange('all')}
          >
            Semua Berita
          </Button>
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? 'primary' : 'secondary'}
              size="sm"
              onClick={() => handleCategoryChange(category.id)}
            >
              {category.name}
            </Button>
          ))}
        </div>
      )}

      {/* Grid Artikel */}
      {articles.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-zinc-400">Tidak ada artikel berita ditemukan</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {articles.map((article) => (
            <Card key={article.id} className="group hover:border-amber-500 transition-colors">
              <div className="p-6">
                {/* Badge Kategori */}
                <div className="flex items-center justify-between mb-3">
                  <Badge
                    variant="secondary"
                    style={{
                      backgroundColor: getCategoryColor(article.categoryId) + '20',
                      color: getCategoryColor(article.categoryId),
                    }}
                  >
                    {getCategoryName(article.categoryId)}
                  </Badge>
                  {article.featured && (
                    <Badge variant="warning" size="sm">
                      Unggulan
                    </Badge>
                  )}
                </div>

                {/* Judul */}
                <h3 className="text-xl font-semibold text-white mb-3 group-hover:text-amber-400 transition-colors">
                  <Link to={`/news/${article.slug}`}>{article.title}</Link>
                </h3>

                {/* Ringkasan */}
                <p className="text-zinc-400 mb-4 leading-relaxed">
                  {truncateText(article.excerpt)}
                </p>

                {/* Informasi Meta */}
                <div className="flex items-center justify-between text-sm text-zinc-500">
                  <span>Oleh {article.author}</span>
                  <span>{formatDate(article.publishedAt)}</span>
                </div>

                {/* Tag */}
                {article.tags && article.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-3">
                    {article.tags.slice(0, 3).map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 text-xs bg-zinc-800 text-zinc-400 rounded"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>
                )}

                {/* Link Baca Selengkapnya */}
                <div className="mt-4">
                  <Link
                    to={`/news/${article.slug}`}
                    className="text-amber-400 hover:text-amber-300 text-sm font-medium transition-colors"
                  >
                    Baca Selengkapnya →
                  </Link>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Tombol Muat Lebih Banyak (jika ada artikel lainnya) */}
      {!limit && articles.length > 0 && (
        <div className="text-center">
          <Button variant="secondary">Muat Artikel Lainnya</Button>
        </div>
      )}
    </div>
  )
}
