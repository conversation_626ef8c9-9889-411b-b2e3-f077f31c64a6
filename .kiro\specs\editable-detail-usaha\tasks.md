# Implementation Plan

- [x] 1. Set up MySQL database schema and data migration
  - Create MySQL database connection configuration and connection pool setup
  - Create database tables for business units, products, statistics, and gallery images with proper MySQL data types
  - Implement database initialization script with proper indexes, constraints, and JSON column support
  - Create data migration script to convert existing static data files to MySQL database records
  - _Requirements: 1.1, 2.1, 6.1_

- [x] 2. Create data service layer and API endpoints
  - [x] 2.1 Implement BusinessUnitService class with CRUD operations
    - Write BusinessUnitService with methods for getAllBusinessUnits, getBusinessUnit, createBusinessUnit, updateBusinessUnit, deleteBusinessUnit
    - Add methods for managing products, statistics, and gallery data
    - Implement data validation and error handling in service layer
    - _Requirements: 2.2, 2.3, 2.4, 6.1_

  - [x] 2.2 Create API endpoints for business unit management
    - Add GET /api/business-units endpoint to list all business units
    - Add GET /api/business-units/:slug endpoint to get specific business unit data
    - Add POST /api/business-units endpoint to create new business unit
    - Add PUT /api/business-units/:id endpoint to update business unit
    - Add DELETE /api/business-units/:id endpoint to delete business unit

    - _Requirements: 1.2, 2.1, 2.2, 2.3, 2.4_

  - [x] 2.3 Create API endpoints for products and content management
    - Add GET /api/business-units/:id/products endpoint for product management
    - Add POST /api/business-units/:id/products endpoint to add products
    - Add PUT /api/products/:id endpoint to update individual products
    - Add DELETE /api/products/:id endpoint to remove products
    - Add PUT /api/business-units/:id/statistics endpoint for statistics management
    - Add gallery management endpoints for image operations
    - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5, 5.1, 5.2, 5.3, 5.5_

- [x] 3. Create admin UI components for business unit management
  - [x] 3.1 Implement BusinessUnitsList component
    - Create list view component showing all business units with name, badge, and last modified date
    - Add search and filter functionality for business units
    - Implement edit and delete action buttons for each business unit
    - Add empty state handling when no business units exist
    - _Requirements: 1.1, 1.2, 1.3, 1.4_

  - [x] 3.2 Create BusinessUnitEditor main component
    - Implement tabbed interface for different editing sections (Basic Info, About, Products, Statistics, Gallery, Contact)
    - Add form state management and validation
    - Implement auto-save functionality with unsaved changes warning
    - Create cancel and save actions with proper state handling
    - _Requirements: 2.1, 2.2, 2.3, 2.4, 6.4_

  - [x] 3.3 Build basic information editing forms
    - Create form fields for name, description, badge text, and theme colors
    - Implement real-time validation with error display
    - Add color picker component for theme color selection
    - Create badge variant selector with preview
    - _Requirements: 2.1, 2.2, 2.3, 2.4_

- [x] 4. Implement about section and content editing
  - [x] 4.1 Create about section editing form
    - Add textarea/rich text editor for about content editing
    - Implement vision and mission statement editing fields
    - Create highlight text and CSS class configuration
    - Add about image URL field with preview
    - _Requirements: 3.1, 3.2, 3.3, 3.4_

  - [x] 4.2 Implement mission statements management
    - Create dynamic list component for adding/removing mission items
    - Add drag-and-drop or up/down controls for reordering mission statements
    - Implement validation for required mission fields
    - _Requirements: 3.1, 3.2_

- - [x] 4.3 Create badges management interface
    - Implement dynamic badges list with add/remove functionality
    - Create badge text and variant editing controls
    - Add preview of how badges will appear
    - _Requirements: 3.5_

- [x] 5. Build products management system
  - [x] 5.1 Create ProductManager component
    - Implement dynamic product list with add/edit/remove functionality
    - Create product form with title, description, price, and image fields
    - Add features list management for each product
    - Implement drag-and-drop reordering for products
    - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

  - [x] 5.2 Implement product features editing
    - Create dynamic features list component for each product
    - Add functionality to add, edit, and remove individual features
    - Implement validation for feature text requirements
    - _Requirements: 4.2, 4.3_

  - [x] 5.3 Add product reordering functionality
    - Implement drag-and-drop interface for product reordering
    - Add up/down arrow controls as alternative to drag-and-drop
    - Create API integration for saving new product order
    - _Requirements: 4.5_

- [x] 6. Create statistics and gallery management
  - [x] 6.1 Implement statistics editing interface
    - Create form fields for statistics title and description
    - Implement dynamic statistics items list with value and label fields
    - Add validation for statistics data format
    - _Requirements: 5.1, 5.2_

  - [x] 6.2 Build gallery management system
    - Create gallery images list with add/remove functionality
    - Implement image upload or URL input for gallery images
    - Add title and description fields for each gallery image
    - Create image reordering functionality
    - _Requirements: 5.3, 5.4_

  - [x] 6.3 Create contact information editing
    - Add form fields for address, phone, email, and hours
    - Implement validation for email and phone number formats
    - Create contact information preview display
    - _Requirements: 5.5_

-

- [x] 7. Implement preview and publishing system
  - [x] 7.1 Create preview functionality
    - Build PreviewModal component that renders business unit using DetailUsahaTemplate
    - Implement preview with unsaved changes applied
    - Add clear indication that user is in preview mode
    - Create responsive preview modes for different screen sizes
    - _Requirements: 7.1, 7.2, 7.3_

  - [x] 7.2 Add publishing workflow
    - Implement publish button in preview interface
    - Create confirmation dialog for publishing changes
    - Add return to edit mode functionality from preview
    - Implement success/error messaging for publish operations
    - _Requirements: 7.4, 7.5_

- [x] 8. Update DetailUsahaTemplate for dynamic data
  - [x] 8.1 Modify DetailUsahaTemplate to use API data
    - Update component to fetch data from API instead of static imports
    - Implement loading states while fetching business unit data
    - Add error handling for failed API requests
    - Maintain backward compatibility with existing static data structure
    - _Requirements: 6.1, 6.2, 6.3_

  - [x] 8.2 Simplify data loading (caching removed for simplicity)
    - Remove complex caching implementations
    - Keep basic loading indicators and skeleton screens

    - _Requirements: 6.2_

- [x] 9. Add admin navigation and routing
  - [x] 9.1 Integrate business units management into admin layout
    - Add "Business Units" navigation item to admin sidebar
    - Create routing for business units list and editor pages
    - Update AdminLayout to include new routes
    - _Requirements: 1.1_

  - [x] 9.2 Update admin dashboard with business units overview
    - Add business units summary to admin dashboard
    - Create quick action buttons for managing business units
    - Display recent business unit updates
    - _Requirements: 1.1_

- [ ] 10. Implement comprehensive testing
  - [ ] 10.1 Create unit tests for data services
    - Write tests for BusinessUnitService CRUD operations
    - Test data validation and error handling
    - Create tests for data transformation functions
    - _Requirements: 2.2, 2.3, 2.4_

  - [ ] 10.2 Add integration tests for API endpoints
    - Test all business unit API endpoints with various scenarios
    - Create tests for authentication and authorization
    - Test error handling and edge cases
    - _Requirements: 1.2, 2.1, 4.1, 5.1_

  - [ ] 10.3 Create component tests for admin UI
    - Test form interactions and validation
    - Create tests for state management and data flow
    - Test preview functionality and publishing workflow
    - _Requirements: 2.4, 7.1, 7.4_
