/**
 * FallbackDataService - Provides placeholder data when database is not available
 */

class FallbackDataService {
  constructor() {
    this.placeholderData = new Map()
    this.initializePlaceholderData()
  }

  /**
   * Initialize placeholder data for known business units
   */
  initializePlaceholderData() {
    const businessUnits = [
      { slug: 'pb<PERSON><PERSON>', name: '<PERSON><PERSON><PERSON>', badgeText: '<PERSON>', icon: '🏸' },
      { slug: 'ciginet', name: 'Cigi Net', badgeText: 'Unit Usaha Internet', icon: '🌐' },
      { slug: 'cigimart', name: 'Cigi Mart', badgeText: 'Unit Usaha Retail', icon: '🛒' },
      { slug: 'cigifood', name: '<PERSON><PERSON>', badgeText: 'Unit Usaha Kuliner', icon: '🍽️' },
      { slug: 'cigifc', name: '<PERSON><PERSON>', badgeText: '<PERSON> Bola', icon: '⚽' },
      { slug: 'cigifarm', name: '<PERSON><PERSON> <PERSON>', badgeText: 'Unit Usaha Pertanian', icon: '🌾' },
      { slug: 'cigiarchery', name: '<PERSON><PERSON>', badgeText: '<PERSON>', icon: '🏹' },
    ]

    businessUnits.forEach(unit => {
      this.placeholderData.set(unit.slug, this.createPlaceholderData(unit.slug, unit.name, unit.badgeText, unit.icon))
    })

    console.log(`[FallbackDataService] Initialized ${this.placeholderData.size} placeholder business units`)
  }

  /**
   * Create placeholder data for business units when database is unavailable
   */
  createPlaceholderData(slug, name, badgeText = 'Unit Usaha', badgeIcon = '🏢') {
    return {
      name,
      badgeText,
      badgeVariant: 'primary',
      badgeIcon,
      description: `${name} adalah unit usaha yang berkomitmen memberikan pelayanan terbaik kepada masyarakat.`,
      themeColor: {
        from: '#3b82f6',
        to: '#1d4ed8',
        primary: '#2563eb',
      },
      layoutConfig: {
        productsLayout: 'grid',
      },
      aboutImage: 'https://picsum.photos/600/400',
      aboutContent: `${name} adalah unit usaha yang berkomitmen memberikan pelayanan terbaik kepada masyarakat. Data ini akan dimuat dari database setelah sistem tersedia.`,
      highlightText: name,
      highlightClass: 'text-blue-400 font-semibold',
      vision: `Menjadi ${name} terdepan yang memberikan nilai terbaik bagi masyarakat`,
      mission: [
        'Memberikan pelayanan berkualitas tinggi',
        'Mengembangkan inovasi berkelanjutan',
        'Memberdayakan masyarakat lokal',
      ],
      badges: [
        { text: 'Terpercaya', variant: 'primary' },
        { text: 'Berkualitas', variant: 'success' },
      ],
      products: {
        title: `Produk & Layanan ${name}`,
        description: 'Produk dan layanan berkualitas tinggi',
        items: [],
        cta: { text: 'Lihat Semua Produk' },
      },
      statistics: {
        title: 'Pencapaian Kami',
        description: 'Data pencapaian akan dimuat dari database',
        items: [],
      },
      gallery: {
        title: 'Galeri',
        description: 'Dokumentasi kegiatan akan dimuat dari database',
        images: [],
      },
      contact: {
        address: 'Desa Cimande Girang, Kecamatan Caringin, Kabupaten Bogor, Jawa Barat',
        phone: '+62 812-3456-7890',
        email: `info@${slug}.com`,
        hours: 'Senin - Sabtu: 08:00 - 17:00 WIB',
      },
    }
  }

  /**
   * Get business unit data by slug (fallback only)
   */
  async getBusinessUnit(slug) {
    const data = this.placeholderData.get(slug)
    if (!data) {
      throw new Error(`Business unit '${slug}' not found in fallback data`)
    }

    console.warn(`[FallbackDataService] Using placeholder data for ${slug} - database unavailable`)
    return data
  }

  /**
   * Get all business units (fallback only)
   */
  async getAllBusinessUnits() {
    console.warn('[FallbackDataService] Using placeholder data - database unavailable')
    
    return Array.from(this.placeholderData.entries()).map(([slug, data]) => ({
      id: slug,
      name: data.name,
      slug,
      badgeText: data.badgeText,
      badgeVariant: data.badgeVariant,
      badgeIcon: data.badgeIcon,
      description: data.description,
      lastModified: new Date().toISOString(),
    }))
  }

  /**
   * Check if business unit exists in fallback data
   */
  hasBusinessUnit(slug) {
    return this.placeholderData.has(slug)
  }

  /**
   * Get available business unit slugs from fallback data
   */
  getAvailableSlugs() {
    return Array.from(this.placeholderData.keys())
  }
}

// Create and export singleton instance
const fallbackDataService = new FallbackDataService()
export default fallbackDataService