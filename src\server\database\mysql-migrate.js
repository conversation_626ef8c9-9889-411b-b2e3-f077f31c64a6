/**
 * MySQL Data Migration Script
 * Converts existing static data files to MySQL database records
 */

import { executeQuery } from './mysql-config.js'
import { v4 as uuidv4 } from 'uuid'
import { fileURLToPath } from 'url'
import { dirname } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

/**
 * Import static data from existing files
 */
async function importStaticData() {
  try {
    // Import business unit data files
    const businessUnits = []

    // List of business units to migrate
    const unitSlugs = [
      'cigifarm',
      'ciginet',
      'cigimart',
      'cigifood',
      'pbcigi',
      'cigiarchery',
      'cigifc',
    ]

    for (const slug of unitSlugs) {
      try {
        // Dynamic import of data files - use absolute path from project root
        const dataPath = `../../../src/Pages/Detailusaha/${slug}/data.js`
        const module = await import(dataPath)

        // Extract the data - it's a named export following the pattern {slug}Data
        const expectedExportName = `${slug}Data`
        let unitData = module[expectedExportName]

        if (!unitData) {
          // Try to find the data by looking for exports that match the slug pattern
          const possibleKeys = Object.keys(module).filter(
            (key) =>
              key.toLowerCase().includes(slug.toLowerCase()) && key.toLowerCase().includes('data')
          )
          if (possibleKeys.length > 0) {
            unitData = module[possibleKeys[0]]
          }
        }

        if (unitData) {
          console.log(`✅ Successfully imported data for ${slug}`)
          businessUnits.push({
            slug,
            data: unitData,
          })
        } else {
          throw new Error('No data found in module')
        }
      } catch (error) {
        console.warn(`⚠️ Could not import data for ${slug}:`, error.message)

        // Create placeholder data based on the structure we know
        const placeholderData = createPlaceholderData(slug)
        businessUnits.push({
          slug,
          data: placeholderData,
        })
      }
    }

    return businessUnits
  } catch (error) {
    console.error('❌ Failed to import static data:', error)
    throw error
  }
}

/**
 * Create placeholder data for units that couldn't be imported
 */
function createPlaceholderData(slug) {
  const unitNames = {
    cigifarm: 'Cigi Farm',
    ciginet: 'Cigi Net',
    cigimart: 'Cigi Mart',
    cigifood: 'Cigi Food',
    pbcigi: 'PB Cigi',
    cigiarchery: 'Cigi Archery',
    cigifc: 'Cigi FC',
  }

  const unitDescriptions = {
    cigifarm: 'Peternakan ayam petelur dan budidaya ikan untuk ketahanan pangan desa',
    ciginet: 'Layanan internet desa dan solusi digital untuk konektivitas masyarakat',
    cigimart: 'Supermarket lokal menyediakan sembako dan kebutuhan harian warga',
    cigifood: 'Pengolahan dan distribusi produk makanan berkualitas tinggi',
    pbcigi: 'Perkumpulan bulutangkis untuk mengembangkan bakat olahraga di komunitas',
    cigiarchery: 'Klub panahan untuk melatih konsentrasi dan ketepatan dalam olahraga tradisional',
    cigifc: 'Tim sepak bola komunitas untuk membangun semangat sportivitas dan kerjasama',
  }

  return {
    name: unitNames[slug] || slug.toUpperCase(),
    badgeText: 'Unit Usaha',
    badgeVariant: 'primary',
    badgeIcon: '🏢',
    description: unitDescriptions[slug] || `Deskripsi untuk ${slug}`,
    themeColor: {
      from: '#3b82f6',
      to: '#1d4ed8',
      primary: '#2563eb',
    },
    layoutConfig: {
      productsLayout: 'grid',
    },
    aboutImage: 'https://picsum.photos/600/400',
    aboutContent: `${unitNames[slug] || slug} adalah unit usaha yang berkomitmen memberikan pelayanan terbaik.`,
    highlightText: unitNames[slug] || slug,
    highlightClass: 'text-blue-400 font-semibold',
    vision: `Menjadi ${slug} terdepan yang memberikan nilai terbaik bagi masyarakat`,
    mission: [
      'Memberikan pelayanan berkualitas tinggi',
      'Mengembangkan inovasi berkelanjutan',
      'Memberdayakan masyarakat lokal',
    ],
    badges: [
      { text: 'Terpercaya', variant: 'primary' },
      { text: 'Berkualitas', variant: 'success' },
    ],
    products: {
      title: `Produk ${unitNames[slug] || slug}`,
      description: 'Produk dan layanan berkualitas tinggi',
      items: [],
      cta: { text: 'Lihat Semua Produk' },
    },
    statistics: {
      title: 'Statistik',
      description: 'Pencapaian kami',
      items: [],
    },
    gallery: {
      title: 'Galeri',
      description: 'Dokumentasi kegiatan',
      images: [],
    },
    contact: {
      address: 'Desa Cimande Girang, Kecamatan Caringin, Kabupaten Bogor, Jawa Barat',
      phone: '+62 812-3456-7890',
      email: `info@${slug}.com`,
      hours: 'Senin - Sabtu: 08:00 - 17:00 WIB',
    },
  }
}

/**
 * Transform static data to database format
 */
function transformBusinessUnitData(slug, data) {
  const id = uuidv4()

  return {
    id,
    name: data.name || slug,
    slug: slug,
    badge_text: data.badgeText || '',
    badge_variant: data.badgeVariant || 'primary',
    badge_icon: data.badgeIcon || '',
    description: data.description || '',
    theme_color_from: data.themeColor?.from || '#3b82f6',
    theme_color_to: data.themeColor?.to || '#1d4ed8',
    theme_color_primary: data.themeColor?.primary || '#2563eb',
    layout_config: JSON.stringify(data.layoutConfig || { productsLayout: 'grid' }),
    about_image: data.aboutImage || '',
    about_content: data.aboutContent || '',
    highlight_text: data.highlightText || '',
    highlight_class: data.highlightClass || '',
    vision: data.vision || '',
    mission: JSON.stringify(data.mission || []),
    badges: JSON.stringify(data.badges || []),
    contact_address: data.contact?.address || '',
    contact_phone: data.contact?.phone || '',
    contact_email: data.contact?.email || '',
    contact_hours: data.contact?.hours || '',
  }
}

/**
 * Transform products data
 */
function transformProductsData(businessUnitId, productsData) {
  if (!productsData?.items || !Array.isArray(productsData.items)) {
    return []
  }

  return productsData.items.map((product, index) => ({
    id: uuidv4(),
    business_unit_id: businessUnitId,
    title: product.title || '',
    description: product.description || '',
    image: product.image || '',
    price: product.price || '',
    features: JSON.stringify(product.features || []),
    sort_order: index,
  }))
}

/**
 * Transform statistics data
 */
function transformStatisticsData(businessUnitId, statisticsData) {
  if (!statisticsData) return null

  return {
    id: uuidv4(),
    business_unit_id: businessUnitId,
    title: statisticsData.title || '',
    description: statisticsData.description || '',
    items: JSON.stringify(statisticsData.items || []),
  }
}

/**
 * Transform gallery data
 */
function transformGalleryData(businessUnitId, galleryData) {
  if (!galleryData?.images || !Array.isArray(galleryData.images)) {
    return []
  }

  return galleryData.images.map((image, index) => ({
    id: uuidv4(),
    business_unit_id: businessUnitId,
    src: image.src || '',
    alt: image.alt || '',
    title: image.title || '',
    description: image.description || '',
    sort_order: index,
  }))
}

/**
 * Migrate business units data to MySQL
 */
export async function migrateBusinessUnits() {
  try {
    console.log('📦 Starting business units migration to MySQL...')

    // Import static data
    const businessUnits = await importStaticData()
    console.log(`📋 Found ${businessUnits.length} business units to migrate`)

    let migratedCount = 0

    for (const { slug, data } of businessUnits) {
      try {
        console.log(`🔄 Migrating ${slug}...`)

        // Transform business unit data
        const businessUnit = transformBusinessUnitData(slug, data)

        // Insert business unit
        await executeQuery(
          `
          INSERT INTO business_units (
            id, name, slug, badge_text, badge_variant, badge_icon, description,
            theme_color_from, theme_color_to, theme_color_primary, layout_config,
            about_image, about_content, highlight_text, highlight_class,
            vision, mission, badges, contact_address, contact_phone, contact_email, contact_hours
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
            name = VALUES(name),
            badge_text = VALUES(badge_text),
            badge_variant = VALUES(badge_variant),
            badge_icon = VALUES(badge_icon),
            description = VALUES(description),
            theme_color_from = VALUES(theme_color_from),
            theme_color_to = VALUES(theme_color_to),
            theme_color_primary = VALUES(theme_color_primary),
            layout_config = VALUES(layout_config),
            about_image = VALUES(about_image),
            about_content = VALUES(about_content),
            highlight_text = VALUES(highlight_text),
            highlight_class = VALUES(highlight_class),
            vision = VALUES(vision),
            mission = VALUES(mission),
            badges = VALUES(badges),
            contact_address = VALUES(contact_address),
            contact_phone = VALUES(contact_phone),
            contact_email = VALUES(contact_email),
            contact_hours = VALUES(contact_hours),
            updated_at = CURRENT_TIMESTAMP
        `,
          [
            businessUnit.id,
            businessUnit.name,
            businessUnit.slug,
            businessUnit.badge_text,
            businessUnit.badge_variant,
            businessUnit.badge_icon,
            businessUnit.description,
            businessUnit.theme_color_from,
            businessUnit.theme_color_to,
            businessUnit.theme_color_primary,
            businessUnit.layout_config,
            businessUnit.about_image,
            businessUnit.about_content,
            businessUnit.highlight_text,
            businessUnit.highlight_class,
            businessUnit.vision,
            businessUnit.mission,
            businessUnit.badges,
            businessUnit.contact_address,
            businessUnit.contact_phone,
            businessUnit.contact_email,
            businessUnit.contact_hours,
          ]
        )

        // Clear existing related data for this business unit
        await executeQuery('DELETE FROM products WHERE business_unit_id = ?', [businessUnit.id])
        await executeQuery('DELETE FROM statistics WHERE business_unit_id = ?', [businessUnit.id])
        await executeQuery('DELETE FROM gallery_images WHERE business_unit_id = ?', [
          businessUnit.id,
        ])

        // Migrate products
        const products = transformProductsData(businessUnit.id, data.products)
        for (const product of products) {
          await executeQuery(
            `
            INSERT INTO products (id, business_unit_id, title, description, image, price, features, sort_order)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
          `,
            [
              product.id,
              product.business_unit_id,
              product.title,
              product.description,
              product.image,
              product.price,
              product.features,
              product.sort_order,
            ]
          )
        }

        // Migrate statistics
        const statistics = transformStatisticsData(businessUnit.id, data.statistics)
        if (statistics) {
          await executeQuery(
            `
            INSERT INTO statistics (id, business_unit_id, title, description, items)
            VALUES (?, ?, ?, ?, ?)
          `,
            [
              statistics.id,
              statistics.business_unit_id,
              statistics.title,
              statistics.description,
              statistics.items,
            ]
          )
        }

        // Migrate gallery
        const galleryImages = transformGalleryData(businessUnit.id, data.gallery)
        for (const image of galleryImages) {
          await executeQuery(
            `
            INSERT INTO gallery_images (id, business_unit_id, src, alt, title, description, sort_order)
            VALUES (?, ?, ?, ?, ?, ?, ?)
          `,
            [
              image.id,
              image.business_unit_id,
              image.src,
              image.alt,
              image.title,
              image.description,
              image.sort_order,
            ]
          )
        }

        console.log(
          `✅ Successfully migrated ${slug} (${products.length} products, ${galleryImages.length} gallery images)`
        )
        migratedCount++
      } catch (error) {
        console.error(`❌ Failed to migrate ${slug}:`, error)
        // Continue with other units
      }
    }

    console.log(`🎉 Migration completed! Migrated ${migratedCount} business units`)
    return migratedCount
  } catch (error) {
    console.error('❌ Migration failed:', error)
    throw error
  }
}

/**
 * Verify migration results
 */
export async function verifyMigration() {
  try {
    console.log('🔍 Verifying migration results...')

    const results = {}

    // Count records in each table
    const businessUnitsCount = await executeQuery('SELECT COUNT(*) as count FROM business_units')
    results.business_units = businessUnitsCount[0].count

    const productsCount = await executeQuery('SELECT COUNT(*) as count FROM products')
    results.products = productsCount[0].count

    const statisticsCount = await executeQuery('SELECT COUNT(*) as count FROM statistics')
    results.statistics = statisticsCount[0].count

    const galleryCount = await executeQuery('SELECT COUNT(*) as count FROM gallery_images')
    results.gallery_images = galleryCount[0].count

    console.log('📊 Migration verification results:')
    console.log(`   - Business Units: ${results.business_units}`)
    console.log(`   - Products: ${results.products}`)
    console.log(`   - Statistics: ${results.statistics}`)
    console.log(`   - Gallery Images: ${results.gallery_images}`)

    return results
  } catch (error) {
    console.error('❌ Migration verification failed:', error)
    throw error
  }
}

/**
 * Clear all business units data
 */
export async function clearBusinessUnitsData() {
  try {
    console.log('🗑️ Clearing business units data...')

    // Clear in order due to foreign key constraints
    await executeQuery('DELETE FROM gallery_images')
    await executeQuery('DELETE FROM statistics')
    await executeQuery('DELETE FROM products')
    await executeQuery('DELETE FROM business_units')

    console.log('✅ All business units data cleared')
    return true
  } catch (error) {
    console.error('❌ Failed to clear data:', error)
    throw error
  }
}
