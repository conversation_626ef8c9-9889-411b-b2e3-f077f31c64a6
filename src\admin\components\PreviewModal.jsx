/**
 * PreviewModal - Component for previewing business unit changes before publishing
 */

import { useState } from 'react'
import Modal from '../../components/ui/Modal.jsx'
import Button from '../../components/ui/Button.jsx'
import Badge from '../../components/ui/Badge.jsx'
import DetailUsahaTemplate from '../../components/DetailUsahaTemplate.jsx'

export default function PreviewModal({
  isOpen,
  onClose,
  businessUnitData,
  onPublish,
  onReturnToEdit,
  isPublishing = false,
}) {
  const [selectedViewport, setSelectedViewport] = useState('desktop')
  const [showPublishConfirm, setShowPublishConfirm] = useState(false)

  const viewports = [
    {
      id: 'desktop',
      name: 'Desktop',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
          />
        </svg>
      ),
      width: '100%',
    },
    {
      id: 'tablet',
      name: 'Tablet',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z"
          />
        </svg>
      ),
      width: '768px',
    },
    {
      id: 'mobile',
      name: 'Mobile',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
          />
        </svg>
      ),
      width: '375px',
    },
  ]

  const handlePublishClick = () => {
    setShowPublishConfirm(true)
  }

  const handleConfirmPublish = () => {
    setShowPublishConfirm(false)
    if (onPublish) {
      onPublish()
    }
  }

  const handleCancelPublish = () => {
    setShowPublishConfirm(false)
  }

  const handleReturnToEdit = () => {
    if (onReturnToEdit) {
      onReturnToEdit()
    }
    onClose()
  }

  if (!businessUnitData) return null

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="full"
      closeOnOverlayClick={false}
      title={
        <div className="flex items-center gap-3">
          <span>Preview: {businessUnitData.name}</span>
          <Badge variant="info" size="sm">
            Preview Mode
          </Badge>
        </div>
      }
    >
      {/* Preview Controls */}
      <div className="bg-zinc-800 border-b border-zinc-700 p-4">
        <div className="flex items-center justify-between">
          {/* Viewport Controls */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-zinc-400 mr-2">View:</span>
            {viewports.map((viewport) => (
              <button
                key={viewport.id}
                onClick={() => setSelectedViewport(viewport.id)}
                className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  selectedViewport === viewport.id
                    ? 'bg-amber-600 text-white'
                    : 'bg-zinc-700 text-zinc-300 hover:bg-zinc-600'
                }`}
              >
                {viewport.icon}
                {viewport.name}
              </button>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            <Button variant="secondary" onClick={handleReturnToEdit}>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M11 17l-5-5m0 0l5-5m-5 5h12"
                />
              </svg>
              Return to Edit
            </Button>
            <Button onClick={handlePublishClick} loading={isPublishing} disabled={isPublishing}>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"
                />
              </svg>
              {isPublishing ? 'Publishing...' : 'Publish Changes'}
            </Button>
          </div>
        </div>
      </div>

      {/* Publishing Progress Overlay */}
      {isPublishing && (
        <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-10">
          <div className="bg-zinc-800 rounded-lg p-6 max-w-sm mx-4 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-500 mx-auto mb-4"></div>
            <h3 className="text-lg font-semibold text-white mb-2">Publishing Changes</h3>
            <p className="text-zinc-300 text-sm">
              Your changes are being published to the live website. This may take a few moments...
            </p>
          </div>
        </div>
      )}

      {/* Preview Content */}
      <div className="bg-zinc-900 min-h-screen">
        <div
          className="mx-auto transition-all duration-300"
          style={{
            width: viewports.find((v) => v.id === selectedViewport)?.width,
            maxWidth: '100%',
          }}
        >
          {/* Preview Indicator */}
          <div className="bg-amber-600 text-white text-center py-2 px-4 text-sm font-medium">
            <div className="flex items-center justify-center gap-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
              PREVIEW MODE - This is how your changes will appear on the live website
            </div>
          </div>

          {/* Business Unit Template */}
          <div className="bg-zinc-950">
            <DetailUsahaTemplate unitData={businessUnitData} />
          </div>
        </div>
      </div>

      {/* Publish Confirmation Modal */}
      {showPublishConfirm && (
        <Modal
          isOpen={showPublishConfirm}
          onClose={handleCancelPublish}
          size="md"
          title="Confirm Publishing"
        >
          <div className="p-6">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 bg-amber-600 rounded-full flex items-center justify-center flex-shrink-0">
                <svg
                  className="w-6 h-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-white mb-2">
                  Publish Changes to Live Website?
                </h3>
                <p className="text-zinc-300 mb-4">
                  This will make your changes visible to all website visitors immediately. The
                  changes cannot be undone automatically, but you can always edit the business unit
                  again.
                </p>
                <div className="bg-zinc-800 rounded-lg p-4 mb-6">
                  <h4 className="text-sm font-medium text-white mb-2">Changes to be published:</h4>
                  <ul className="text-sm text-zinc-300 space-y-1">
                    <li>• Business unit: {businessUnitData.name}</li>
                    <li>• All content sections (About, Products, Statistics, Gallery, Contact)</li>
                    <li>• Theme colors and styling</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-3">
              <Button variant="secondary" onClick={handleCancelPublish}>
                Cancel
              </Button>
              <Button onClick={handleConfirmPublish} loading={isPublishing} disabled={isPublishing}>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"
                  />
                </svg>
                {isPublishing ? 'Publishing...' : 'Publish Now'}
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </Modal>
  )
}
