/**
 * BusinessUnitsShow - Page for displaying business unit details (read-only)
 */

import { useState, useEffect, useCallback } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import Card from '../../../components/ui/Card.jsx'
import Button from '../../../components/ui/Button.jsx'
import Badge from '../../../components/ui/Badge.jsx'
import { useToast } from '../../../components/ui/Toast.jsx'
import businessUnitService from '../../../services/BusinessUnitService.js'

export default function BusinessUnitsShow() {
  const [businessUnit, setBusinessUnit] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const navigate = useNavigate()
  const { id } = useParams()
  const { showToast, ToastContainer } = useToast()

  const fetchBusinessUnit = useCallback(async (businessUnitId) => {
    try {
      const data = await businessUnitService.getBusinessUnit(businessUnitId)
      setBusinessUnit(data)
    } catch (err) {
      console.error('Business unit not found:', businessUnitId)
      showToast('Business unit not found', 'error')
      navigate('/admin/business-units')
    }
  }, [navigate, showToast])

  // Load business unit data when component mounts
  useEffect(() => {
    if (id) {
      fetchBusinessUnit(id)
    }
  }, [id, fetchBusinessUnit])

  const handleEdit = () => {
    navigate(`/admin/business-units/${id}/edit`)
  }

  const handleBackToList = () => {
    navigate('/admin/business-units')
  }

  const handleDelete = async () => {
    if (window.confirm(`Are you sure you want to delete "${businessUnit.name}"? This action cannot be undone.`)) {
      try {
        await businessUnitService.deleteBusinessUnit(businessUnit.id)
        showToast('Business unit deleted successfully', 'success')
        navigate('/admin/business-units')
      } catch (err) {
        console.error('Error deleting business unit:', err)
        showToast('Failed to delete business unit', 'error')
      }
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-zinc-700 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg
              className="w-8 h-8 text-zinc-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-white mb-2">Loading Business Unit</h3>
          <p className="text-zinc-400">Please wait while we load the business unit data...</p>
        </div>
      </div>
    )
  }

  if (!businessUnit) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-zinc-700 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg
              className="w-8 h-8 text-zinc-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-white mb-2">Business Unit Not Found</h3>
          <p className="text-zinc-400">The business unit you're looking for doesn't exist.</p>
          <Button variant="secondary" onClick={handleBackToList} className="mt-4">
            Back to List
          </Button>
        </div>
      </div>
    )
  }

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  return (
    <div className="space-y-6">
      {/* Business Unit Info */}
      <div className="flex items-center gap-2">
        <Badge variant={businessUnit.badgeVariant || 'primary'} size="sm">
          {businessUnit.badgeText}
        </Badge>
        {businessUnit.lastModified && (
          <span className="text-xs text-zinc-500">
            Last modified: {formatDate(businessUnit.lastModified)}
          </span>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex items-center gap-2">
        <Button variant="secondary" onClick={handleEdit}>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
          Edit
        </Button>
        <Button variant="danger" onClick={handleDelete}>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
          Delete
        </Button>
      </div>

      {/* Basic Information */}
      <Card padding="lg">
        <h2 className="text-lg font-semibold text-white mb-4">Basic Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-zinc-400 mb-2">Name</label>
            <p className="text-white">{businessUnit.name}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-zinc-400 mb-2">Slug</label>
            <p className="text-white font-mono">{businessUnit.slug}</p>
          </div>
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-zinc-400 mb-2">Description</label>
            <p className="text-white">{businessUnit.description}</p>
          </div>
        </div>
      </Card>

      {/* About Section */}
      {businessUnit.aboutContent && (
        <Card padding="lg">
          <h2 className="text-lg font-semibold text-white mb-4">About</h2>
          <div className="space-y-4">
            {businessUnit.highlightText && (
              <div>
                <label className="block text-sm font-medium text-zinc-400 mb-2">Highlight Text</label>
                <p className="text-white">{businessUnit.highlightText}</p>
              </div>
            )}
            <div>
              <label className="block text-sm font-medium text-zinc-400 mb-2">Content</label>
              <p className="text-white">{businessUnit.aboutContent}</p>
            </div>
            {businessUnit.vision && (
              <div>
                <label className="block text-sm font-medium text-zinc-400 mb-2">Vision</label>
                <p className="text-white">{businessUnit.vision}</p>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* Products Section */}
      {businessUnit.products && businessUnit.products.items && businessUnit.products.items.length > 0 && (
        <Card padding="lg">
          <h2 className="text-lg font-semibold text-white mb-4">Products</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-zinc-400 mb-2">Title</label>
              <p className="text-white">{businessUnit.products.title}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-zinc-400 mb-2">Description</label>
              <p className="text-white">{businessUnit.products.description}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-zinc-400 mb-2">Products Count</label>
              <p className="text-white">{businessUnit.products.items.length} products</p>
            </div>
          </div>
        </Card>
      )}

      {/* Statistics Section */}
      {businessUnit.statistics && businessUnit.statistics.items && businessUnit.statistics.items.length > 0 && (
        <Card padding="lg">
          <h2 className="text-lg font-semibold text-white mb-4">Statistics</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-zinc-400 mb-2">Title</label>
              <p className="text-white">{businessUnit.statistics.title}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-zinc-400 mb-2">Description</label>
              <p className="text-white">{businessUnit.statistics.description}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-zinc-400 mb-2">Statistics Count</label>
              <p className="text-white">{businessUnit.statistics.items.length} statistics</p>
            </div>
          </div>
        </Card>
      )}

      {/* Gallery Section */}
      {businessUnit.gallery && businessUnit.gallery.images && businessUnit.gallery.images.length > 0 && (
        <Card padding="lg">
          <h2 className="text-lg font-semibold text-white mb-4">Gallery</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-zinc-400 mb-2">Title</label>
              <p className="text-white">{businessUnit.gallery.title}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-zinc-400 mb-2">Description</label>
              <p className="text-white">{businessUnit.gallery.description}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-zinc-400 mb-2">Images Count</label>
              <p className="text-white">{businessUnit.gallery.images.length} images</p>
            </div>
          </div>
        </Card>
      )}

      {/* Contact Information */}
      {businessUnit.contact && (
        <Card padding="lg">
          <h2 className="text-lg font-semibold text-white mb-4">Contact Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {businessUnit.contact.address && (
              <div>
                <label className="block text-sm font-medium text-zinc-400 mb-2">Address</label>
                <p className="text-white">{businessUnit.contact.address}</p>
              </div>
            )}
            {businessUnit.contact.phone && (
              <div>
                <label className="block text-sm font-medium text-zinc-400 mb-2">Phone</label>
                <p className="text-white">{businessUnit.contact.phone}</p>
              </div>
            )}
            {businessUnit.contact.email && (
              <div>
                <label className="block text-sm font-medium text-zinc-400 mb-2">Email</label>
                <p className="text-white">{businessUnit.contact.email}</p>
              </div>
            )}
            {businessUnit.contact.hours && (
              <div>
                <label className="block text-sm font-medium text-zinc-400 mb-2">Business Hours</label>
                <p className="text-white">{businessUnit.contact.hours}</p>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* Toast Notifications */}
      <ToastContainer />
    </div>
  )
}
