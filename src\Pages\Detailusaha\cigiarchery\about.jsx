const CigiArcheryAbout = () => {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Tentang <span className="text-purple-600"><PERSON><PERSON></span>
          </h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          <div>
            <img
              src="https://picsum.photos/600/400"
              alt="Cigi Archery"
              className="w-full h-96 object-cover rounded-lg shadow-lg"
            />
          </div>
          <div className="flex flex-col justify-center">
            <p className="text-lg text-gray-700 leading-relaxed mb-6">
              <PERSON><PERSON> adalah tim panahan masyarakat Desa Cimande Girang yang didirikan untuk
              mengembangkan bakat olahraga warga, khususnya generasi muda. Kami berkomitmen
              membangun prestasi dan karakter melalui olahraga panahan yang sehat dan kompetitif.
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <div className="bg-white p-8 rounded-lg shadow-lg">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Visi</h2>
            <p className="text-gray-700 leading-relaxed">
              Menjadi tim panahan terdepan di tingkat kecamatan yang mencetak atlet berprestasi dan
              membanggakan desa
            </p>
          </div>
          <div className="bg-white p-8 rounded-lg shadow-lg">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Misi</h2>
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="flex-shrink-0 w-6 h-6 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                  1
                </span>
                <span className="text-gray-700">
                  Mengembangkan potensi dan bakat panahan masyarakat desa
                </span>
              </li>
              <li className="flex items-start">
                <span className="flex-shrink-0 w-6 h-6 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                  2
                </span>
                <span className="text-gray-700">
                  Menyelenggarakan pertandingan dan kompetisi panahan
                </span>
              </li>
              <li className="flex items-start">
                <span className="flex-shrink-0 w-6 h-6 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                  3
                </span>
                <span className="text-gray-700">
                  Membangun karakter fokus, disiplin, dan pantang menyerah
                </span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CigiArcheryAbout
