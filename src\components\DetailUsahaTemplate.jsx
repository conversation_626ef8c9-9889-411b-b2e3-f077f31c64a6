import { useMemo } from 'react'
import Section from './ui/Section'
import Card from './ui/Card'
import Badge from './ui/Badge'
import Button from './ui/Button'
import AnimatedElement from './ui/AnimatedElement'

const DetailUsahaTemplate = ({ unitData }) => {
  const {
    // Basic Info
    name,
    badgeText,
    badgeVariant = 'primary',
    badgeIcon,
    description,
    themeColor,

    // About Section
    aboutImage,
    aboutContent,
    highlightText,
    highlightClass,
    badges = [],

    // Vision & Mission
    vision,
    mission,

    // Products/Services
    products,

    // Statistics
    statistics,

    // Gallery
    gallery,

    // Contact
    contact,

    // Layout Configuration
    layoutConfig = {},
  } = unitData

  // Dynamic gradient based on theme color
  const gradientStyle = useMemo(
    () => ({
      background: `linear-gradient(to right, ${themeColor?.from || 'var(--color-primary-600)'}, ${themeColor?.to || 'var(--color-primary-400)'})`,
    }),
    [themeColor]
  )

  // Render Products Section with different layouts
  const renderProductsGrid = () => (
    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
      {products.items.map((item, index) => (
        <AnimatedElement key={index} animation="slideInUp" delay={index * 100}>
          <Card className="h-full hover:scale-105 transition-transform duration-300">
            {item.image && (
              <div className="h-48 bg-gradient-to-br from-zinc-800 to-zinc-900 rounded-t-xl overflow-hidden">
                <img
                  src={item.image}
                  alt={item.title}
                  className="w-full h-full object-cover"
                  loading="lazy"
                />
              </div>
            )}
            <div className="p-6">
              <h3 className="text-xl font-bold text-white mb-3">{item.title}</h3>
              <p className="text-zinc-300 mb-4">{item.description}</p>
              {item.features && (
                <ul className="space-y-2">
                  {item.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-sm text-zinc-400">
                      <div className="w-4 h-4 bg-green-500 rounded-full mr-2 flex-shrink-0">
                        <svg
                          className="w-3 h-3 text-white m-0.5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={3}
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                      </div>
                      {feature}
                    </li>
                  ))}
                </ul>
              )}
              {item.price && (
                <div className="mt-4 p-3 bg-zinc-800 rounded-lg">
                  <span
                    className="text-lg font-bold"
                    style={{ color: themeColor?.primary || '#3B82F6' }}
                  >
                    {item.price}
                  </span>
                </div>
              )}
            </div>
          </Card>
        </AnimatedElement>
      ))}
    </div>
  )

  const renderProductsCarousel = () => (
    <div className="space-y-8 max-w-6xl mx-auto">
      {products.items.map((item, index) => (
        <AnimatedElement
          key={index}
          animation={index % 2 === 0 ? 'slideInLeft' : 'slideInRight'}
          delay={index * 200}
        >
          <Card
            className={`overflow-hidden ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} lg:flex items-center gap-8`}
          >
            {item.image && (
              <div className="lg:w-1/2 h-64 lg:h-80">
                <img
                  src={item.image}
                  alt={item.title}
                  className="w-full h-full object-cover"
                  loading="lazy"
                />
              </div>
            )}
            <div className="lg:w-1/2 p-8">
              <h3 className="text-2xl font-bold text-white mb-4">{item.title}</h3>
              <p className="text-lg text-zinc-300 mb-6">{item.description}</p>
              {item.features && (
                <ul className="space-y-3 mb-6">
                  {item.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-zinc-300">
                      <div
                        className="w-5 h-5 rounded-full mr-3 flex-shrink-0"
                        style={{ backgroundColor: themeColor?.primary || '#3B82F6' }}
                      >
                        <svg
                          className="w-4 h-4 text-white m-0.5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={3}
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                      </div>
                      {feature}
                    </li>
                  ))}
                </ul>
              )}
              {item.price && (
                <div
                  className="text-2xl font-bold"
                  style={{ color: themeColor?.primary || '#3B82F6' }}
                >
                  {item.price}
                </div>
              )}
            </div>
          </Card>
        </AnimatedElement>
      ))}
    </div>
  )

  const renderProductsShowcase = () => (
    <div className="space-y-12 max-w-7xl mx-auto">
      {/* Featured Product */}
      {products.items[0] && (
        <AnimatedElement animation="fadeInUp">
          <Card
            className="overflow-hidden lg:flex items-center gap-8 p-8"
            style={{
              background: `linear-gradient(135deg, ${themeColor?.from || '#1f2937'}22, ${themeColor?.to || '#374151'}22)`,
            }}
          >
            <div className="lg:w-2/3">
              <div className="mb-4">
                <Badge variant="primary" size="sm">
                  Produk Unggulan
                </Badge>
              </div>
              <h3 className="text-3xl font-bold text-white mb-4">{products.items[0].title}</h3>
              <p className="text-xl text-zinc-300 mb-6">{products.items[0].description}</p>
              {products.items[0].features && (
                <div className="grid md:grid-cols-2 gap-3 mb-6">
                  {products.items[0].features.map((feature, idx) => (
                    <div key={idx} className="flex items-center text-zinc-300">
                      <div
                        className="w-5 h-5 rounded-full mr-3 flex-shrink-0"
                        style={{ backgroundColor: themeColor?.primary || '#3B82F6' }}
                      >
                        <svg
                          className="w-4 h-4 text-white m-0.5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={3}
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                      </div>
                      {feature}
                    </div>
                  ))}
                </div>
              )}
              {products.items[0].price && (
                <div
                  className="text-3xl font-bold mb-4"
                  style={{ color: themeColor?.primary || '#3B82F6' }}
                >
                  {products.items[0].price}
                </div>
              )}
              <Button variant="primary" size="lg">
                Pesan Sekarang
              </Button>
            </div>
            {products.items[0].image && (
              <div className="lg:w-1/3">
                <img
                  src={products.items[0].image}
                  alt={products.items[0].title}
                  className="w-full h-64 lg:h-80 object-cover rounded-xl"
                  loading="lazy"
                />
              </div>
            )}
          </Card>
        </AnimatedElement>
      )}

      {/* Other Products */}
      {products.items.slice(1).length > 0 && (
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {products.items.slice(1).map((item, index) => (
            <AnimatedElement key={index} animation="slideInUp" delay={(index + 1) * 100}>
              <Card className="h-full group hover:scale-105 transition-all duration-300">
                {item.image && (
                  <div className="h-48 overflow-hidden rounded-t-xl">
                    <img
                      src={item.image}
                      alt={item.title}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                      loading="lazy"
                    />
                  </div>
                )}
                <div className="p-6">
                  <h4 className="text-lg font-bold text-white mb-2">{item.title}</h4>
                  <p className="text-zinc-400 text-sm mb-3">{item.description}</p>
                  {item.price && (
                    <div
                      className="text-lg font-bold"
                      style={{ color: themeColor?.primary || '#3B82F6' }}
                    >
                      {item.price}
                    </div>
                  )}
                </div>
              </Card>
            </AnimatedElement>
          ))}
        </div>
      )}
    </div>
  )

  const renderProductsList = () => (
    <div className="max-w-4xl mx-auto space-y-6">
      {products.items.map((item, index) => (
        <AnimatedElement key={index} animation="slideInUp" delay={index * 100}>
          <Card className="p-6 hover:scale-102 transition-transform duration-300">
            <div className="flex flex-col md:flex-row gap-6">
              {item.image && (
                <div className="md:w-32 md:h-32 w-full h-48 flex-shrink-0">
                  <img
                    src={item.image}
                    alt={item.title}
                    className="w-full h-full object-cover rounded-lg"
                    loading="lazy"
                  />
                </div>
              )}
              <div className="flex-1">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-xl font-bold text-white">{item.title}</h3>
                  {item.price && (
                    <span
                      className="text-xl font-bold"
                      style={{ color: themeColor?.primary || '#3B82F6' }}
                    >
                      {item.price}
                    </span>
                  )}
                </div>
                <p className="text-zinc-300 mb-4">{item.description}</p>
                {item.features && (
                  <div className="flex flex-wrap gap-2">
                    {item.features.map((feature, idx) => (
                      <Badge key={idx} variant="secondary" size="sm">
                        {feature}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </Card>
        </AnimatedElement>
      ))}
    </div>
  )

  // Pricing plans for internet unit (Cigi Net) - simplified cards without images
  const renderPricingPlans = () => (
    <div className="max-w-6xl mx-auto grid md:grid-cols-3 gap-6">
      {products.items.map((plan, idx) => (
        <AnimatedElement key={idx} animation="fadeInUp" delay={idx * 100}>
          <Card padding="lg" className="h-full text-center">
            <div className="mb-4">
              <h3 className="text-xl font-bold text-white">{plan.title}</h3>
            </div>
            <div
              className="text-3xl lg:text-4xl font-extrabold mb-4"
              style={{ color: themeColor?.primary || '#3B82F6' }}
            >
              {plan.price}
            </div>
            <p className="text-zinc-300 mb-6">{plan.description}</p>

            {plan.features && (
              <ul className="space-y-3 text-left mb-6">
                {plan.features.map((f, i) => (
                  <li key={i} className="flex items-start">
                    <div
                      className="w-6 h-6 rounded-full mr-3 mt-1 flex-shrink-0"
                      style={{
                        backgroundColor: themeColor?.primary || '#3B82F6',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <svg
                        className="w-4 h-4 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={3}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    </div>
                    <span className="text-zinc-300">{f}</span>
                  </li>
                ))}
              </ul>
            )}

            <div className="mt-auto">
              <Button variant="primary" size="md">
                Pilih Paket
              </Button>
            </div>
          </Card>
        </AnimatedElement>
      ))}
    </div>
  )

  return (
    <>
      {/* Hero Section */}
      <Section padding="xl" id="hero">
        <div className="text-center max-w-4xl mx-auto mb-16">
          <AnimatedElement animation="fadeInUp">
            <Badge variant={badgeVariant} size="lg" className="mb-6">
              {badgeIcon} {badgeText}
            </Badge>
            <h1 className="text-responsive-xl mb-6">
              <span className="gradient-text">{name}</span>
            </h1>
            <p className="text-xl text-zinc-300 leading-relaxed">{description}</p>
          </AnimatedElement>
        </div>

        {/* About Section */}
        <div className="grid md:grid-cols-2 gap-12 items-center mb-20" id="about">
          <AnimatedElement animation="fadeInUp">
            <div className="relative">
              <div
                className="absolute -inset-4 rounded-2xl blur-2xl opacity-20"
                style={gradientStyle}
              ></div>
              <img
                src={aboutImage}
                alt={name}
                className="relative w-full rounded-2xl shadow-2xl object-cover"
                loading="lazy"
              />
            </div>
          </AnimatedElement>

          <AnimatedElement animation="slideInRight" delay={300}>
            <Card padding="lg">
              <h2 className="text-2xl font-bold text-white mb-6">Tentang {name}</h2>
              <div className="text-lg text-zinc-300 leading-relaxed mb-6">
                {highlightText && highlightClass ? (
                  <span
                    dangerouslySetInnerHTML={{
                      __html: aboutContent.replace(
                        highlightText,
                        `<span class="${highlightClass}">${highlightText}</span>`
                      ),
                    }}
                  />
                ) : (
                  aboutContent
                )}
              </div>
              {badges.length > 0 && (
                <div className="flex flex-wrap gap-3">
                  {badges.map((badge, index) => (
                    <Badge key={index} variant={badge.variant}>
                      {badge.text}
                    </Badge>
                  ))}
                </div>
              )}
            </Card>
          </AnimatedElement>
        </div>
      </Section>

      {/* Vision & Mission */}
      {(vision || mission) && (
        <Section padding="xl" className="bg-zinc-900/50">
          <div className="text-center mb-16">
            <AnimatedElement animation="fadeInUp">
              <h2 className="text-responsive-lg mb-4">Visi & Misi</h2>
              <p className="text-xl text-zinc-400">
                Fondasi dan arah pengembangan {name} untuk masa depan
              </p>
            </AnimatedElement>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {vision && (
              <AnimatedElement animation="slideInLeft">
                <Card padding="lg" className="h-full">
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-400 rounded-full flex items-center justify-center mr-4">
                      <svg
                        className="w-6 h-6 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                        />
                      </svg>
                    </div>
                    <h2 className="text-2xl font-bold text-white">Visi</h2>
                  </div>
                  <p className="text-lg text-zinc-300 leading-relaxed italic">"{vision}"</p>
                </Card>
              </AnimatedElement>
            )}

            {mission && (
              <AnimatedElement animation="slideInRight" delay={200}>
                <Card padding="lg" className="h-full">
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-gradient-to-r from-amber-600 to-amber-400 rounded-full flex items-center justify-center mr-4">
                      <svg
                        className="w-6 h-6 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"
                        />
                      </svg>
                    </div>
                    <h2 className="text-2xl font-bold text-white">Misi</h2>
                  </div>
                  {Array.isArray(mission) ? (
                    <ul className="space-y-3">
                      {mission.map((item, index) => (
                        <li key={index} className="flex items-start">
                          <div className="w-6 h-6 bg-amber-500 rounded-full flex items-center justify-center mr-3 mt-1 flex-shrink-0">
                            <span className="text-xs text-white font-bold">{index + 1}</span>
                          </div>
                          <span className="text-zinc-300">{item}</span>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-lg text-zinc-300 leading-relaxed">{mission}</p>
                  )}
                </Card>
              </AnimatedElement>
            )}
          </div>
        </Section>
      )}

      {/* Products/Services Section (hidden for Cigi Net since we show a dedicated Paket & Harga) */}
      {products && name !== 'Cigi Net' && (
        <Section padding="xl" id="product">
          <div className="text-center mb-16">
            <AnimatedElement animation="fadeInUp">
              <h2 className="text-responsive-lg mb-4">{products.title}</h2>
              <p className="text-xl text-zinc-400">{products.description}</p>
            </AnimatedElement>
          </div>

          {products.items &&
            (() => {
              const layout = layoutConfig.productsLayout || 'grid'
              switch (layout) {
                case 'carousel':
                  return renderProductsCarousel()
                case 'showcase':
                  return renderProductsShowcase()
                case 'list':
                  return renderProductsList()
                default:
                  return renderProductsGrid()
              }
            })()}

          {products.cta && (
            <div className="text-center mt-12">
              <AnimatedElement animation="fadeInUp">
                <Button variant="primary" size="lg">
                  {products.cta.text}
                </Button>
              </AnimatedElement>
            </div>
          )}
        </Section>
      )}

      {/* Pricing for Cigi Net (internet plans) */}
      {unitData.name === 'Cigi Net' && products && (
        <Section padding="lg" className="bg-zinc-900/30" id="pricing">
          <div className="text-center mb-12">
            <AnimatedElement animation="fadeInUp">
              <h2 className="text-responsive-lg mb-4">Paket & Harga</h2>
              <p className="text-xl text-zinc-400">
                Pilih paket internet Cigi Net yang sesuai kebutuhan Anda
              </p>
            </AnimatedElement>
          </div>

          {renderPricingPlans()}
        </Section>
      )}

      {/* Statistics Section */}
      {statistics && (
        <Section padding="xl" className="bg-zinc-900/50">
          <div className="text-center mb-16">
            <AnimatedElement animation="fadeInUp">
              <h2 className="text-responsive-lg mb-4">{statistics.title}</h2>
              <p className="text-xl text-zinc-400">{statistics.description}</p>
            </AnimatedElement>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-6xl mx-auto">
            {statistics.items.map((stat, index) => (
              <AnimatedElement key={index} animation="slideInUp" delay={index * 100}>
                <div className="text-center">
                  <div
                    className="text-4xl lg:text-5xl font-bold mb-2"
                    style={{ color: themeColor?.primary || '#3B82F6' }}
                  >
                    {stat.value}
                  </div>
                  <div className="text-sm text-zinc-400 uppercase tracking-wider">{stat.label}</div>
                </div>
              </AnimatedElement>
            ))}
          </div>
        </Section>
      )}

      {/* Gallery Section */}
      {gallery && (
        <Section padding="xl">
          <div className="text-center mb-16">
            <AnimatedElement animation="fadeInUp">
              <h2 className="text-responsive-lg mb-4">{gallery.title}</h2>
              <p className="text-xl text-zinc-400">{gallery.description}</p>
            </AnimatedElement>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
            {gallery.images.map((image, index) => (
              <AnimatedElement key={index} animation="fadeInUp" delay={index * 100}>
                <div className="group relative overflow-hidden rounded-xl bg-zinc-800">
                  <img
                    src={image.src}
                    alt={image.alt}
                    className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
                    loading="lazy"
                  />
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                    <div className="p-4 text-white">
                      <h4 className="font-semibold">{image.title}</h4>
                      <p className="text-sm text-zinc-300">{image.description}</p>
                    </div>
                  </div>
                </div>
              </AnimatedElement>
            ))}
          </div>
        </Section>
      )}

      {/* Contact Section */}
      {contact && (
        <Section padding="xl" className="bg-zinc-900/50" id="contact">
          <div className="text-center mb-16">
            <AnimatedElement animation="fadeInUp">
              <h2 className="text-responsive-lg mb-4">Lokasi & Kontak</h2>
              <p className="text-xl text-zinc-400">
                Hubungi kami untuk informasi lebih lanjut atau kunjungi langsung lokasi kami
              </p>
            </AnimatedElement>
          </div>

          <div className="grid md:grid-cols-2 gap-12 max-w-6xl mx-auto">
            <AnimatedElement animation="slideInLeft">
              <Card padding="lg">
                <h3 className="text-xl font-bold text-white mb-6">Informasi Kontak</h3>
                <div className="space-y-4">
                  {contact.address && (
                    <div className="flex items-start">
                      <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                        <svg
                          className="w-6 h-6 text-white"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                          />
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                          />
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-semibold text-white">Alamat</h4>
                        <p className="text-zinc-300">{contact.address}</p>
                      </div>
                    </div>
                  )}

                  {contact.phone && (
                    <div className="flex items-start">
                      <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                        <svg
                          className="w-6 h-6 text-white"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                          />
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-semibold text-white">Telepon</h4>
                        <p className="text-zinc-300">{contact.phone}</p>
                      </div>
                    </div>
                  )}

                  {contact.email && (
                    <div className="flex items-start">
                      <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                        <svg
                          className="w-6 h-6 text-white"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-semibold text-white">Email</h4>
                        <p className="text-zinc-300">{contact.email}</p>
                      </div>
                    </div>
                  )}

                  {contact.hours && (
                    <div className="flex items-start">
                      <div className="w-12 h-12 bg-orange-600 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                        <svg
                          className="w-6 h-6 text-white"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-semibold text-white">Jam Operasional</h4>
                        <p className="text-zinc-300">{contact.hours}</p>
                      </div>
                    </div>
                  )}
                </div>
              </Card>
            </AnimatedElement>

            <AnimatedElement animation="slideInRight" delay={200}>
              <div className="bg-zinc-800 rounded-xl overflow-hidden">
                <div className="h-64 bg-gradient-to-br from-zinc-700 to-zinc-800 flex items-center justify-center">
                  <div className="text-center text-zinc-400">
                    <svg
                      className="w-16 h-16 mx-auto mb-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                    <p>Peta Lokasi</p>
                  </div>
                </div>
              </div>
            </AnimatedElement>
          </div>
        </Section>
      )}
    </>
  )
}

export default DetailUsahaTemplate
