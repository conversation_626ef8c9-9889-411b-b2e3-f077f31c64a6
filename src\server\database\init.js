/**
 * Database initialization script for business units management
 * Creates tables for business units, products, statistics, and gallery images
 */

import Database from 'better-sqlite3'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Database path
const DB_PATH = path.join(__dirname, '../../../data/modular_system.db')

/**
 * Initialize database with business units schema
 */
export function initializeBusinessUnitsSchema() {
  const db = new Database(DB_PATH)

  try {
    // Enable foreign keys
    db.pragma('foreign_keys = ON')

    // Create business_units table
    db.exec(`
      CREATE TABLE IF NOT EXISTS business_units (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        slug TEXT UNIQUE NOT NULL,
        badge_text TEXT,
        badge_variant TEXT DEFAULT 'primary',
        badge_icon TEXT,
        description TEXT,
        theme_color_from TEXT,
        theme_color_to TEXT,
        theme_color_primary TEXT,
        layout_config TEXT, -- <PERSON><PERSON><PERSON> string
        about_image TEXT,
        about_content TEXT,
        highlight_text TEXT,
        highlight_class TEXT,
        vision TEXT,
        mission TEXT, -- JSON array as string
        badges TEXT, -- JSON array as string
        contact_address TEXT,
        contact_phone TEXT,
        contact_email TEXT,
        contact_hours TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Create products table
    db.exec(`
      CREATE TABLE IF NOT EXISTS products (
        id TEXT PRIMARY KEY,
        business_unit_id TEXT NOT NULL,
        title TEXT NOT NULL,
        description TEXT,
        image TEXT,
        price TEXT,
        features TEXT, -- JSON array as string
        sort_order INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (business_unit_id) REFERENCES business_units (id) ON DELETE CASCADE
      )
    `)

    // Create statistics table
    db.exec(`
      CREATE TABLE IF NOT EXISTS statistics (
        id TEXT PRIMARY KEY,
        business_unit_id TEXT NOT NULL,
        title TEXT,
        description TEXT,
        items TEXT, -- JSON array as string
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (business_unit_id) REFERENCES business_units (id) ON DELETE CASCADE
      )
    `)

    // Create gallery_images table
    db.exec(`
      CREATE TABLE IF NOT EXISTS gallery_images (
        id TEXT PRIMARY KEY,
        business_unit_id TEXT NOT NULL,
        src TEXT NOT NULL,
        alt TEXT,
        title TEXT,
        description TEXT,
        sort_order INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (business_unit_id) REFERENCES business_units (id) ON DELETE CASCADE
      )
    `)

    // Create indexes for better performance
    db.exec(`
      CREATE INDEX IF NOT EXISTS idx_business_units_slug ON business_units(slug);
      CREATE INDEX IF NOT EXISTS idx_products_business_unit ON products(business_unit_id);
      CREATE INDEX IF NOT EXISTS idx_products_sort_order ON products(business_unit_id, sort_order);
      CREATE INDEX IF NOT EXISTS idx_statistics_business_unit ON statistics(business_unit_id);
      CREATE INDEX IF NOT EXISTS idx_gallery_business_unit ON gallery_images(business_unit_id);
      CREATE INDEX IF NOT EXISTS idx_gallery_sort_order ON gallery_images(business_unit_id, sort_order);
    `)

    // Create trigger to update updated_at timestamp
    db.exec(`
      CREATE TRIGGER IF NOT EXISTS update_business_units_timestamp 
      AFTER UPDATE ON business_units
      BEGIN
        UPDATE business_units SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END
    `)

    console.log('✅ Business units database schema initialized successfully')
    return true
  } catch (error) {
    console.error('❌ Error initializing business units schema:', error)
    throw error
  } finally {
    db.close()
  }
}

/**
 * Check if tables exist and are properly structured
 */
export function verifySchema() {
  const db = new Database(DB_PATH)

  try {
    const tables = ['business_units', 'products', 'statistics', 'gallery_images']
    const results = {}

    for (const table of tables) {
      const result = db
        .prepare(
          `
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name=?
      `
        )
        .get(table)

      results[table] = !!result
    }

    console.log('📊 Schema verification results:', results)
    return results
  } catch (error) {
    console.error('❌ Error verifying schema:', error)
    throw error
  } finally {
    db.close()
  }
}

// Run initialization if this file is executed directly
if (typeof process !== 'undefined' && typeof window === 'undefined' && import.meta.url.endsWith(process.argv[1].replace(/\\/g, '/'))) {
  console.log('🚀 Running database initialization...')
  initializeBusinessUnitsSchema()
  verifySchema()
}
