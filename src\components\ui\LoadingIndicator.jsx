/**
 * LoadingIndicator - Various loading indicators for different use cases
 */

const LoadingSpinner = ({ size = 'md', className = '' }) => {
  const sizes = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12',
  }

  return (
    <div className={`animate-spin ${sizes[size]} ${className}`}>
      <svg className="w-full h-full" fill="none" viewBox="0 0 24 24">
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    </div>
  )
}

const LoadingDots = ({ className = '' }) => (
  <div className={`flex space-x-1 ${className}`}>
    <div
      className="w-2 h-2 bg-current rounded-full animate-bounce"
      style={{ animationDelay: '0ms' }}
    />
    <div
      className="w-2 h-2 bg-current rounded-full animate-bounce"
      style={{ animationDelay: '150ms' }}
    />
    <div
      className="w-2 h-2 bg-current rounded-full animate-bounce"
      style={{ animationDelay: '300ms' }}
    />
  </div>
)

const LoadingPulse = ({ className = '' }) => (
  <div className={`animate-pulse bg-zinc-700 rounded ${className}`} />
)

const LoadingOverlay = ({ message = 'Loading...', className = '' }) => (
  <div
    className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 ${className}`}
  >
    <div className="bg-zinc-800 rounded-lg p-6 flex flex-col items-center space-y-4">
      <LoadingSpinner size="lg" className="text-amber-500" />
      <p className="text-white text-sm">{message}</p>
    </div>
  </div>
)

const InlineLoader = ({ message = 'Loading...', className = '' }) => (
  <div className={`flex items-center space-x-3 ${className}`}>
    <LoadingSpinner size="sm" className="text-amber-500" />
    <span className="text-zinc-400 text-sm">{message}</span>
  </div>
)

const ButtonLoader = ({ loading = false, children, ...props }) => (
  <button {...props} disabled={loading || props.disabled}>
    {loading ? (
      <div className="flex items-center space-x-2">
        <LoadingSpinner size="sm" />
        <span>Loading...</span>
      </div>
    ) : (
      children
    )}
  </button>
)

const FormFieldLoader = ({ className = '' }) => (
  <div className={`space-y-3 ${className}`}>
    <LoadingPulse className="h-4 w-24" />
    <LoadingPulse className="h-10 w-full" />
  </div>
)

const CardLoader = ({ className = '' }) => (
  <div className={`bg-zinc-800 rounded-lg p-6 space-y-4 ${className}`}>
    <LoadingPulse className="h-6 w-3/4" />
    <LoadingPulse className="h-4 w-full" />
    <LoadingPulse className="h-4 w-2/3" />
    <div className="flex space-x-2 mt-4">
      <LoadingPulse className="h-8 w-20" />
      <LoadingPulse className="h-8 w-16" />
    </div>
  </div>
)

const TableRowLoader = ({ columns = 4, className = '' }) => (
  <tr className={className}>
    {Array.from({ length: columns }).map((_, index) => (
      <td key={index} className="px-6 py-4">
        <LoadingPulse className="h-4 w-full" />
      </td>
    ))}
  </tr>
)

const ListItemLoader = ({ className = '' }) => (
  <div className={`flex items-center space-x-3 p-3 ${className}`}>
    <LoadingPulse className="w-10 h-10 rounded-full" />
    <div className="flex-1 space-y-2">
      <LoadingPulse className="h-4 w-3/4" />
      <LoadingPulse className="h-3 w-1/2" />
    </div>
  </div>
)

export {
  LoadingSpinner,
  LoadingDots,
  LoadingPulse,
  LoadingOverlay,
  InlineLoader,
  ButtonLoader,
  FormFieldLoader,
  CardLoader,
  TableRowLoader,
  ListItemLoader,
}

export default LoadingSpinner
