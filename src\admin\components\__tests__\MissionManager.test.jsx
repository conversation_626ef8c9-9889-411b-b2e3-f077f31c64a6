/**
 * Test file for MissionManager component
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import MissionManager from '../MissionManager.jsx'

// Mock the UI components
vi.mock('../../../components/ui/Button.jsx', () => ({
  default: ({ children, onClick, size, variant, ...props }) => (
    <button
      data-testid="button"
      data-size={size}
      data-variant={variant}
      onClick={onClick}
      {...props}
    >
      {children}
    </button>
  ),
}))

describe('MissionManager', () => {
  const mockOnChange = vi.fn()

  const beforeEach = (fn) => {
    // Mock beforeEach for testing
    fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders with initial empty mission when no missions provided', () => {
    render(<MissionManager missions={[]} onChange={mockOnChange} />)

    expect(screen.getByText('Mission Statements')).toBeInTheDocument()
    expect(screen.getByText('Add Mission')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Enter mission statement 1...')).toBeInTheDocument()
  })

  it('renders with provided missions', () => {
    const missions = ['Mission 1', 'Mission 2']
    render(<MissionManager missions={missions} onChange={mockOnChange} />)

    expect(screen.getByDisplayValue('Mission 1')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Mission 2')).toBeInTheDocument()
    expect(screen.getByText('1')).toBeInTheDocument() // Mission number
    expect(screen.getByText('2')).toBeInTheDocument() // Mission number
  })

  it('adds new mission when Add Mission button is clicked', () => {
    render(<MissionManager missions={['Mission 1']} onChange={mockOnChange} />)

    const addButton = screen.getByText('Add Mission')
    fireEvent.click(addButton)

    expect(screen.getByPlaceholderText('Enter mission statement 2...')).toBeInTheDocument()
  })

  it('removes mission when remove button is clicked', () => {
    render(<MissionManager missions={['Mission 1', 'Mission 2']} onChange={mockOnChange} />)

    const removeButtons = screen.getAllByTitle('Remove mission')
    fireEvent.click(removeButtons[1]) // Remove second mission

    expect(mockOnChange).toHaveBeenCalledWith(['Mission 1'])
  })

  it('prevents removing the last mission', () => {
    render(<MissionManager missions={['Mission 1']} onChange={mockOnChange} />)

    const removeButton = screen.getByTitle('Remove mission')
    expect(removeButton).toBeDisabled()
  })

  it('moves mission up when up arrow is clicked', () => {
    render(<MissionManager missions={['Mission 1', 'Mission 2']} onChange={mockOnChange} />)

    const upButtons = screen.getAllByTitle('Move up')
    fireEvent.click(upButtons[1]) // Move second mission up

    expect(mockOnChange).toHaveBeenCalledWith(['Mission 2', 'Mission 1'])
  })

  it('moves mission down when down arrow is clicked', () => {
    render(<MissionManager missions={['Mission 1', 'Mission 2']} onChange={mockOnChange} />)

    const downButtons = screen.getAllByTitle('Move down')
    fireEvent.click(downButtons[0]) // Move first mission down

    expect(mockOnChange).toHaveBeenCalledWith(['Mission 2', 'Mission 1'])
  })

  it('disables up button for first mission', () => {
    render(<MissionManager missions={['Mission 1', 'Mission 2']} onChange={mockOnChange} />)

    const upButtons = screen.getAllByTitle('Move up')
    expect(upButtons[0]).toBeDisabled() // First mission up button should be disabled
    expect(upButtons[1]).not.toBeDisabled() // Second mission up button should be enabled
  })

  it('disables down button for last mission', () => {
    render(<MissionManager missions={['Mission 1', 'Mission 2']} onChange={mockOnChange} />)

    const downButtons = screen.getAllByTitle('Move down')
    expect(downButtons[0]).not.toBeDisabled() // First mission down button should be enabled
    expect(downButtons[1]).toBeDisabled() // Last mission down button should be disabled
  })

  it('validates empty mission statements', async () => {
    render(<MissionManager missions={['Mission 1']} onChange={mockOnChange} />)

    const textarea = screen.getByDisplayValue('Mission 1')
    fireEvent.change(textarea, { target: { value: '' } })

    await waitFor(() => {
      expect(screen.getByText('Mission statement cannot be empty')).toBeInTheDocument()
    })
  })

  it('validates mission statement length', async () => {
    render(<MissionManager missions={['Mission 1']} onChange={mockOnChange} />)

    const textarea = screen.getByDisplayValue('Mission 1')
    const longMission = 'a'.repeat(501)
    fireEvent.change(textarea, { target: { value: longMission } })

    await waitFor(() => {
      expect(
        screen.getByText('Mission statement must be 500 characters or less')
      ).toBeInTheDocument()
    })
  })

  it('shows character count for each mission', () => {
    render(<MissionManager missions={['Mission 1']} onChange={mockOnChange} />)

    expect(screen.getByText('9/500 characters')).toBeInTheDocument()
  })

  it('calls onChange with filtered missions (excluding empty ones)', () => {
    render(<MissionManager missions={['Mission 1', '']} onChange={mockOnChange} />)

    const textarea = screen.getByDisplayValue('Mission 1')
    fireEvent.change(textarea, { target: { value: 'Updated Mission 1' } })

    expect(mockOnChange).toHaveBeenCalledWith(['Updated Mission 1'])
  })

  it('displays validation summary when there are errors', async () => {
    render(<MissionManager missions={['Mission 1', 'Mission 2']} onChange={mockOnChange} />)

    const textareas = screen.getAllByRole('textbox')
    fireEvent.change(textareas[0], { target: { value: '' } })
    fireEvent.change(textareas[1], { target: { value: 'a'.repeat(501) } })

    await waitFor(() => {
      expect(screen.getByText('Mission validation errors:')).toBeInTheDocument()
      expect(screen.getByText('Mission statement cannot be empty')).toBeInTheDocument()
      expect(
        screen.getByText('Mission statement must be 500 characters or less')
      ).toBeInTheDocument()
    })
  })

  it('shows help text with usage instructions', () => {
    render(<MissionManager missions={['Mission 1']} onChange={mockOnChange} />)

    expect(
      screen.getByText("• Add multiple mission statements to describe your business unit's goals")
    ).toBeInTheDocument()
    expect(screen.getByText('• Use the up/down arrows to reorder missions')).toBeInTheDocument()
    expect(screen.getByText('• At least one mission statement is required')).toBeInTheDocument()
  })

  it('handles error reindexing when removing missions', async () => {
    render(
      <MissionManager missions={['Mission 1', 'Mission 2', 'Mission 3']} onChange={mockOnChange} />
    )

    // Create errors for missions 1 and 3
    const textareas = screen.getAllByRole('textbox')
    fireEvent.change(textareas[0], { target: { value: '' } })
    fireEvent.change(textareas[2], { target: { value: '' } })

    await waitFor(() => {
      expect(screen.getAllByText('Mission statement cannot be empty')).toHaveLength(2)
    })

    // Remove middle mission (index 1)
    const removeButtons = screen.getAllByTitle('Remove mission')
    fireEvent.click(removeButtons[1])

    // Errors should still be present but reindexed
    await waitFor(() => {
      expect(screen.getAllByText('Mission statement cannot be empty')).toHaveLength(2)
    })
  })

  it('swaps errors when moving missions up/down', async () => {
    render(<MissionManager missions={['', 'Mission 2']} onChange={mockOnChange} />)

    // Create error for first mission
    await waitFor(() => {
      expect(screen.getByText('Mission statement cannot be empty')).toBeInTheDocument()
    })

    // Move second mission up (swap positions)
    const upButtons = screen.getAllByTitle('Move up')
    fireEvent.click(upButtons[1])

    // Error should still be present for the mission that was originally empty
    await waitFor(() => {
      expect(screen.getByText('Mission statement cannot be empty')).toBeInTheDocument()
    })
  })
})
