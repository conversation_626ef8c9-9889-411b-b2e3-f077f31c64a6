/**
 * GalleryManager - Component for managing business unit gallery images
 */

import { useState } from 'react'
import Card from '../../components/ui/Card.jsx'
import Button from '../../components/ui/Button.jsx'

export default function GalleryManager({ gallery, onChange }) {
  const [errors, setErrors] = useState({})
  const [imageInputType, setImageInputType] = useState('url') // 'url' or 'upload'

  const handleTitleChange = (value) => {
    const updatedGallery = {
      ...gallery,
      title: value,
    }
    onChange(updatedGallery)

    // Clear title error if it exists
    if (errors.title) {
      setErrors((prev) => ({ ...prev, title: null }))
    }
  }

  const handleDescriptionChange = (value) => {
    const updatedGallery = {
      ...gallery,
      description: value,
    }
    onChange(updatedGallery)

    // Clear description error if it exists
    if (errors.description) {
      setErrors((prev) => ({ ...prev, description: null }))
    }
  }

  const handleImageChange = (index, field, value) => {
    const updatedImages = [...(gallery.images || [])]
    updatedImages[index] = {
      ...updatedImages[index],
      [field]: value,
    }

    const updatedGallery = {
      ...gallery,
      images: updatedImages,
    }
    onChange(updatedGallery)

    // Clear image-specific errors
    const errorKey = `image_${index}_${field}`
    if (errors[errorKey]) {
      setErrors((prev) => ({ ...prev, [errorKey]: null }))
    }
  }

  const addImage = () => {
    const updatedImages = [
      ...(gallery.images || []),
      {
        src: '',
        alt: '',
        title: '',
        description: '',
      },
    ]
    const updatedGallery = {
      ...gallery,
      images: updatedImages,
    }
    onChange(updatedGallery)
  }

  const removeImage = (index) => {
    if (window.confirm('Are you sure you want to remove this image?')) {
      const updatedImages = (gallery.images || []).filter((_, i) => i !== index)
      const updatedGallery = {
        ...gallery,
        images: updatedImages,
      }
      onChange(updatedGallery)

      // Clear any errors for this image
      const newErrors = { ...errors }
      Object.keys(newErrors).forEach((key) => {
        if (key.startsWith(`image_${index}_`)) {
          delete newErrors[key]
        }
      })
      setErrors(newErrors)
    }
  }

  const moveImage = (index, direction) => {
    const images = [...(gallery.images || [])]
    const newIndex = direction === 'up' ? index - 1 : index + 1

    if (newIndex >= 0 && newIndex < images.length) {
      ;[images[index], images[newIndex]] = [images[newIndex], images[index]]

      const updatedGallery = {
        ...gallery,
        images,
      }
      onChange(updatedGallery)
    }
  }

  const handleFileUpload = (index, file) => {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      setErrors((prev) => ({
        ...prev,
        [`image_${index}_src`]: 'Only JPEG, PNG, and WebP images are allowed',
      }))
      return
    }

    // Validate file size (5MB limit)
    const maxSize = 5 * 1024 * 1024 // 5MB in bytes
    if (file.size > maxSize) {
      setErrors((prev) => ({
        ...prev,
        [`image_${index}_src`]: 'Image size must be less than 5MB',
      }))
      return
    }

    // For now, we'll create a data URL for preview
    // In a real implementation, this would upload to a server
    const reader = new FileReader()
    reader.onload = (e) => {
      handleImageChange(index, 'src', e.target.result)

      // Auto-generate alt text from filename if not provided
      if (!gallery.images[index]?.alt) {
        const filename = file.name.replace(/\.[^/.]+$/, '') // Remove extension
        const altText = filename.replace(/[-_]/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())
        handleImageChange(index, 'alt', altText)
      }
    }
    reader.readAsDataURL(file)
  }

  const validateGallery = () => {
    const newErrors = {}

    // Validate title (optional but if provided should be reasonable length)
    if (gallery.title && gallery.title.trim().length > 100) {
      newErrors.title = 'Title must be 100 characters or less'
    }

    // Validate description (optional but if provided should be reasonable length)
    if (gallery.description && gallery.description.trim().length > 500) {
      newErrors.description = 'Description must be 500 characters or less'
    }

    // Validate gallery images
    if (gallery.images && gallery.images.length > 0) {
      gallery.images.forEach((image, index) => {
        // Validate src (required)
        if (!image.src || image.src.trim().length === 0) {
          newErrors[`image_${index}_src`] = 'Image URL or file is required'
        } else if (image.src.startsWith('http') && !isValidUrl(image.src)) {
          newErrors[`image_${index}_src`] = 'Please enter a valid image URL'
        }

        // Validate alt text (required for accessibility)
        if (!image.alt || image.alt.trim().length === 0) {
          newErrors[`image_${index}_alt`] = 'Alt text is required for accessibility'
        } else if (image.alt.trim().length > 200) {
          newErrors[`image_${index}_alt`] = 'Alt text must be 200 characters or less'
        }

        // Validate title (optional but if provided should be reasonable length)
        if (image.title && image.title.trim().length > 100) {
          newErrors[`image_${index}_title`] = 'Title must be 100 characters or less'
        }

        // Validate description (optional but if provided should be reasonable length)
        if (image.description && image.description.trim().length > 300) {
          newErrors[`image_${index}_description`] = 'Description must be 300 characters or less'
        }
      })
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const isValidUrl = (string) => {
    try {
      new URL(string)
      return true
    } catch {
      return false
    }
  }

  // Validate on data changes
  useState(() => {
    validateGallery()
  }, [gallery])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-xl font-semibold text-white mb-2">Gallery Management</h2>
        <p className="text-zinc-400">
          Manage images and visual content for this business unit's gallery.
        </p>
      </div>

      {/* Gallery Title and Description */}
      <Card padding="lg">
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-white">Section Information</h3>

          <div>
            <label className="block text-sm font-medium text-white mb-2">Gallery Title</label>
            <input
              type="text"
              value={gallery.title || ''}
              onChange={(e) => handleTitleChange(e.target.value)}
              placeholder="e.g., Our Gallery, Photo Gallery, Visual Showcase"
              className={`w-full px-3 py-2 bg-zinc-800 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors ${
                errors.title
                  ? 'border-red-500 focus:border-red-400'
                  : 'border-zinc-700 focus:border-amber-500'
              }`}
            />
            {errors.title && <p className="mt-1 text-sm text-red-400">{errors.title}</p>}
            <p className="mt-1 text-sm text-zinc-500">Optional title for the gallery section</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-white mb-2">Gallery Description</label>
            <textarea
              value={gallery.description || ''}
              onChange={(e) => handleDescriptionChange(e.target.value)}
              placeholder="Brief description of the gallery content..."
              rows={3}
              className={`w-full px-3 py-2 bg-zinc-800 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors resize-vertical ${
                errors.description
                  ? 'border-red-500 focus:border-red-400'
                  : 'border-zinc-700 focus:border-amber-500'
              }`}
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-400">{errors.description}</p>
            )}
            <p className="mt-1 text-sm text-zinc-500">
              Optional description to provide context for the gallery
            </p>
          </div>
        </div>
      </Card>

      {/* Image Input Type Selection */}
      <Card padding="lg">
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-white">Image Input Method</h3>
          <div className="flex gap-4">
            <label className="flex items-center">
              <input
                type="radio"
                name="imageInputType"
                value="url"
                checked={imageInputType === 'url'}
                onChange={(e) => setImageInputType(e.target.value)}
                className="mr-2 text-amber-500 focus:ring-amber-500"
              />
              <span className="text-white">Image URL</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="imageInputType"
                value="upload"
                checked={imageInputType === 'upload'}
                onChange={(e) => setImageInputType(e.target.value)}
                className="mr-2 text-amber-500 focus:ring-amber-500"
              />
              <span className="text-white">File Upload</span>
            </label>
          </div>
          <p className="text-sm text-zinc-400">
            {imageInputType === 'url'
              ? 'Enter image URLs from external sources or your CDN'
              : 'Upload image files directly (JPEG, PNG, WebP up to 5MB)'}
          </p>
        </div>
      </Card>

      {/* Gallery Images */}
      <Card padding="lg">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-white">Gallery Images</h3>
            <Button onClick={addImage} size="sm">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4v16m8-8H4"
                />
              </svg>
              Add Image
            </Button>
          </div>

          {!gallery.images || gallery.images.length === 0 ? (
            <div className="text-center py-8">
              <div className="w-12 h-12 bg-zinc-700 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg
                  className="w-6 h-6 text-zinc-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <h4 className="text-sm font-medium text-white mb-1">No Images Added</h4>
              <p className="text-xs text-zinc-400 mb-4">Add your first image to get started</p>
              <Button onClick={addImage} size="sm" variant="secondary">
                Add First Image
              </Button>
            </div>
          ) : (
            <div className="space-y-6">
              {gallery.images.map((image, index) => (
                <div key={index} className="bg-zinc-800 rounded-lg p-4 border border-zinc-700">
                  <div className="flex items-start gap-4">
                    {/* Move Controls */}
                    <div className="flex flex-col gap-1 pt-2">
                      <button
                        onClick={() => moveImage(index, 'up')}
                        disabled={index === 0}
                        className="p-1 text-zinc-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Move up"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 15l7-7 7 7"
                          />
                        </svg>
                      </button>
                      <button
                        onClick={() => moveImage(index, 'down')}
                        disabled={index === gallery.images.length - 1}
                        className="p-1 text-zinc-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Move down"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 9l-7 7-7-7"
                          />
                        </svg>
                      </button>
                    </div>

                    {/* Image Preview */}
                    <div className="w-24 h-24 bg-zinc-700 rounded-lg overflow-hidden flex-shrink-0">
                      {image.src ? (
                        <img
                          src={image.src}
                          alt={image.alt || 'Gallery image'}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.target.style.display = 'none'
                            e.target.nextSibling.style.display = 'flex'
                          }}
                        />
                      ) : null}
                      <div
                        className="w-full h-full flex items-center justify-center text-zinc-400"
                        style={{ display: image.src ? 'none' : 'flex' }}
                      >
                        <svg
                          className="w-8 h-8"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                    </div>

                    {/* Form Fields */}
                    <div className="flex-1 space-y-4">
                      {/* Image Source */}
                      <div>
                        {imageInputType === 'url' ? (
                          <div>
                            <label className="block text-sm font-medium text-white mb-2">
                              Image URL <span className="text-red-400">*</span>
                            </label>
                            <input
                              type="text"
                              value={image.src || ''}
                              onChange={(e) => handleImageChange(index, 'src', e.target.value)}
                              placeholder="https://example.com/image.jpg"
                              className={`w-full px-3 py-2 bg-zinc-800 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors ${
                                errors[`image_${index}_src`]
                                  ? 'border-red-500 focus:border-red-400'
                                  : 'border-zinc-700 focus:border-amber-500'
                              }`}
                            />
                            {errors[`image_${index}_src`] && (
                              <p className="mt-1 text-sm text-red-400">
                                {errors[`image_${index}_src`]}
                              </p>
                            )}
                          </div>
                        ) : (
                          <div>
                            <label className="block text-sm font-medium text-white mb-2">
                              Image File <span className="text-red-400">*</span>
                            </label>
                            <input
                              type="file"
                              accept="image/jpeg,image/jpg,image/png,image/webp"
                              onChange={(e) => {
                                const file = e.target.files[0]
                                if (file) {
                                  handleFileUpload(index, file)
                                }
                              }}
                              className="block w-full text-sm text-zinc-400 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-amber-600 file:text-white hover:file:bg-amber-700 file:cursor-pointer cursor-pointer"
                            />
                            {errors[`image_${index}_src`] && (
                              <p className="mt-1 text-sm text-red-400">
                                {errors[`image_${index}_src`]}
                              </p>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Alt Text */}
                      <div>
                        <label className="block text-sm font-medium text-white mb-2">
                          Alt Text <span className="text-red-400">*</span>
                        </label>
                        <input
                          type="text"
                          value={image.alt || ''}
                          onChange={(e) => handleImageChange(index, 'alt', e.target.value)}
                          placeholder="Describe the image for accessibility"
                          className={`w-full px-3 py-2 bg-zinc-800 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors ${
                            errors[`image_${index}_alt`]
                              ? 'border-red-500 focus:border-red-400'
                              : 'border-zinc-700 focus:border-amber-500'
                          }`}
                        />
                        {errors[`image_${index}_alt`] && (
                          <p className="mt-1 text-sm text-red-400">
                            {errors[`image_${index}_alt`]}
                          </p>
                        )}
                        <p className="mt-1 text-sm text-zinc-500">
                          Required for accessibility - describe what's in the image
                        </p>
                      </div>

                      {/* Title and Description */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-white mb-2">Title</label>
                          <input
                            type="text"
                            value={image.title || ''}
                            onChange={(e) => handleImageChange(index, 'title', e.target.value)}
                            placeholder="Optional image title"
                            className={`w-full px-3 py-2 bg-zinc-800 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors ${
                              errors[`image_${index}_title`]
                                ? 'border-red-500 focus:border-red-400'
                                : 'border-zinc-700 focus:border-amber-500'
                            }`}
                          />
                          {errors[`image_${index}_title`] && (
                            <p className="mt-1 text-sm text-red-400">
                              {errors[`image_${index}_title`]}
                            </p>
                          )}
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-white mb-2">
                            Description
                          </label>
                          <input
                            type="text"
                            value={image.description || ''}
                            onChange={(e) =>
                              handleImageChange(index, 'description', e.target.value)
                            }
                            placeholder="Optional image description"
                            className={`w-full px-3 py-2 bg-zinc-800 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors ${
                              errors[`image_${index}_description`]
                                ? 'border-red-500 focus:border-red-400'
                                : 'border-zinc-700 focus:border-amber-500'
                            }`}
                          />
                          {errors[`image_${index}_description`] && (
                            <p className="mt-1 text-sm text-red-400">
                              {errors[`image_${index}_description`]}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Remove Button */}
                    <div className="pt-6">
                      <button
                        onClick={() => removeImage(index)}
                        className="p-2 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg transition-colors"
                        title="Remove image"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </Card>

      {/* Preview */}
      {gallery.images && gallery.images.length > 0 && (
        <Card padding="lg">
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-white">Preview</h3>
            <div className="bg-zinc-900 rounded-lg p-6 border border-zinc-700">
              {gallery.title && (
                <h4 className="text-xl font-semibold text-white mb-2">{gallery.title}</h4>
              )}
              {gallery.description && <p className="text-zinc-400 mb-6">{gallery.description}</p>}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {gallery.images.map((image, index) => (
                  <div key={index} className="bg-zinc-800 rounded-lg overflow-hidden">
                    <div className="aspect-video bg-zinc-700 flex items-center justify-center">
                      {image.src ? (
                        <img
                          src={image.src}
                          alt={image.alt}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.target.style.display = 'none'
                            e.target.nextSibling.style.display = 'flex'
                          }}
                        />
                      ) : null}
                      <div
                        className="w-full h-full flex items-center justify-center text-zinc-400"
                        style={{ display: image.src ? 'none' : 'flex' }}
                      >
                        <svg
                          className="w-12 h-12"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                    </div>
                    {(image.title || image.description) && (
                      <div className="p-3">
                        {image.title && (
                          <h5 className="text-sm font-medium text-white mb-1">{image.title}</h5>
                        )}
                        {image.description && (
                          <p className="text-xs text-zinc-400">{image.description}</p>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Validation Summary */}
      {Object.keys(errors).length > 0 && (
        <Card padding="lg">
          <div className="flex items-start gap-3">
            <svg
              className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <div>
              <h4 className="text-sm font-medium text-red-400 mb-2">
                Please fix the following errors:
              </h4>
              <ul className="text-sm text-red-300 space-y-1">
                {Object.entries(errors).map(([key, error]) => (
                  <li key={key}>• {error}</li>
                ))}
              </ul>
            </div>
          </div>
        </Card>
      )}
    </div>
  )
}
