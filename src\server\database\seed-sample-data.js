/**
 * Seed sample business units data for development
 * Creates basic business units if none exist in the database
 */

import { v4 as uuidv4 } from 'uuid'

/**
 * Sample business units data for development
 */
const sampleBusinessUnits = [
    {
        id: uuidv4(),
        name: '<PERSON><PERSON> <PERSON><PERSON>',
        slug: 'pbcigi',
        badge_text: '<PERSON>usa<PERSON> B<PERSON>',
        badge_variant: 'primary',
        badge_icon: '🏢',
        description: 'Pusat Bisnis dan Komersialisasi Institut Global Indonesia',
        theme_color_from: '#3b82f6',
        theme_color_to: '#1d4ed8',
        theme_color_primary: '#3b82f6',
        layout_config: JSON.stringify({ productsLayout: 'grid' }),
        about_image: '/assets/unit-usaha/cigi-global.jpg',
        about_content: 'PBC IGI adalah pusat bisnis dan komersialisasi yang mengelola berbagai unit usaha di bawah Institut Global Indonesia.',
        highlight_text: 'PBC IGI',
        highlight_class: 'text-blue-400 font-semibold',
        vision: 'Menjadi pusat bisnis terdepan yang mengintegrasikan inovasi dan teknologi untuk kemajuan ekonomi.',
        mission: JSON.stringify([
            'Mengembangkan unit-unit usaha yang berkelanjutan',
            'Memberikan layanan bisnis berkualitas tinggi',
            'Menciptakan lapangan kerja dan peluang usaha',
            'Mendukung pengembangan ekonomi lokal'
        ]),
        badges: JSON.stringify([
            { text: 'Terpercaya', variant: 'primary' },
            { text: 'Inovatif', variant: 'success' },
            { text: 'Profesional', variant: 'info' }
        ]),
        contact_address: 'Institut Global Indonesia, Jawa Barat',
        contact_phone: '+62 21-1234-5678',
        contact_email: '<EMAIL>',
        contact_hours: 'Senin - Jumat: 08:00 - 17:00 WIB'
    },
    {
        id: uuidv4(),
        name: 'Cigi Net',
        slug: 'ciginet',
        badge_text: 'Internet Provider',
        badge_variant: 'info',
        badge_icon: '🌐',
        description: 'Penyedia layanan internet dan teknologi informasi',
        theme_color_from: '#06b6d4',
        theme_color_to: '#0891b2',
        theme_color_primary: '#06b6d4',
        layout_config: JSON.stringify({ productsLayout: 'grid' }),
        about_image: '/assets/unit-usaha/cigi-net.jpg',
        about_content: 'Cigi Net menyediakan layanan internet berkualitas tinggi dan solusi teknologi informasi untuk masyarakat dan bisnis.',
        highlight_text: 'Cigi Net',
        highlight_class: 'text-cyan-400 font-semibold',
        vision: 'Menjadi penyedia layanan internet terdepan yang menghubungkan masyarakat dengan dunia digital.',
        mission: JSON.stringify([
            'Menyediakan layanan internet berkualitas tinggi',
            'Mengembangkan infrastruktur teknologi informasi',
            'Memberikan dukungan teknis terbaik',
            'Mendukung transformasi digital masyarakat'
        ]),
        badges: JSON.stringify([
            { text: 'Berkualitas', variant: 'info' },
            { text: 'Terpercaya', variant: 'primary' },
            { text: 'Inovatif', variant: 'success' }
        ]),
        contact_address: 'Institut Global Indonesia, Jawa Barat',
        contact_phone: '+62 21-2345-6789',
        contact_email: '<EMAIL>',
        contact_hours: 'Senin - Minggu: 24 Jam'
    },
    {
        id: uuidv4(),
        name: 'Cigi Mart',
        slug: 'cigimart',
        badge_text: 'Retail',
        badge_variant: 'success',
        badge_icon: '🛒',
        description: 'Toko retail dan marketplace online',
        theme_color_from: '#10b981',
        theme_color_to: '#059669',
        theme_color_primary: '#10b981',
        layout_config: JSON.stringify({ productsLayout: 'showcase' }),
        about_image: '/assets/unit-usaha/cigi-mart.jpg',
        about_content: 'Cigi Mart adalah toko retail modern yang menyediakan berbagai kebutuhan sehari-hari dengan layanan online dan offline.',
        highlight_text: 'Cigi Mart',
        highlight_class: 'text-green-400 font-semibold',
        vision: 'Menjadi toko retail terpercaya yang memenuhi kebutuhan masyarakat dengan pelayanan terbaik.',
        mission: JSON.stringify([
            'Menyediakan produk berkualitas dengan harga terjangkau',
            'Memberikan pelayanan pelanggan yang excellent',
            'Mengembangkan platform e-commerce yang mudah digunakan',
            'Mendukung UMKM lokal melalui marketplace'
        ]),
        badges: JSON.stringify([
            { text: 'Terpercaya', variant: 'success' },
            { text: 'Lengkap', variant: 'primary' },
            { text: 'Terjangkau', variant: 'warning' }
        ]),
        contact_address: 'Institut Global Indonesia, Jawa Barat',
        contact_phone: '+62 21-3456-7890',
        contact_email: '<EMAIL>',
        contact_hours: 'Senin - Minggu: 08:00 - 22:00 WIB'
    },
    {
        id: uuidv4(),
        name: 'Cigi Food',
        slug: 'cigifood',
        badge_text: 'F&B',
        badge_variant: 'warning',
        badge_icon: '🍽️',
        description: 'Layanan makanan dan minuman',
        theme_color_from: '#f59e0b',
        theme_color_to: '#d97706',
        theme_color_primary: '#f59e0b',
        layout_config: JSON.stringify({ productsLayout: 'carousel' }),
        about_image: '/assets/unit-usaha/cigi-food.jpg',
        about_content: 'Cigi Food menyajikan berbagai hidangan lezat dan minuman segar dengan cita rasa autentik dan pelayanan terbaik.',
        highlight_text: 'Cigi Food',
        highlight_class: 'text-amber-400 font-semibold',
        vision: 'Menjadi penyedia layanan makanan dan minuman terbaik dengan cita rasa yang tak terlupakan.',
        mission: JSON.stringify([
            'Menyajikan makanan berkualitas dengan bahan segar',
            'Memberikan pengalaman kuliner yang memuaskan',
            'Mengembangkan menu inovatif dan variatif',
            'Menjaga standar kebersihan dan kesehatan makanan'
        ]),
        badges: JSON.stringify([
            { text: 'Lezat', variant: 'warning' },
            { text: 'Segar', variant: 'success' },
            { text: 'Higienis', variant: 'info' }
        ]),
        contact_address: 'Institut Global Indonesia, Jawa Barat',
        contact_phone: '+62 21-4567-8901',
        contact_email: '<EMAIL>',
        contact_hours: 'Senin - Minggu: 10:00 - 22:00 WIB'
    },
    {
        id: uuidv4(),
        name: 'Cigi Archery',
        slug: 'cigiarchery',
        badge_text: 'Tim Panahan Desa',
        badge_variant: 'warning',
        badge_icon: '🏹',
        description: 'Tim panahan masyarakat Desa Cimande Girang yang mengembangkan bakat olahraga',
        theme_color_from: '#9333ea',
        theme_color_to: '#a855f7',
        theme_color_primary: '#a855f7',
        layout_config: JSON.stringify({ productsLayout: 'list' }),
        about_image: '/assets/unit-usaha/cigi-archery.jpg',
        about_content: 'Cigi Archery adalah tim panahan masyarakat Desa Cimande Girang yang didirikan untuk mengembangkan bakat olahraga warga, khususnya generasi muda.',
        highlight_text: 'Cigi Archery',
        highlight_class: 'text-purple-400 font-semibold',
        vision: 'Menjadi tim panahan terdepan yang mengharumkan nama desa dan mengembangkan potensi olahraga masyarakat',
        mission: JSON.stringify([
            'Mengembangkan bakat panahan generasi muda desa',
            'Membangun prestasi olahraga untuk kemajuan desa',
            'Membentuk karakter sportif dan disiplin melalui olahraga',
            'Menjadi wadah pengembangan olahraga panahan yang berkelanjutan'
        ]),
        badges: JSON.stringify([
            { text: 'Prestasi Tinggi', variant: 'warning' },
            { text: 'Pelatihan Berkualitas', variant: 'success' },
            { text: 'Semangat Sportif', variant: 'info' }
        ]),
        contact_address: 'Desa Cimande Girang, Kecamatan Caringin, Kabupaten Bogor, Jawa Barat',
        contact_phone: '+62 812-3456-7891',
        contact_email: '<EMAIL>',
        contact_hours: 'Senin - Sabtu: 16:00 - 18:00 WIB'
    }
]

/**
 * Seed sample data using appropriate database adapter
 */
export async function seedSampleData() {
    try {
        console.log('🌱 Seeding sample business units data...')

        // Try MySQL first
        try {
            const { executeQuery } = await import('./mysql-config.js')

            // Check if any business units exist
            const existing = await executeQuery('SELECT COUNT(*) as count FROM business_units')
            if (existing[0].count > 0) {
                console.log('📦 Database already has business units, skipping seed')
                return { success: true, message: 'Data already exists' }
            }

            // Insert sample data
            for (const unit of sampleBusinessUnits) {
                const query = `
          INSERT INTO business_units (
            id, name, slug, badge_text, badge_variant, badge_icon, description,
            theme_color_from, theme_color_to, theme_color_primary, layout_config,
            about_image, about_content, highlight_text, highlight_class,
            vision, mission, badges, contact_address, contact_phone, contact_email, contact_hours
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `

                await executeQuery(query, [
                    unit.id, unit.name, unit.slug, unit.badge_text, unit.badge_variant, unit.badge_icon,
                    unit.description, unit.theme_color_from, unit.theme_color_to, unit.theme_color_primary,
                    unit.layout_config, unit.about_image, unit.about_content, unit.highlight_text,
                    unit.highlight_class, unit.vision, unit.mission, unit.badges,
                    unit.contact_address, unit.contact_phone, unit.contact_email, unit.contact_hours
                ])

                console.log(`✅ Seeded: ${unit.name}`)
            }

            console.log(`🎉 Successfully seeded ${sampleBusinessUnits.length} business units to MySQL`)
            return { success: true, count: sampleBusinessUnits.length, adapter: 'mysql' }

        } catch (mysqlError) {
            console.log('MySQL not available, trying SQLite...')

            // Fallback to SQLite
            const Database = (await import('better-sqlite3')).default
            const path = (await import('path')).default
            const { fileURLToPath } = await import('url')

            const __filename = fileURLToPath(import.meta.url)
            const __dirname = path.dirname(__filename)
            const DB_PATH = path.join(__dirname, '../../../data/modular_system.db')

            const db = new Database(DB_PATH)

            try {
                // Initialize schema first
                db.pragma('foreign_keys = ON')

                // Create business_units table if it doesn't exist
                db.exec(`
          CREATE TABLE IF NOT EXISTS business_units (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            slug TEXT UNIQUE NOT NULL,
            badge_text TEXT,
            badge_variant TEXT DEFAULT 'primary',
            badge_icon TEXT,
            description TEXT,
            theme_color_from TEXT,
            theme_color_to TEXT,
            theme_color_primary TEXT,
            layout_config TEXT,
            about_image TEXT,
            about_content TEXT,
            highlight_text TEXT,
            highlight_class TEXT,
            vision TEXT,
            mission TEXT,
            badges TEXT,
            contact_address TEXT,
            contact_phone TEXT,
            contact_email TEXT,
            contact_hours TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
          )
        `)

                // Check if any business units exist
                const existing = db.prepare('SELECT COUNT(*) as count FROM business_units').get()
                if (existing.count > 0) {
                    console.log('📦 SQLite database already has business units, skipping seed')
                    return { success: true, message: 'Data already exists' }
                }

                // Insert sample data
                const insertStmt = db.prepare(`
          INSERT INTO business_units (
            id, name, slug, badge_text, badge_variant, badge_icon, description,
            theme_color_from, theme_color_to, theme_color_primary, layout_config,
            about_image, about_content, highlight_text, highlight_class,
            vision, mission, badges, contact_address, contact_phone, contact_email, contact_hours
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `)

                for (const unit of sampleBusinessUnits) {
                    insertStmt.run([
                        unit.id, unit.name, unit.slug, unit.badge_text, unit.badge_variant, unit.badge_icon,
                        unit.description, unit.theme_color_from, unit.theme_color_to, unit.theme_color_primary,
                        unit.layout_config, unit.about_image, unit.about_content, unit.highlight_text,
                        unit.highlight_class, unit.vision, unit.mission, unit.badges,
                        unit.contact_address, unit.contact_phone, unit.contact_email, unit.contact_hours
                    ])

                    console.log(`✅ Seeded: ${unit.name}`)
                }

                console.log(`🎉 Successfully seeded ${sampleBusinessUnits.length} business units to SQLite`)
                return { success: true, count: sampleBusinessUnits.length, adapter: 'sqlite' }

            } finally {
                db.close()
            }
        }

    } catch (error) {
        console.error('❌ Error seeding sample data:', error)
        throw error
    }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    seedSampleData()
        .then(result => {
            console.log('Seed result:', result)
            process.exit(0)
        })
        .catch(error => {
            console.error('Seed failed:', error)
            process.exit(1)
        })
}

export default seedSampleData
