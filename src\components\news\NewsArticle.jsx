/**
 * NewsArticle - Komponen tampilan artikel berita individual
 */

import { useState, useEffect, useCallback } from 'react'
import { useParams, Link, useNavigate } from 'react-router-dom'
import ReactMarkdown from 'react-markdown'
import Card from '../ui/Card.jsx'
import Badge from '../ui/Badge.jsx'
import Button from '../ui/Button.jsx'
import newsService from '../../services/NewsService.js'

export default function NewsArticle() {
  const { slug } = useParams()
  const navigate = useNavigate()
  const [article, setArticle] = useState(null)
  const [relatedArticles, setRelatedArticles] = useState([])
  const [category, setCategory] = useState(null)
  const [loading, setLoading] = useState(true)
  const [notFound, setNotFound] = useState(false)

  const loadArticle = useCallback(() => {
    setLoading(true)
    setNotFound(false)

    try {
      const articleData = newsService.getArticleBySlug(slug)

      if (!articleData || articleData.status !== 'published') {
        setNotFound(true)
        setLoading(false)
        return
      }

      setArticle(articleData)

      // Muat kategori
      const categoryData = newsService.getCategory(articleData.categoryId)
      setCategory(categoryData)

      // Muat artikel terkait (kategori sama, kecuali artikel saat ini)
      const relatedData = newsService
        .getArticlesByCategory(articleData.categoryId)
        .filter((a) => a.id !== articleData.id)
        .slice(0, 3)

      setRelatedArticles(relatedData)
    } catch (error) {
      console.error('Error loading article:', error)
      setNotFound(true)
    } finally {
      setLoading(false)
    }
  }, [slug])

  useEffect(() => {
    loadArticle()
  }, [loadArticle])

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: article.title,
        text: article.excerpt,
        url: window.location.href,
      })
    } else {
      // Fallback: salin ke clipboard
      navigator.clipboard.writeText(window.location.href)
      alert('URL artikel disalin ke clipboard!')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-black text-white pt-16">
        <div className="container mx-auto px-4 py-12">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-amber-600 border-t-transparent rounded-full animate-spin mx-auto"></div>
            <p className="text-zinc-400 mt-4">Memuat artikel...</p>
          </div>
        </div>
      </div>
    )
  }

  if (notFound) {
    return (
      <div className="min-h-screen bg-black text-white pt-16">
        <div className="container mx-auto px-4 py-12">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-white mb-4">Artikel Tidak Ditemukan</h1>
            <p className="text-zinc-400 mb-8">
              Artikel yang Anda cari tidak ada atau telah dihapus.
            </p>
            <Button onClick={() => navigate('/news')}>Kembali ke Berita</Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-black text-white pt-16">
      <div className="container mx-auto px-4 py-12">
        {/* Breadcrumb */}
        <nav className="mb-8">
          <div className="flex items-center space-x-2 text-sm text-zinc-400">
            <Link to="/" className="hover:text-amber-400 transition-colors">
              Beranda
            </Link>
            <span>/</span>
            <Link to="/news" className="hover:text-amber-400 transition-colors">
              Berita
            </Link>
            <span>/</span>
            <span className="text-white">{article.title}</span>
          </div>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Konten Utama */}
          <div className="lg:col-span-3">
            <Card padding="lg">
              {/* Header Artikel */}
              <div className="mb-8">
                {/* Badge Kategori dan Unggulan */}
                <div className="flex items-center gap-3 mb-4">
                  {category && (
                    <Badge
                      variant="secondary"
                      style={{
                        backgroundColor: category.color + '20',
                        color: category.color,
                      }}
                    >
                      {category.name}
                    </Badge>
                  )}
                  {article.featured && <Badge variant="warning">Unggulan</Badge>}
                </div>

                {/* Judul */}
                <h1 className="text-4xl font-bold text-white mb-4 leading-tight">
                  {article.title}
                </h1>

                {/* Ringkasan */}
                <p className="text-xl text-zinc-300 mb-6 leading-relaxed">{article.excerpt}</p>

                {/* Informasi Meta */}
                <div className="flex flex-wrap items-center gap-6 text-sm text-zinc-400 border-b border-zinc-700 pb-6">
                  <div className="flex items-center gap-2">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                      />
                    </svg>
                    <span>Oleh {article.author}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                    <span>Diterbitkan {formatDate(article.publishedAt)}</span>
                  </div>
                  {article.updatedAt !== article.createdAt && (
                    <div className="flex items-center gap-2">
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                        />
                      </svg>
                      <span>Diperbarui {formatDate(article.updatedAt)}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Konten Artikel */}
              <div className="prose prose-invert prose-lg max-w-none">
                <ReactMarkdown
                  components={{
                    h1: ({ children }) => (
                      <h1 className="text-3xl font-bold text-white mt-8 mb-4">{children}</h1>
                    ),
                    h2: ({ children }) => (
                      <h2 className="text-2xl font-bold text-white mt-6 mb-3">{children}</h2>
                    ),
                    h3: ({ children }) => (
                      <h3 className="text-xl font-bold text-white mt-4 mb-2">{children}</h3>
                    ),
                    p: ({ children }) => (
                      <p className="text-zinc-200 mb-4 leading-relaxed">{children}</p>
                    ),
                    ul: ({ children }) => (
                      <ul className="text-zinc-200 mb-4 list-disc list-inside space-y-1">
                        {children}
                      </ul>
                    ),
                    ol: ({ children }) => (
                      <ol className="text-zinc-200 mb-4 list-decimal list-inside space-y-1">
                        {children}
                      </ol>
                    ),
                    blockquote: ({ children }) => (
                      <blockquote className="border-l-4 border-amber-500 pl-4 italic text-zinc-300 my-4">
                        {children}
                      </blockquote>
                    ),
                    code: ({ children }) => (
                      <code className="bg-zinc-800 text-amber-400 px-1 py-0.5 rounded text-sm">
                        {children}
                      </code>
                    ),
                    pre: ({ children }) => (
                      <pre className="bg-zinc-800 p-4 rounded-lg overflow-x-auto my-4">
                        {children}
                      </pre>
                    ),
                  }}
                >
                  {article.content}
                </ReactMarkdown>
              </div>

              {/* Tag */}
              {article.tags && article.tags.length > 0 && (
                <div className="mt-8 pt-6 border-t border-zinc-700">
                  <h3 className="text-sm font-medium text-zinc-400 mb-3">Tag:</h3>
                  <div className="flex flex-wrap gap-2">
                    {article.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-3 py-1 text-sm bg-zinc-800 text-zinc-300 rounded-full hover:bg-zinc-700 transition-colors"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Bagikan */}
              <div className="mt-8 pt-6 border-t border-zinc-700">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-zinc-400">Bagikan artikel ini:</h3>
                  <Button variant="secondary" size="sm" onClick={handleShare}>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
                      />
                    </svg>
                    Bagikan
                  </Button>
                </div>
              </div>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Artikel Terkait */}
            {relatedArticles.length > 0 && (
              <Card padding="lg">
                <h3 className="text-lg font-semibold text-white mb-4">Artikel Terkait</h3>
                <div className="space-y-4">
                  {relatedArticles.map((relatedArticle) => (
                    <div
                      key={relatedArticle.id}
                      className="border-b border-zinc-700 pb-4 last:border-b-0 last:pb-0"
                    >
                      <h4 className="font-medium text-white mb-2 hover:text-amber-400 transition-colors">
                        <Link to={`/news/${relatedArticle.slug}`}>{relatedArticle.title}</Link>
                      </h4>
                      <p className="text-sm text-zinc-400 mb-2">
                        {relatedArticle.excerpt.substring(0, 100)}...
                      </p>
                      <p className="text-xs text-zinc-500">
                        {formatDate(relatedArticle.publishedAt)}
                      </p>
                    </div>
                  ))}
                </div>
              </Card>
            )}

            {/* Kembali ke Berita */}
            <Card padding="lg">
              <Button variant="secondary" className="w-full" onClick={() => navigate('/news')}>
                ← Kembali ke Semua Berita
              </Button>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
