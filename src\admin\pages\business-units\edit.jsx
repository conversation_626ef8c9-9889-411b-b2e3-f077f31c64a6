/**
 * BusinessUnitsEdit - Page for editing existing business units
 */

import { useState, useEffect, useCallback } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import Card from '../../../components/ui/Card.jsx'
import Button from '../../../components/ui/Button.jsx'
import Badge from '../../../components/ui/Badge.jsx'
import BusinessUnitEditor from '../../components/BusinessUnitEditor.jsx'
import businessUnitService from '../../../services/BusinessUnitService.js'
import { useToast } from '../../../components/ui/Toast.jsx'

export default function BusinessUnitsEdit() {
  const [selectedBusinessUnit, setSelectedBusinessUnit] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const navigate = useNavigate()
  const { id } = useParams()
  const { showToast, ToastContainer } = useToast()

  const fetchBusinessUnitForEdit = useCallback(async (businessUnitId) => {
    try {
      const businessUnit = await businessUnitService.getBusinessUnit(businessUnitId)
      setSelectedBusinessUnit(businessUnit)
    } catch (err) {
      console.error('Business unit not found in API:', businessUnitId)
      showToast('Business unit not found', 'error')
      navigate('/admin/business-units')
    }
  }, [navigate, showToast])

  // Load business unit data when component mounts
  useEffect(() => {
    if (id) {
      fetchBusinessUnitForEdit(id)
    }
  }, [id, fetchBusinessUnitForEdit])

  const handleSaveBusinessUnit = async (businessUnitData) => {
    setLoading(true)
    setError(null)

    try {
      // Validate required fields
      if (!businessUnitData.name || businessUnitData.name.trim().length < 3) {
        throw new Error('Business unit name must be at least 3 characters long')
      }

      if (!businessUnitData.description || businessUnitData.description.trim().length < 10) {
        throw new Error('Description must be at least 10 characters long')
      }

      // Update existing business unit
      const savedBusinessUnit = await businessUnitService.updateBusinessUnit(businessUnitData.id, businessUnitData)
      
      showToast('Business unit updated successfully', 'success')
      console.log('Business unit updated successfully:', savedBusinessUnit.name)

      // Navigate back to the business units list after saving
      navigate('/admin/business-units')
    } catch (err) {
      console.error('Error updating business unit:', err)
      setError(err.message || 'Failed to update business unit. Please try again.')
      showToast('Failed to update business unit', 'error')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    navigate('/admin/business-units')
  }

  if (!selectedBusinessUnit) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-zinc-700 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg
              className="w-8 h-8 text-zinc-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-white mb-2">Loading Business Unit</h3>
          <p className="text-zinc-400">Please wait while we load the business unit data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Cancel Button */}
      <div className="flex justify-end">
        <Button variant="secondary" onClick={handleCancel}>
          Cancel
        </Button>
      </div>

      {/* Error Display */}
      {error && (
        <Card padding="lg">
          <div className="flex items-center gap-3 text-red-400">
            <svg
              className="w-5 h-5 flex-shrink-0"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <p>{error}</p>
          </div>
        </Card>
      )}

      {/* Business Unit Editor */}
      <BusinessUnitEditor
        businessUnit={selectedBusinessUnit}
        onSave={handleSaveBusinessUnit}
        onCancel={handleCancel}
        mode="edit"
      />

      {/* Toast Notifications */}
      <ToastContainer />
    </div>
  )
}
