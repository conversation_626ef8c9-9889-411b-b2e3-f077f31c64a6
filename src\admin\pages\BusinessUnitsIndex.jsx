/**
 * BusinessUnitsIndex - Main listing page for business units management
 */

import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import Card from '../../components/ui/Card.jsx'
import Button from '../../components/ui/Button.jsx'
import Badge from '../../components/ui/Badge.jsx'
import BusinessUnitsList from '../components/BusinessUnitsList.jsx'
import businessUnitService from '../../services/BusinessUnitService.js'
import { useToast } from '../../components/ui/Toast.jsx'

export default function BusinessUnitsIndex() {
  const [businessUnits, setBusinessUnits] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const navigate = useNavigate()
  const { showToast, ToastContainer } = useToast()

  // Load business units data on component mount
  useEffect(() => {
    loadBusinessUnitsData()
  }, [])

  const loadBusinessUnitsData = async () => {
    setLoading(true)
    setError(null)

    try {
      const data = await businessUnitService.getAllBusinessUnits()
      setBusinessUnits(data)
    } catch (err) {
      console.error('Error loading business units:', err)
      setError('Failed to load business units. Please try again.')
      showToast('Failed to load business units', 'error')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateBusinessUnit = () => {
    navigate('/admin/business-units/create')
  }

  const handleEditBusinessUnit = (businessUnit) => {
    navigate(`/admin/business-units/${businessUnit.id}/edit`)
  }

  const handleViewBusinessUnit = (businessUnit) => {
    navigate(`/admin/business-units/${businessUnit.id}`)
  }

  const handleDeleteBusinessUnit = async (businessUnitId) => {
    try {
      await businessUnitService.deleteBusinessUnit(businessUnitId)

      // Remove from local state
      setBusinessUnits(prev => prev.filter((unit) => unit.id !== businessUnitId))

      showToast('Business unit deleted successfully', 'success')
      console.log('Business unit deleted successfully:', businessUnitId)
    } catch (err) {
      console.error('Error deleting business unit:', err)
      showToast('Failed to delete business unit', 'error')
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Business Units Management</h1>
          <p className="text-zinc-400">Manage your business unit details and content</p>
          {businessUnits.length > 0 && businessUnits[0].lastModified && (
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="warning" size="sm">
                Demo Mode
              </Badge>
              <span className="text-xs text-zinc-500">
                Using fallback data. Start backend server for dynamic features.
              </span>
            </div>
          )}
        </div>
        <Button onClick={handleCreateBusinessUnit}>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          New Business Unit
        </Button>
      </div>

      {/* Error Display */}
      {error && (
        <Card padding="lg">
          <div className="flex items-center justify-between p-4 bg-red-600/10 border border-red-600/20 rounded-lg">
            <div className="flex items-center gap-3 text-red-400">
              <svg
                className="w-5 h-5 flex-shrink-0"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <p>{error}</p>
            </div>
            <Button variant="secondary" size="sm" onClick={loadBusinessUnitsData}>
              Retry
            </Button>
          </div>
        </Card>
      )}

      {/* Business Units List */}
      <BusinessUnitsList
        businessUnits={businessUnits}
        onEdit={handleEditBusinessUnit}
        onView={handleViewBusinessUnit}
        onDelete={handleDeleteBusinessUnit}
        onRefresh={loadBusinessUnitsData}
        loading={loading}
      />

      {/* Toast Notifications */}
      <ToastContainer />
    </div>
  )
}
