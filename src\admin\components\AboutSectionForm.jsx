/**
 * AboutSectionForm - Form component for editing about section content
 */

import { useState, useEffect } from 'react'
import Card from '../../components/ui/Card.jsx'
import Button from '../../components/ui/Button.jsx'
import Badge from '../../components/ui/Badge.jsx'
import MissionManager from './MissionManager.jsx'
import BadgesManager from './BadgesManager.jsx'

export default function AboutSectionForm({ data, onFieldChange }) {
  const [formData, setFormData] = useState({
    aboutImage: '',
    aboutContent: '',
    highlightText: '',
    highlightClass: '',
    vision: '',
    mission: [],
    badges: [],
  })
  const [errors, setErrors] = useState({})
  const [showImagePreview, setShowImagePreview] = useState(false)

  // Initialize form data
  useEffect(() => {
    if (data) {
      setFormData({
        aboutImage: data.aboutImage || '',
        aboutContent: data.aboutContent || '',
        highlightText: data.highlightText || '',
        highlightClass: data.highlightClass || '',
        vision: data.vision || '',
        mission: data.mission || [],
        badges: data.badges || [],
      })
    }
  }, [data])

  const highlightClasses = [
    { value: '', label: 'None', preview: 'text-white' },
    { value: 'text-amber-400', label: 'Amber', preview: 'text-amber-400' },
    { value: 'text-blue-400', label: 'Blue', preview: 'text-blue-400' },
    { value: 'text-green-400', label: 'Green', preview: 'text-green-400' },
    { value: 'text-red-400', label: 'Red', preview: 'text-red-400' },
    { value: 'text-purple-400', label: 'Purple', preview: 'text-purple-400' },
    { value: 'font-bold text-amber-400', label: 'Bold Amber', preview: 'font-bold text-amber-400' },
    {
      value: 'font-semibold text-white bg-amber-600 px-2 py-1 rounded',
      label: 'Highlighted',
      preview: 'font-semibold text-white bg-amber-600 px-2 py-1 rounded',
    },
  ]

  const validateField = (name, value) => {
    const newErrors = { ...errors }

    switch (name) {
      case 'aboutContent':
        if (value && value.length > 2000) {
          newErrors.aboutContent = 'About content must be 2000 characters or less'
        } else {
          delete newErrors.aboutContent
        }
        break
      case 'vision':
        if (value && value.length > 1000) {
          newErrors.vision = 'Vision must be 1000 characters or less'
        } else {
          delete newErrors.vision
        }
        break
      case 'highlightText':
        if (value && value.length > 200) {
          newErrors.highlightText = 'Highlight text must be 200 characters or less'
        } else {
          delete newErrors.highlightText
        }
        break
      case 'aboutImage':
        if (value && !isValidUrl(value)) {
          newErrors.aboutImage = 'Please enter a valid URL'
        } else {
          delete newErrors.aboutImage
        }
        break
      default:
        break
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const isValidUrl = (string) => {
    try {
      new URL(string)
      return true
    } catch {
      return false
    }
  }

  const handleInputChange = (name, value) => {
    const newFormData = { ...formData, [name]: value }
    setFormData(newFormData)

    // Real-time validation
    validateField(name, value)

    // Notify parent component
    if (onFieldChange) {
      onFieldChange(name, value)
    }
  }

  const handleMissionChange = (missions) => {
    const newFormData = { ...formData, mission: missions }
    setFormData(newFormData)

    if (onFieldChange) {
      onFieldChange('mission', missions)
    }
  }

  const handleBadgesChange = (badges) => {
    const newFormData = { ...formData, badges: badges }
    setFormData(newFormData)

    if (onFieldChange) {
      onFieldChange('badges', badges)
    }
  }

  const ImagePreview = () => {
    if (!formData.aboutImage || !isValidUrl(formData.aboutImage)) return null

    return (
      <div className="mt-3">
        <div className="flex items-center gap-2 mb-2">
          <span className="text-sm text-zinc-400">Preview:</span>
          <Button size="sm" variant="ghost" onClick={() => setShowImagePreview(!showImagePreview)}>
            {showImagePreview ? 'Hide' : 'Show'} Preview
          </Button>
        </div>
        {showImagePreview && (
          <div className="relative">
            <img
              src={formData.aboutImage}
              alt="About section preview"
              className="max-w-full h-48 object-cover rounded-lg border border-zinc-700"
              onError={(e) => {
                e.target.style.display = 'none'
                e.target.nextSibling.style.display = 'block'
              }}
            />
            <div className="hidden p-4 bg-zinc-800 border border-zinc-700 rounded-lg text-center">
              <svg
                className="w-12 h-12 text-zinc-500 mx-auto mb-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
              <p className="text-sm text-zinc-400">Failed to load image</p>
            </div>
          </div>
        )}
      </div>
    )
  }

  const HighlightPreview = () => {
    if (!formData.highlightText) return null

    const selectedClass = highlightClasses.find((c) => c.value === formData.highlightClass)

    return (
      <div className="mt-3 p-4 bg-zinc-900 rounded-lg">
        <div className="flex items-center gap-2 mb-2">
          <span className="text-sm text-zinc-400">Preview:</span>
        </div>
        <p className="text-base">
          This is sample text with{' '}
          <span className={selectedClass?.preview || 'text-white'}>{formData.highlightText}</span>{' '}
          highlighted within it.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* About Image */}
      <Card padding="lg">
        <h3 className="text-lg font-semibold text-white mb-4">About Image</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-white mb-2">Image URL</label>
            <input
              type="url"
              value={formData.aboutImage}
              onChange={(e) => handleInputChange('aboutImage', e.target.value)}
              placeholder="https://example.com/image.jpg"
              className={`w-full px-3 py-2 bg-zinc-800 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors ${
                errors.aboutImage
                  ? 'border-red-500 focus:border-red-400'
                  : 'border-zinc-700 focus:border-amber-500'
              }`}
            />
            {errors.aboutImage && <p className="mt-1 text-sm text-red-400">{errors.aboutImage}</p>}
            <p className="mt-1 text-xs text-zinc-500">
              Enter a valid URL to an image that represents your business unit
            </p>
            <ImagePreview />
          </div>
        </div>
      </Card>

      {/* About Content */}
      <Card padding="lg">
        <h3 className="text-lg font-semibold text-white mb-4">About Content</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-white mb-2">About Description</label>
            <textarea
              value={formData.aboutContent}
              onChange={(e) => handleInputChange('aboutContent', e.target.value)}
              placeholder="Write a detailed description about your business unit..."
              rows={6}
              className={`w-full px-3 py-2 bg-zinc-800 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors resize-vertical ${
                errors.aboutContent
                  ? 'border-red-500 focus:border-red-400'
                  : 'border-zinc-700 focus:border-amber-500'
              }`}
            />
            <div className="flex items-center justify-between mt-1">
              {errors.aboutContent ? (
                <p className="text-sm text-red-400">{errors.aboutContent}</p>
              ) : (
                <p className="text-sm text-zinc-500">
                  {formData.aboutContent.length}/2000 characters
                </p>
              )}
            </div>
          </div>
        </div>
      </Card>

      {/* Vision Statement */}
      <Card padding="lg">
        <h3 className="text-lg font-semibold text-white mb-4">Vision Statement</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-white mb-2">Vision</label>
            <textarea
              value={formData.vision}
              onChange={(e) => handleInputChange('vision', e.target.value)}
              placeholder="Enter your business unit's vision statement..."
              rows={4}
              className={`w-full px-3 py-2 bg-zinc-800 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors resize-vertical ${
                errors.vision
                  ? 'border-red-500 focus:border-red-400'
                  : 'border-zinc-700 focus:border-amber-500'
              }`}
            />
            <div className="flex items-center justify-between mt-1">
              {errors.vision ? (
                <p className="text-sm text-red-400">{errors.vision}</p>
              ) : (
                <p className="text-sm text-zinc-500">{formData.vision.length}/1000 characters</p>
              )}
            </div>
          </div>
        </div>
      </Card>

      {/* Highlight Text Configuration */}
      <Card padding="lg">
        <h3 className="text-lg font-semibold text-white mb-4">Highlight Text</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-white mb-2">Highlight Text</label>
            <input
              type="text"
              value={formData.highlightText}
              onChange={(e) => handleInputChange('highlightText', e.target.value)}
              placeholder="Enter text to highlight (e.g., key phrases, company name)"
              className={`w-full px-3 py-2 bg-zinc-800 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors ${
                errors.highlightText
                  ? 'border-red-500 focus:border-red-400'
                  : 'border-zinc-700 focus:border-amber-500'
              }`}
            />
            {errors.highlightText && (
              <p className="mt-1 text-sm text-red-400">{errors.highlightText}</p>
            )}
            <p className="mt-1 text-xs text-zinc-500">
              This text will be highlighted when it appears in content
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-white mb-2">Highlight Style</label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {highlightClasses.map((highlightClass) => (
                <button
                  key={highlightClass.value}
                  type="button"
                  onClick={() => handleInputChange('highlightClass', highlightClass.value)}
                  className={`p-3 rounded-lg border-2 transition-colors text-left ${
                    formData.highlightClass === highlightClass.value
                      ? 'border-amber-500 bg-amber-500/10'
                      : 'border-zinc-700 hover:border-zinc-600'
                  }`}
                >
                  <div className="text-xs text-zinc-400 mb-1">{highlightClass.label}</div>
                  <div className={`text-sm ${highlightClass.preview}`}>Sample text</div>
                </button>
              ))}
            </div>
          </div>

          <HighlightPreview />
        </div>
      </Card>

      {/* Mission Statements */}
      <Card padding="lg">
        <h3 className="text-lg font-semibold text-white mb-4">Mission Statements</h3>
        <MissionManager missions={formData.mission} onChange={handleMissionChange} />
      </Card>

      {/* Badges Management */}
      <Card padding="lg">
        <h3 className="text-lg font-semibold text-white mb-4">Badges</h3>
        <BadgesManager badges={formData.badges} onChange={handleBadgesChange} />
      </Card>

      {/* Form Validation Summary */}
      {Object.keys(errors).length > 0 && (
        <Card padding="lg">
          <div className="flex items-start gap-3">
            <svg
              className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <div>
              <h4 className="text-sm font-medium text-red-400 mb-2">
                Please fix the following errors:
              </h4>
              <ul className="text-sm text-red-300 space-y-1">
                {Object.values(errors).map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          </div>
        </Card>
      )}
    </div>
  )
}
