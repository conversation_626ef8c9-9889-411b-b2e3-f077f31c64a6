/**
 * NewsService - Layanan manajemen berita sederhana
 * Menangani operasi CRUD artikel berita menggunakan localStorage
 */

import { v4 as uuidv4 } from 'uuid'

class NewsService {
  constructor() {
    this.storageKey = 'cigi_news_articles'
    this.categoriesKey = 'cigi_news_categories'
    this.init()
  }

  init() {
    // Inisialisasi dengan data contoh jika belum ada artikel
    if (!this.getArticles().length) {
      this.initializeSampleData()
    }
  }

  initializeSampleData() {
    const sampleCategories = [
      { id: 'teknologi', name: 'Teknologi', color: '#3B82F6' },
      { id: 'bisnis', name: '<PERSON><PERSON><PERSON>', color: '#10B981' },
      { id: 'inovasi', name: 'Inova<PERSON>', color: '#8B5CF6' },
      { id: 'perusahaan', name: '<PERSON><PERSON> Perusahaan', color: '#F59E0B' },
    ]

    const sampleArticles = [
      {
        id: uuidv4(),
        title: 'Cigi Global Meluncurkan Solusi Digital Baru',
        slug: 'cigi-global-meluncurkan-solusi-digital-baru',
        excerpt:
          'Kami dengan bangga mengumumkan peluncuran platform solusi digital baru yang dirancang untuk membantu bisnis bertransformasi.',
        content: `# Cigi Global Meluncurkan Solusi Digital Baru

Kami dengan bangga mengumumkan peluncuran platform solusi digital baru yang dirancang untuk membantu bisnis bertransformasi.

## Fitur Utama

- **Analitik Lanjutan**: Wawasan kinerja bisnis secara real-time
- **Integrasi Cloud**: Integrasi mulus dengan layanan cloud yang ada
- **Desain Mobile-First**: Dioptimalkan untuk perangkat mobile dan tablet
- **Keamanan**: Keamanan tingkat enterprise dan perlindungan data

## Cara Memulai

Tim kami siap membantu Anda untuk memulai dengan platform baru ini. Hubungi kami hari ini untuk menjadwalkan demo dan pelajari lebih lanjut bagaimana solusi kami dapat bermanfaat bagi bisnis Anda.`,
        categoryId: 'teknologi',
        tags: ['digital', 'platform', 'inovasi'],
        author: 'Tim Cigi Global',
        publishedAt: new Date('2024-01-15').toISOString(),
        createdAt: new Date('2024-01-15').toISOString(),
        updatedAt: new Date('2024-01-15').toISOString(),
        status: 'published',
        featured: true,
        imageUrl: null,
      },
      {
        id: uuidv4(),
        title: 'Ekspansi Divisi Teknologi Pertanian Kami',
        slug: 'ekspansi-divisi-teknologi-pertanian-kami',
        excerpt:
          'CigiFarm terus berkembang dengan kemitraan baru dan solusi inovatif untuk pertanian modern.',
        content: `# Ekspansi Divisi Teknologi Pertanian Kami

CigiFarm terus berkembang dengan kemitraan baru dan solusi inovatif untuk pertanian modern.

## Kemitraan Baru

Kami telah menjalin kemitraan strategis dengan perusahaan teknologi pertanian terkemuka untuk menghadirkan solusi mutakhir bagi para petani di seluruh dunia.

## Inovasi dalam Pertanian

Tim kami sedang mengembangkan teknologi revolusioner yang akan mengubah cara kita bertani dan memproduksi pangan.`,
        categoryId: 'bisnis',
        tags: ['pertanian', 'teknologi', 'kemitraan'],
        author: 'Tim CigiFarm',
        publishedAt: new Date('2024-01-10').toISOString(),
        createdAt: new Date('2024-01-10').toISOString(),
        updatedAt: new Date('2024-01-10').toISOString(),
        status: 'published',
        featured: false,
        imageUrl: null,
      },
    ]

    this.saveCategories(sampleCategories)
    this.saveArticles(sampleArticles)
  }

  // Article management
  getArticles() {
    try {
      const articles = localStorage.getItem(this.storageKey)
      return articles ? JSON.parse(articles) : []
    } catch (error) {
      console.error('Error loading articles:', error)
      return []
    }
  }

  getArticle(id) {
    const articles = this.getArticles()
    return articles.find((article) => article.id === id)
  }

  getArticleBySlug(slug) {
    const articles = this.getArticles()
    return articles.find((article) => article.slug === slug)
  }

  getPublishedArticles() {
    return this.getArticles().filter((article) => article.status === 'published')
  }

  getFeaturedArticles() {
    return this.getPublishedArticles().filter((article) => article.featured)
  }

  getArticlesByCategory(categoryId) {
    return this.getPublishedArticles().filter((article) => article.categoryId === categoryId)
  }

  createArticle(articleData) {
    const articles = this.getArticles()
    const newArticle = {
      id: uuidv4(),
      ...articleData,
      slug: this.generateSlug(articleData.title),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    articles.push(newArticle)
    this.saveArticles(articles)
    return newArticle
  }

  updateArticle(id, articleData) {
    const articles = this.getArticles()
    const index = articles.findIndex((article) => article.id === id)

    if (index === -1) {
      throw new Error('Article not found')
    }

    articles[index] = {
      ...articles[index],
      ...articleData,
      slug: articleData.title ? this.generateSlug(articleData.title) : articles[index].slug,
      updatedAt: new Date().toISOString(),
    }

    this.saveArticles(articles)
    return articles[index]
  }

  deleteArticle(id) {
    const articles = this.getArticles()
    const filteredArticles = articles.filter((article) => article.id !== id)
    this.saveArticles(filteredArticles)
    return true
  }

  // Category management
  getCategories() {
    try {
      const categories = localStorage.getItem(this.categoriesKey)
      return categories ? JSON.parse(categories) : []
    } catch (error) {
      console.error('Error loading categories:', error)
      return []
    }
  }

  getCategory(id) {
    const categories = this.getCategories()
    return categories.find((category) => category.id === id)
  }

  createCategory(categoryData) {
    const categories = this.getCategories()
    const newCategory = {
      id: categoryData.id || this.generateSlug(categoryData.name),
      ...categoryData,
    }

    categories.push(newCategory)
    this.saveCategories(categories)
    return newCategory
  }

  updateCategory(id, categoryData) {
    const categories = this.getCategories()
    const index = categories.findIndex((category) => category.id === id)

    if (index === -1) {
      throw new Error('Category not found')
    }

    categories[index] = { ...categories[index], ...categoryData }
    this.saveCategories(categories)
    return categories[index]
  }

  deleteCategory(id) {
    const categories = this.getCategories()
    const filteredCategories = categories.filter((category) => category.id !== id)
    this.saveCategories(filteredCategories)
    return true
  }

  // Utility methods
  generateSlug(title) {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim('-')
  }

  saveArticles(articles) {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(articles))
    } catch (error) {
      console.error('Error saving articles:', error)
    }
  }

  saveCategories(categories) {
    try {
      localStorage.setItem(this.categoriesKey, JSON.stringify(categories))
    } catch (error) {
      console.error('Error saving categories:', error)
    }
  }

  // Search functionality
  searchArticles(query) {
    const articles = this.getPublishedArticles()
    const lowercaseQuery = query.toLowerCase()

    return articles.filter(
      (article) =>
        article.title.toLowerCase().includes(lowercaseQuery) ||
        article.excerpt.toLowerCase().includes(lowercaseQuery) ||
        article.content.toLowerCase().includes(lowercaseQuery) ||
        article.tags.some((tag) => tag.toLowerCase().includes(lowercaseQuery))
    )
  }

  // Statistics
  getStats() {
    const articles = this.getArticles()
    const published = articles.filter((a) => a.status === 'published')
    const draft = articles.filter((a) => a.status === 'draft')
    const featured = articles.filter((a) => a.featured)

    return {
      total: articles.length,
      published: published.length,
      draft: draft.length,
      featured: featured.length,
    }
  }
}

// Create and export singleton instance
const newsService = new NewsService()
export default newsService
