import { Component } from 'react'
import Button from './ui/Button'
import Card from './ui/Card'
import Section from './ui/Section'

class ErrorBoundary extends Component {
  constructor(props) {
    super(props)
    this.state = { hasError: false, error: null, errorInfo: null }
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error }
  }

  componentDidCatch(error, errorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    this.setState({
      error: error || new Error('Unknown error occurred'),
      errorInfo: errorInfo || null,
    })
  }

  resetError = () => {
    this.setState({ hasError: false, error: null, errorInfo: null })
  }

  render() {
    if (this.state.hasError) {
      return (
        <Section padding="xl">
          <Card className="max-w-2xl mx-auto text-center" padding="xl">
            <div className="w-20 h-20 bg-red-600/20 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <svg
                className="w-10 h-10 text-red-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>

            <h1 className="text-2xl font-bold text-white mb-4">Oops! Terjadi Kesalahan</h1>

            <p className="text-zinc-400 mb-8">
              Maaf, terjadi kesalahan yang tidak terduga. Tim kami telah diberitahu dan sedang
              menangani masalah ini.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button onClick={this.resetError} size="lg">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
                Coba Lagi
              </Button>

              <Button variant="secondary" onClick={() => (window.location.href = '/')} size="lg">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                  />
                </svg>
                Kembali ke Beranda
              </Button>
            </div>

            {import.meta.env?.MODE === 'development' && (
              <details className="mt-8 text-left">
                <summary className="cursor-pointer text-zinc-500 hover:text-zinc-400">
                  Detail Error (Development)
                </summary>
                <pre className="mt-4 p-4 bg-zinc-900 rounded-lg text-xs text-red-400 overflow-auto">
                  {this.state.error && this.state.error.toString()}
                  <br />
                  {this.state.errorInfo?.componentStack || 'No component stack available'}
                </pre>
              </details>
            )}
          </Card>
        </Section>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary
