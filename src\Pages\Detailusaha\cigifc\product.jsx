const CigiFCProduct = () => {
  const programs = [
    {
      title: 'Pelatihan Rutin',
      description: 'Program pelatihan reguler untuk semua tingkat usia dan kemampuan',
      price: 'Rp 150.000/bulan',
      features: [
        '<PERSON><PERSON>wal latihan 3x seminggu',
        '<PERSON><PERSON><PERSON><PERSON> be<PERSON>',
        'Fasilitas lapangan standar',
      ],
    },
    {
      title: 'Kompetisi Internal',
      description: 'Turnamen internal bulanan untuk mengasah kemampuan',
      price: 'Gratis untuk anggota',
      features: ['Turnamen bulanan', 'Berbagai kategori usia', 'Hadiah menarik'],
    },
    {
      title: 'Pembinaan Atlet',
      description: 'Program khusus untuk pembinaan atlet berbakat',
      price: 'Beasiswa tersedia',
      features: ['Seleksi atlet berbakat', 'Pelatihan intensif', 'Persiapan kompetisi'],
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Program & Kegiatan Cigi FC</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Berbagai program pelatihan dan kegiatan untuk mengembangkan prestasi sepak bola
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {programs.map((program, index) => (
            <div key={index} className="bg-white rounded-lg shadow-lg overflow-hidden">
              <img
                src={`https://picsum.photos/400/30${index}`}
                alt={program.title}
                className="w-full h-48 object-cover"
              />
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">{program.title}</h3>
                <p className="text-gray-600 mb-4">{program.description}</p>
                <div className="text-2xl font-bold text-green-600 mb-4">{program.price}</div>
                <ul className="space-y-2">
                  {program.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-sm text-gray-700">
                      <svg
                        className="w-4 h-4 text-green-500 mr-2"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default CigiFCProduct
