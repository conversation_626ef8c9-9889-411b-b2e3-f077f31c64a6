/**
 * BasicInfoForm - Form component for editing basic business unit information
 */

import { useState, useEffect } from 'react'
import Card from '../../components/ui/Card.jsx'
import Button from '../../components/ui/Button.jsx'
import Badge from '../../components/ui/Badge.jsx'

export default function BasicInfoForm({ data, onFieldChange }) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    badgeText: '',
    badgeVariant: 'primary',
    badgeIcon: '',
    themeColor: {
      from: '#3b82f6',
      to: '#1d4ed8',
      primary: '#3b82f6',
    },
  })
  const [errors, setErrors] = useState({})
  const [showColorPicker, setShowColorPicker] = useState(null)

  // Initialize form data
  useEffect(() => {
    if (data) {
      setFormData({
        name: data.name || '',
        description: data.description || '',
        badgeText: data.badgeText || '',
        badgeVariant: data.badgeVariant || 'primary',
        badgeIcon: data.badgeIcon || '',
        themeColor: data.themeColor || {
          from: '#3b82f6',
          to: '#1d4ed8',
          primary: '#3b82f6',
        },
      })
    }
  }, [data])

  const badgeVariants = [
    {
      value: 'primary',
      label: 'Primary',
      color: 'bg-amber-600/20 text-amber-400 border-amber-600/30',
    },
    { value: 'secondary', label: 'Secondary', color: 'bg-zinc-700 text-zinc-200 border-zinc-600' },
    {
      value: 'success',
      label: 'Success',
      color: 'bg-green-600/20 text-green-400 border-green-600/30',
    },
    {
      value: 'warning',
      label: 'Warning',
      color: 'bg-yellow-600/20 text-yellow-400 border-yellow-600/30',
    },
    { value: 'info', label: 'Info', color: 'bg-blue-600/20 text-blue-400 border-blue-600/30' },
  ]

  const predefinedColors = [
    '#3b82f6',
    '#1d4ed8',
    '#2563eb',
    '#1e40af',
    '#1e3a8a',
    '#ef4444',
    '#dc2626',
    '#b91c1c',
    '#991b1b',
    '#7f1d1d',
    '#10b981',
    '#059669',
    '#047857',
    '#065f46',
    '#064e3b',
    '#f59e0b',
    '#d97706',
    '#b45309',
    '#92400e',
    '#78350f',
    '#8b5cf6',
    '#7c3aed',
    '#6d28d9',
    '#5b21b6',
    '#4c1d95',
    '#ec4899',
    '#db2777',
    '#be185d',
    '#9d174d',
    '#831843',
  ]

  const validateField = (name, value) => {
    const newErrors = { ...errors }

    switch (name) {
      case 'name':
        if (!value || value.trim().length < 3) {
          newErrors.name = 'Business unit name must be at least 3 characters long'
        } else if (value.length > 100) {
          newErrors.name = 'Business unit name must be 100 characters or less'
        } else {
          delete newErrors.name
        }
        break
      case 'description':
        if (!value || value.trim().length < 10) {
          newErrors.description = 'Description must be at least 10 characters long'
        } else if (value.length > 500) {
          newErrors.description = 'Description must be 500 characters or less'
        } else {
          delete newErrors.description
        }
        break
      case 'badgeText':
        if (value && value.length > 50) {
          newErrors.badgeText = 'Badge text must be 50 characters or less'
        } else {
          delete newErrors.badgeText
        }
        break
      default:
        break
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (name, value) => {
    const newFormData = { ...formData, [name]: value }
    setFormData(newFormData)

    // Real-time validation
    validateField(name, value)

    // Notify parent component
    if (onFieldChange) {
      onFieldChange(name, value)
    }
  }

  const handleThemeColorChange = (colorType, value) => {
    const newThemeColor = { ...formData.themeColor, [colorType]: value }
    const newFormData = { ...formData, themeColor: newThemeColor }
    setFormData(newFormData)

    if (onFieldChange) {
      onFieldChange('themeColor', newThemeColor)
    }
  }

  const isValidHexColor = (color) => {
    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color)
  }

  const ColorPicker = ({ colorType, value, onChange }) => (
    <div className="relative">
      <div className="flex items-center gap-2">
        <button
          type="button"
          onClick={() => setShowColorPicker(showColorPicker === colorType ? null : colorType)}
          className="w-10 h-10 rounded-lg border-2 border-zinc-600 hover:border-zinc-500 transition-colors"
          style={{ backgroundColor: value }}
        />
        <input
          type="text"
          value={value}
          onChange={(e) => {
            const newValue = e.target.value
            if (isValidHexColor(newValue) || newValue === '') {
              onChange(newValue)
            }
          }}
          placeholder="#3b82f6"
          className="flex-1 px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:border-amber-500 transition-colors font-mono text-sm"
        />
      </div>

      {showColorPicker === colorType && (
        <div className="absolute top-12 left-0 z-10 p-4 bg-zinc-800 border border-zinc-700 rounded-lg shadow-xl">
          <div className="grid grid-cols-5 gap-2 mb-3">
            {predefinedColors.map((color) => (
              <button
                key={color}
                type="button"
                onClick={() => {
                  onChange(color)
                  setShowColorPicker(null)
                }}
                className="w-8 h-8 rounded border-2 border-zinc-600 hover:border-zinc-400 transition-colors"
                style={{ backgroundColor: color }}
              />
            ))}
          </div>
          <div className="flex items-center gap-2">
            <input
              type="color"
              value={value}
              onChange={(e) => onChange(e.target.value)}
              className="w-8 h-8 rounded border border-zinc-600 bg-transparent cursor-pointer"
            />
            <span className="text-xs text-zinc-400">Custom color</span>
          </div>
        </div>
      )}
    </div>
  )

  const BadgePreview = () => {
    const variant = badgeVariants.find((v) => v.value === formData.badgeVariant)
    if (!formData.badgeText) return null

    return (
      <div className="flex items-center gap-2">
        <span className="text-sm text-zinc-400">Preview:</span>
        <span
          className={`inline-flex items-center px-2.5 py-1 text-sm font-medium border rounded-lg ${variant?.color || badgeVariants[0].color}`}
        >
          {formData.badgeIcon && <span className="mr-1">{formData.badgeIcon}</span>}
          {formData.badgeText}
        </span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <Card padding="lg">
        <h3 className="text-lg font-semibold text-white mb-4">Basic Information</h3>
        <div className="space-y-4">
          {/* Name Field */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Business Unit Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Enter business unit name"
              className={`w-full px-3 py-2 bg-zinc-800 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors ${
                errors.name
                  ? 'border-red-500 focus:border-red-400'
                  : 'border-zinc-700 focus:border-amber-500'
              }`}
            />
            {errors.name && <p className="mt-1 text-sm text-red-400">{errors.name}</p>}
          </div>

          {/* Description Field */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Description *</label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Enter business unit description"
              rows={3}
              className={`w-full px-3 py-2 bg-zinc-800 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors resize-vertical ${
                errors.description
                  ? 'border-red-500 focus:border-red-400'
                  : 'border-zinc-700 focus:border-amber-500'
              }`}
            />
            <div className="flex items-center justify-between mt-1">
              {errors.description ? (
                <p className="text-sm text-red-400">{errors.description}</p>
              ) : (
                <p className="text-sm text-zinc-500">
                  {formData.description.length}/500 characters
                </p>
              )}
            </div>
          </div>
        </div>
      </Card>

      {/* Badge Configuration */}
      <Card padding="lg">
        <h3 className="text-lg font-semibold text-white mb-4">Badge Configuration</h3>
        <div className="space-y-4">
          {/* Badge Text */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Badge Text</label>
            <input
              type="text"
              value={formData.badgeText}
              onChange={(e) => handleInputChange('badgeText', e.target.value)}
              placeholder="Enter badge text (optional)"
              className={`w-full px-3 py-2 bg-zinc-800 border rounded-lg text-white placeholder-zinc-400 focus:outline-none transition-colors ${
                errors.badgeText
                  ? 'border-red-500 focus:border-red-400'
                  : 'border-zinc-700 focus:border-amber-500'
              }`}
            />
            {errors.badgeText && <p className="mt-1 text-sm text-red-400">{errors.badgeText}</p>}
          </div>

          {/* Badge Icon */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Badge Icon (Emoji)</label>
            <input
              type="text"
              value={formData.badgeIcon}
              onChange={(e) => handleInputChange('badgeIcon', e.target.value)}
              placeholder="🏢 (optional emoji)"
              className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:border-amber-500 transition-colors"
            />
          </div>

          {/* Badge Variant */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Badge Variant</label>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
              {badgeVariants.map((variant) => (
                <button
                  key={variant.value}
                  type="button"
                  onClick={() => handleInputChange('badgeVariant', variant.value)}
                  className={`p-3 rounded-lg border-2 transition-colors ${
                    formData.badgeVariant === variant.value
                      ? 'border-amber-500 bg-amber-500/10'
                      : 'border-zinc-700 hover:border-zinc-600'
                  }`}
                >
                  <div
                    className={`inline-flex items-center px-2 py-1 text-xs font-medium border rounded ${variant.color}`}
                  >
                    {variant.label}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Badge Preview */}
          {formData.badgeText && (
            <div className="p-4 bg-zinc-900 rounded-lg">
              <BadgePreview />
            </div>
          )}
        </div>
      </Card>

      {/* Theme Colors */}
      <Card padding="lg">
        <h3 className="text-lg font-semibold text-white mb-4">Theme Colors</h3>
        <div className="space-y-4">
          {/* Gradient From Color */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Gradient From Color</label>
            <ColorPicker
              colorType="from"
              value={formData.themeColor.from}
              onChange={(value) => handleThemeColorChange('from', value)}
            />
          </div>

          {/* Gradient To Color */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Gradient To Color</label>
            <ColorPicker
              colorType="to"
              value={formData.themeColor.to}
              onChange={(value) => handleThemeColorChange('to', value)}
            />
          </div>

          {/* Primary Color */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Primary Color</label>
            <ColorPicker
              colorType="primary"
              value={formData.themeColor.primary}
              onChange={(value) => handleThemeColorChange('primary', value)}
            />
          </div>

          {/* Theme Preview */}
          <div className="p-4 bg-zinc-900 rounded-lg">
            <div className="flex items-center gap-2 mb-3">
              <span className="text-sm text-zinc-400">Theme Preview:</span>
            </div>
            <div className="space-y-3">
              {/* Gradient Preview */}
              <div
                className="h-16 rounded-lg"
                style={{
                  background: `linear-gradient(135deg, ${formData.themeColor.from}, ${formData.themeColor.to})`,
                }}
              />
              {/* Primary Color Preview */}
              <div className="flex items-center gap-2">
                <div
                  className="w-8 h-8 rounded"
                  style={{ backgroundColor: formData.themeColor.primary }}
                />
                <span className="text-sm text-zinc-400">Primary Color</span>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Form Validation Summary */}
      {Object.keys(errors).length > 0 && (
        <Card padding="lg">
          <div className="flex items-start gap-3">
            <svg
              className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <div>
              <h4 className="text-sm font-medium text-red-400 mb-2">
                Please fix the following errors:
              </h4>
              <ul className="text-sm text-red-300 space-y-1">
                {Object.values(errors).map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          </div>
        </Card>
      )}
    </div>
  )
}
