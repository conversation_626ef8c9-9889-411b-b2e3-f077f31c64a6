import { Link } from 'react-router-dom'
import Section from '../components/ui/Section'
import Card from '../components/ui/Card'
import Button from '../components/ui/Button'
import AnimatedElement from '../components/ui/AnimatedElement'

export default function NotFound() {
  return (
    <Section padding="xl">
      <AnimatedElement animation="fadeInUp">
        <Card className="max-w-2xl mx-auto text-center" padding="xl">
          <div className="w-32 h-32 bg-gradient-to-br from-amber-500 to-amber-600 rounded-3xl flex items-center justify-center mx-auto mb-8">
            <span className="text-6xl font-bold text-white">404</span>
          </div>

          <h1 className="text-3xl font-bold text-white mb-4">Halaman Tidak Ditemukan</h1>

          <p className="text-lg text-zinc-400 mb-8">
            Ma<PERSON>, halaman yang Anda cari tidak dapat ditemukan. Mungkin halaman telah dipindahkan
            atau URL yang dimasukkan salah.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button as={Link} to="/" size="lg">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                />
              </svg>
              Kembali ke Beranda
            </Button>

            <Button variant="secondary" as={Link} to="/contact" size="lg">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                />
              </svg>
              Hubungi Kami
            </Button>
          </div>
        </Card>
      </AnimatedElement>
    </Section>
  )
}
