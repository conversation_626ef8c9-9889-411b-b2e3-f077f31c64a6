/**
 * BusinessUnitService - Client-side service for business unit operations
 * Handles API calls to the business unit endpoints with cache invalidation
 */

// import businessUnitApiService from './BusinessUnitApiService'

class BusinessUnitService {
  constructor() {
    this.apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3002/api'
    this.baseUrl = `${this.apiBaseUrl}/business-units`
  }

  /**
 * Get all business units
 * @returns {Promise<Array>} List of business units
 */
  async getAllBusinessUnits() {
    try {
      console.log('🔄 Attempting to fetch business units from API:', this.baseUrl)
      const response = await fetch(this.baseUrl)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()

      // Check if the response has the expected structure
      if (result.success && Array.isArray(result.data)) {
        console.log('✅ Successfully fetched business units from API')
        // Mark data as coming from API (real data)
        return result.data.map(unit => ({ ...unit, _isRealData: true }))
      }

      // If response doesn't have expected structure, throw error to trigger fallback
      throw new Error('Invalid response format')
    } catch (error) {
      console.warn('⚠️ API not available, using fallback data:', error.message)
      console.log('💡 To use dynamic data, start the backend server with: npm run dev:server')

      // Return fallback data for development
      return this._getFallbackBusinessUnits().map(unit => ({ ...unit, _isFallbackData: true }))
    }
  }

  /**
 * Get business unit by slug
 * @param {string} slug - Business unit slug
 * @returns {Promise<Object>} Business unit data
 */
  async getBusinessUnit(slug) {
    try {
      const response = await fetch(`${this.baseUrl}/${slug}`)
      if (!response.ok) {
        throw new Error('Failed to fetch business unit')
      }
      const result = await response.json()

      // Check if the response has the expected structure
      if (result.success && result.data) {
        return { ...result.data, _isRealData: true }
      }

      throw new Error('Invalid response format')
    } catch (error) {
      console.error('Error fetching business unit:', error)
      throw error
    }
  }

  /**
   * Create a new business unit
   * @param {Object} data - Business unit data
   * @returns {Promise<Object>} Created business unit
   */
  async createBusinessUnit(data) {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this._getSessionToken()}`,
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || 'Failed to create business unit')
      }

      const result = await response.json()

      // Business unit created successfully
      if (result.success) {
        console.log('[BusinessUnitService] Business unit created successfully')
        return { ...result.data, _isRealData: true }
      }

      throw new Error('Invalid response format')
    } catch (error) {
      console.error('Error creating business unit:', error)
      throw error
    }
  }

  /**
   * Update a business unit
   * @param {string} id - Business unit ID
   * @param {Object} data - Updated business unit data
   * @returns {Promise<Object>} Updated business unit
   */
  async updateBusinessUnit(id, data) {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this._getSessionToken()}`,
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || 'Failed to update business unit')
      }

      const result = await response.json()

      // Business unit updated successfully
      if (result.success && result.data) {
        console.log(`[BusinessUnitService] Business unit updated: ${result.data.slug}`)
        return { ...result.data, _isRealData: true }
      }

      throw new Error('Invalid response format')
    } catch (error) {
      console.error('Error updating business unit:', error)
      throw error
    }
  }

  /**
   * Delete a business unit
   * @param {string} id - Business unit ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteBusinessUnit(id) {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${this._getSessionToken()}`,
        },
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || 'Failed to delete business unit')
      }

      const result = await response.json()

      // Business unit deleted successfully
      if (result.success) {
        console.log('[BusinessUnitService] Business unit deleted successfully')
        return true
      }

      throw new Error('Invalid response format')
    } catch (error) {
      console.error('Error deleting business unit:', error)
      throw error
    }
  }

  /**
   * Get statistics for dashboard
   * @returns {Object} Statistics data
   */
  getStats() {
    // This would normally make an API call, but for now return static data
    return {
      total: 7,
      active: 7,
      recent: 3,
    }
  }

  /**
   * Get fallback business units data for development
   * @returns {Array} Fallback business units
   */
  _getFallbackBusinessUnits() {
    const now = new Date().toISOString()

    return [
      {
        id: 'pbcigi',
        name: 'PB Cigi',
        slug: 'pbcigi',
        badgeText: 'Pusat Bisnis',
        badgeVariant: 'primary',
        badgeIcon: '🏢',
        description: 'Pusat Bisnis dan Komersialisasi Institut Global Indonesia',
        lastModified: now,
      },
      {
        id: 'ciginet',
        name: 'Cigi Net',
        slug: 'ciginet',
        badgeText: 'Internet Provider',
        badgeVariant: 'info',
        badgeIcon: '🌐',
        description: 'Penyedia layanan internet dan teknologi informasi',
        lastModified: now,
      },
      {
        id: 'cigimart',
        name: 'Cigi Mart',
        slug: 'cigimart',
        badgeText: 'Retail',
        badgeVariant: 'success',
        badgeIcon: '🛒',
        description: 'Toko retail dan marketplace online',
        lastModified: now,
      },
      {
        id: 'cigifood',
        name: 'Cigi Food',
        slug: 'cigifood',
        badgeText: 'F&B',
        badgeVariant: 'warning',
        badgeIcon: '🍽️',
        description: 'Layanan makanan dan minuman',
        lastModified: now,
      },
      {
        id: 'cigifc',
        name: 'Cigi FC',
        slug: 'cigifc',
        badgeText: 'Sports',
        badgeVariant: 'primary',
        badgeIcon: '⚽',
        description: 'Klub sepak bola dan olahraga',
        lastModified: now,
      },
      {
        id: 'cigifarm',
        name: 'Cigi Farm',
        slug: 'cigifarm',
        badgeText: 'Agriculture',
        badgeVariant: 'success',
        badgeIcon: '🌱',
        description: 'Pertanian dan agribisnis modern',
        lastModified: now,
      },
      {
        id: 'cigiarchery',
        name: 'Cigi Archery',
        slug: 'cigiarchery',
        badgeText: 'Tim Panahan Desa',
        badgeVariant: 'warning',
        badgeIcon: '🏹',
        description: 'Tim panahan masyarakat Desa Cimande Girang yang mengembangkan bakat olahraga',
        lastModified: now,
      },
    ]
  }

  /**
   * Get session token for authenticated requests
   * @returns {string} Session token
   */
  _getSessionToken() {
    // Try to get session token from various sources
    return localStorage.getItem('sessionId') ||
      sessionStorage.getItem('sessionId') ||
      ''
  }
}

// Create and export a singleton instance
const businessUnitService = new BusinessUnitService()
export default businessUnitService
