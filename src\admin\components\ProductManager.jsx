/**
 * ProductManager - Component for managing products with add/edit/remove functionality
 */

import { useState, useEffect } from 'react'
import Button from '../../components/ui/Button.jsx'
import Card from '../../components/ui/Card.jsx'
import ProductForm from './ProductForm.jsx'
import ProductFeaturesList from './ProductFeaturesList.jsx'
import { ProductManagerSkeleton } from '../../components/ui/AdminSkeletons.jsx'
import { InlineLoader } from '../../components/ui/LoadingIndicator.jsx'

export default function ProductManager({ products = {}, onChange }) {
  const [productData, setProductData] = useState({
    title: 'Produk Unggulan',
    description: 'Produk berkualitas tinggi dari unit usaha kami',
    items: [],
    cta: { text: 'Lihat Semua Produk' },
  })
  const [editingIndex, setEditingIndex] = useState(null)
  const [draggedIndex, setDraggedIndex] = useState(null)
  const [errors, setErrors] = useState({})


  // Initialize product data
  useEffect(() => {
    if (products && typeof products === 'object') {
      setProductData({
        title: products.title || 'Produk Unggulan',
        description: products.description || 'Produk berkualitas tinggi dari unit usaha kami',
        items: products.items || [],
        cta: products.cta || { text: 'Lihat Semua Produk' },
      })
    }
  }, [products])

  const validateProduct = (product, index) => {
    const newErrors = { ...errors }
    const errorKey = `product_${index}`

    const productErrors = {}

    if (!product.title || product.title.trim().length < 3) {
      productErrors.title = 'Product title must be at least 3 characters long'
    } else if (product.title.length > 100) {
      productErrors.title = 'Product title must be 100 characters or less'
    }

    if (product.description && product.description.length > 500) {
      productErrors.description = 'Product description must be 500 characters or less'
    }

    if (product.price && product.price.length > 50) {
      productErrors.price = 'Product price must be 50 characters or less'
    }

    if (product.image && product.image.length > 500) {
      productErrors.image = 'Image URL must be 500 characters or less'
    }

    if (Object.keys(productErrors).length > 0) {
      newErrors[errorKey] = productErrors
    } else {
      delete newErrors[errorKey]
    }

    setErrors(newErrors)
    return Object.keys(productErrors).length === 0
  }

  const handleProductChange = (index, field, value) => {
    const newItems = [...productData.items]
    newItems[index] = { ...newItems[index], [field]: value }

    const newProductData = { ...productData, items: newItems }
    setProductData(newProductData)

    // Validate the product
    validateProduct(newItems[index], index)

    if (onChange) {
      onChange(newProductData)
    }
  }

  const handleSectionChange = (field, value) => {
    const newProductData = { ...productData, [field]: value }
    setProductData(newProductData)

    if (onChange) {
      onChange(newProductData)
    }
  }

  const addProduct = () => {
    const newProduct = {
      title: '',
      description: '',
      image: '',
      price: '',
      features: [],
    }

    const newItems = [...productData.items, newProduct]
    const newProductData = { ...productData, items: newItems }
    setProductData(newProductData)

    // Start editing the new product
    setEditingIndex(newItems.length - 1)

    if (onChange) {
      onChange(newProductData)
    }
  }

  const removeProduct = (index) => {
    if (window.confirm('Are you sure you want to remove this product?')) {
      const newItems = productData.items.filter((_, i) => i !== index)
      const newProductData = { ...productData, items: newItems }
      setProductData(newProductData)

      // Clear errors for this product
      const newErrors = { ...errors }
      delete newErrors[`product_${index}`]

      // Reindex remaining errors
      const reindexedErrors = {}
      Object.keys(newErrors).forEach((key) => {
        if (key.startsWith('product_')) {
          const oldIndex = parseInt(key.split('_')[1])
          if (oldIndex > index) {
            reindexedErrors[`product_${oldIndex - 1}`] = newErrors[key]
          } else if (oldIndex < index) {
            reindexedErrors[key] = newErrors[key]
          }
        } else {
          reindexedErrors[key] = newErrors[key]
        }
      })
      setErrors(reindexedErrors)

      // Close editing if we were editing this product
      if (editingIndex === index) {
        setEditingIndex(null)
      } else if (editingIndex > index) {
        setEditingIndex(editingIndex - 1)
      }

      if (onChange) {
        onChange(newProductData)
      }
    }
  }

  const duplicateProduct = (index) => {
    const productToDuplicate = { ...productData.items[index] }
    productToDuplicate.title = `${productToDuplicate.title} (Copy)`

    const newItems = [...productData.items]
    newItems.splice(index + 1, 0, productToDuplicate)

    const newProductData = { ...productData, items: newItems }
    setProductData(newProductData)

    if (onChange) {
      onChange(newProductData)
    }
  }

  const moveProduct = (fromIndex, toIndex) => {
    if (fromIndex === toIndex) return

    const newItems = [...productData.items]
    const [movedItem] = newItems.splice(fromIndex, 1)
    newItems.splice(toIndex, 0, movedItem)

    const newProductData = { ...productData, items: newItems }
    setProductData(newProductData)

    // Update editing index if needed
    if (editingIndex === fromIndex) {
      setEditingIndex(toIndex)
    } else if (editingIndex > fromIndex && editingIndex <= toIndex) {
      setEditingIndex(editingIndex - 1)
    } else if (editingIndex < fromIndex && editingIndex >= toIndex) {
      setEditingIndex(editingIndex + 1)
    }

    if (onChange) {
      onChange(newProductData)
    }
  }

  const moveProductToTop = (index) => {
    if (index === 0) return
    moveProduct(index, 0)
    saveProductOrder(index, 0)
  }

  const moveProductToBottom = (index) => {
    if (index === productData.items.length - 1) return
    moveProduct(index, productData.items.length - 1)
    saveProductOrder(index, productData.items.length - 1)
  }

  const reverseProductOrder = () => {
    if (window.confirm('Are you sure you want to reverse the order of all products?')) {
      const newItems = [...productData.items].reverse()
      const newProductData = { ...productData, items: newItems }
      setProductData(newProductData)

      if (onChange) {
        onChange(newProductData)
      }

      // Save the new order
      saveProductOrder(0, productData.items.length - 1)
    }
  }

  const handleDragStart = (e, index) => {
    setDraggedIndex(index)
    e.dataTransfer.effectAllowed = 'move'
    e.dataTransfer.setData('text/plain', index.toString())

    // Add visual feedback
    e.target.style.opacity = '0.5'
  }

  const handleDragEnd = (e) => {
    e.target.style.opacity = '1'
    setDraggedIndex(null)
  }

  const handleDragOver = (e) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  const handleDragEnter = (e) => {
    e.preventDefault()
    e.target.closest('.product-item')?.classList.add('drag-over')
  }

  const handleDragLeave = (e) => {
    e.preventDefault()
    e.target.closest('.product-item')?.classList.remove('drag-over')
  }

  const handleDrop = (e, dropIndex) => {
    e.preventDefault()
    e.target.closest('.product-item')?.classList.remove('drag-over')

    const draggedIdx = parseInt(e.dataTransfer.getData('text/plain'))
    if (draggedIdx !== dropIndex && !isNaN(draggedIdx)) {
      moveProduct(draggedIdx, dropIndex)
      saveProductOrder(draggedIdx, dropIndex)
    }
    setDraggedIndex(null)
  }

  const saveProductOrder = async (fromIndex, toIndex) => {
    try {
      // This would be the API call to save the new product order
      // For now, we'll just log it
      console.log('Saving product order change:', {
        fromIndex,
        toIndex,
        newOrder: productData.items.map((item, index) => ({
          id: item.id || index,
          sortOrder: index,
        })),
      })

      // In a real implementation, this would be:
      // await api.updateProductOrder(businessUnitId, newOrder)
    } catch (error) {
      console.error('Failed to save product order:', error)
      // Optionally revert the change or show an error message
    }
  }

  return (
    <div className="space-y-6">
      <style jsx>{`
        .product-item.drag-over {
          border-color: #f59e0b !important;
          background-color: rgba(245, 158, 11, 0.1) !important;
        }
        .product-item:hover {
          transform: translateY(-1px);
        }
        .product-item.dragging {
          transform: rotate(2deg) scale(0.95);
          opacity: 0.8;
        }
      `}</style>
      {/* Products Section Configuration */}
      <Card padding="lg">
        <h3 className="text-lg font-semibold text-white mb-4">Products Section</h3>
        <div className="space-y-4">
          {/* Section Title */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Section Title</label>
            <input
              type="text"
              value={productData.title}
              onChange={(e) => handleSectionChange('title', e.target.value)}
              placeholder="Enter section title"
              className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:border-amber-500 transition-colors"
            />
          </div>

          {/* Section Description */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Section Description</label>
            <textarea
              value={productData.description}
              onChange={(e) => handleSectionChange('description', e.target.value)}
              placeholder="Enter section description"
              rows={2}
              className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:border-amber-500 transition-colors resize-vertical"
            />
          </div>

          {/* CTA Text */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Call-to-Action Text</label>
            <input
              type="text"
              value={productData.cta.text}
              onChange={(e) => handleSectionChange('cta', { text: e.target.value })}
              placeholder="Enter CTA text"
              className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:border-amber-500 transition-colors"
            />
          </div>
        </div>
      </Card>

      {/* Products List */}
      <Card padding="lg">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">
            Products ({productData.items.length})
          </h3>
          <div className="flex items-center gap-2">
            {productData.items.length > 1 && (
              <Button
                variant="secondary"
                size="sm"
                onClick={reverseProductOrder}
                title="Reverse product order"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"
                  />
                </svg>
                Reverse Order
              </Button>
            )}
            <Button onClick={addProduct}>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4v16m8-8H4"
                />
              </svg>
              Add Product
            </Button>
          </div>
        </div>

        {productData.items.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-zinc-700 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg
                className="w-8 h-8 text-zinc-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
                />
              </svg>
            </div>
            <h4 className="text-lg font-semibold text-white mb-2">No Products Yet</h4>
            <p className="text-zinc-400 mb-4">
              Start by adding your first product to showcase your offerings.
            </p>
            <Button onClick={addProduct}>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4v16m8-8H4"
                />
              </svg>
              Add First Product
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {productData.items.map((product, index) => (
              <div
                key={index}
                draggable
                onDragStart={(e) => handleDragStart(e, index)}
                onDragEnd={handleDragEnd}
                onDragOver={handleDragOver}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDrop={(e) => handleDrop(e, index)}
                className={`product-item p-4 bg-zinc-900 border rounded-lg transition-all cursor-move ${
                  draggedIndex === index
                    ? 'opacity-50 scale-95'
                    : 'border-zinc-700 hover:border-zinc-600'
                } ${editingIndex === index ? 'border-amber-500' : ''}`}
                style={{
                  transform: draggedIndex === index ? 'rotate(2deg)' : 'none',
                }}
              >
                <div className="flex items-start gap-4">
                  {/* Drag Handle */}
                  <div className="flex-shrink-0 cursor-move text-zinc-400 hover:text-white transition-colors mt-1">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 8h16M4 16h16"
                      />
                    </svg>
                  </div>

                  {/* Product Number */}
                  <div className="flex-shrink-0 w-8 h-8 bg-amber-600/20 text-amber-400 rounded-full flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </div>

                  {/* Product Content */}
                  <div className="flex-1">
                    {editingIndex === index ? (
                      <ProductForm
                        product={product}
                        index={index}
                        onChange={handleProductChange}
                        errors={errors[`product_${index}`] || {}}
                      />
                    ) : (
                      <div className="space-y-2">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="text-white font-medium">
                              {product.title || 'Untitled Product'}
                            </h4>
                            {product.description && (
                              <p className="text-zinc-400 text-sm mt-1 line-clamp-2">
                                {product.description}
                              </p>
                            )}
                            {product.price && (
                              <p className="text-amber-400 font-medium text-sm mt-1">
                                {product.price}
                              </p>
                            )}
                          </div>
                          {product.image && (
                            <img
                              src={product.image}
                              alt={product.title}
                              className="w-16 h-16 object-cover rounded-lg"
                              onError={(e) => {
                                e.target.style.display = 'none'
                              }}
                            />
                          )}
                        </div>
                        {product.features && product.features.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {product.features.slice(0, 3).map((feature, featureIndex) => (
                              <span
                                key={featureIndex}
                                className="inline-flex items-center px-2 py-1 text-xs bg-zinc-700 text-zinc-300 rounded"
                              >
                                {feature}
                              </span>
                            ))}
                            {product.features.length > 3 && (
                              <span className="inline-flex items-center px-2 py-1 text-xs bg-zinc-700 text-zinc-300 rounded">
                                +{product.features.length - 3} more
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Controls */}
                  <div className="flex-shrink-0 flex flex-col gap-1">
                    {/* Move to Top */}
                    <button
                      type="button"
                      onClick={() => moveProductToTop(index)}
                      disabled={index === 0}
                      className="p-1 text-zinc-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      title="Move to top"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 10l7-7m0 0l7 7m-7-7v18"
                        />
                      </svg>
                    </button>

                    {/* Move Up */}
                    <button
                      type="button"
                      onClick={() => {
                        moveProduct(index, Math.max(0, index - 1))
                        saveProductOrder(index, Math.max(0, index - 1))
                      }}
                      disabled={index === 0}
                      className="p-1 text-zinc-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      title="Move up"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 15l7-7 7 7"
                        />
                      </svg>
                    </button>

                    {/* Move Down */}
                    <button
                      type="button"
                      onClick={() => {
                        moveProduct(index, Math.min(productData.items.length - 1, index + 1))
                        saveProductOrder(index, Math.min(productData.items.length - 1, index + 1))
                      }}
                      disabled={index === productData.items.length - 1}
                      className="p-1 text-zinc-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      title="Move down"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </button>

                    {/* Move to Bottom */}
                    <button
                      type="button"
                      onClick={() => moveProductToBottom(index)}
                      disabled={index === productData.items.length - 1}
                      className="p-1 text-zinc-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      title="Move to bottom"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 14l-7 7m0 0l-7-7m7 7V3"
                        />
                      </svg>
                    </button>

                    {/* Edit */}
                    <button
                      type="button"
                      onClick={() => setEditingIndex(editingIndex === index ? null : index)}
                      className={`p-1 transition-colors ${
                        editingIndex === index ? 'text-amber-400' : 'text-zinc-400 hover:text-white'
                      }`}
                      title={editingIndex === index ? 'Close editor' : 'Edit product'}
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                        />
                      </svg>
                    </button>

                    {/* Duplicate */}
                    <button
                      type="button"
                      onClick={() => duplicateProduct(index)}
                      className="p-1 text-zinc-400 hover:text-white transition-colors"
                      title="Duplicate product"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                        />
                      </svg>
                    </button>

                    {/* Remove */}
                    <button
                      type="button"
                      onClick={() => removeProduct(index)}
                      className="p-1 text-red-400 hover:text-red-300 transition-colors"
                      title="Remove product"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>

      {/* Help Text */}
      <Card padding="lg">
        <div className="text-sm text-zinc-500 space-y-2">
          <p>
            <strong>Tips for managing products:</strong>
          </p>
          <ul className="list-disc list-inside space-y-1 ml-2">
            <li>
              <strong>Reordering:</strong> Drag products to reorder them, or use the arrow buttons
              for precise positioning
            </li>
            <li>
              <strong>Quick moves:</strong> Use the double-arrow buttons to move products to top or
              bottom instantly
            </li>
            <li>
              <strong>Bulk operations:</strong> Use "Reverse Order" to quickly flip the entire
              product sequence
            </li>
            <li>
              <strong>Editing:</strong> Click the edit button to modify product details and features
            </li>
            <li>
              <strong>Duplication:</strong> Use duplicate to quickly create similar products with
              the same structure
            </li>
            <li>
              <strong>Features:</strong> Add compelling features to highlight product benefits and
              unique selling points
            </li>
            <li>
              <strong>Images:</strong> Include high-quality images for better visual appeal and
              customer engagement
            </li>
            <li>
              <strong>Auto-save:</strong> Product order changes are automatically saved when you
              reorder items
            </li>
          </ul>
        </div>
      </Card>

      {/* Validation Summary */}
      {Object.keys(errors).length > 0 && (
        <Card padding="lg">
          <div className="flex items-start gap-3">
            <svg
              className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <div>
              <h4 className="text-sm font-medium text-red-400 mb-2">
                Please fix the following errors:
              </h4>
              <div className="space-y-2">
                {Object.entries(errors).map(([key, productErrors]) => {
                  const productIndex = parseInt(key.split('_')[1]) + 1
                  return (
                    <div key={key}>
                      <p className="text-sm font-medium text-red-300">Product {productIndex}:</p>
                      <ul className="text-sm text-red-300 ml-4 space-y-1">
                        {Object.values(productErrors).map((error, index) => (
                          <li key={index}>• {error}</li>
                        ))}
                      </ul>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        </Card>
      )}
    </div>
  )
}
