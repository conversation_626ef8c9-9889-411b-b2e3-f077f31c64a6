import Section from '../components/ui/Section'
import Card from '../components/ui/Card'
import Badge from '../components/ui/Badge'
import Button from '../components/ui/Button'
import AnimatedElement from '../components/ui/AnimatedElement'
import DataImage from '../data'

export default function About() {
  return (
    <>
      {/* Hero Section */}
      <Section padding="xl">
        <div className="text-center max-w-4xl mx-auto mb-16">
          <AnimatedElement animation="fadeInUp">
            <Badge variant="primary" size="lg" className="mb-6">
              Tentang Kami
            </Badge>
            <h1 className="text-responsive-xl mb-6">
              Tentang <span className="gradient-text">Cigi Global</span>
            </h1>
            <p className="text-xl text-zinc-300 leading-relaxed">
              Induk berbagai unit usaha berbasis komunitas untuk kemandirian ekonomi desa
            </p>
          </AnimatedElement>
        </div>

        {/* G<PERSON>bar dan Profil */}
        <div className="grid md:grid-cols-2 gap-12 items-center mb-20">
          <AnimatedElement animation="slideInRight" delay={300} className="relative">
            <div className="relative">
              <div className="absolute -inset-4 bg-gradient-to-r from-amber-600 to-amber-400 rounded-2xl blur-2xl opacity-20 animate-pulse"></div>
              <img
                src={DataImage.CigiGlobal}
                alt="Cigi Global Office"
                className="relative w-full max-w-md md:max-w-lg mx-auto rounded-2xl shadow-2xl"
                loading="lazy"
              />
            </div>
          </AnimatedElement>

          <AnimatedElement animation="slideInRight" delay={300}>
            <Card padding="lg">
              <h2 className="text-2xl font-bold text-white mb-6">Profil Singkat</h2>
              <p className="text-lg text-zinc-300 leading-relaxed mb-6">
                <span className="text-amber-400 font-semibold">Cigi Global</span> adalah induk dari
                berbagai unit usaha yang berdiri dari, oleh, dan untuk masyarakat Desa Cimande
                Girang. Tujuan utama kami adalah memberdayakan potensi desa melalui kegiatan usaha
                yang berkelanjutan dan berdampak langsung bagi masyarakat.
              </p>
              <div className="flex flex-wrap gap-3">
                <Badge variant="success">Berkelanjutan</Badge>
                <Badge variant="primary">Berbasis Komunitas</Badge>
                <Badge variant="info">Pemberdayaan Desa</Badge>
              </div>
            </Card>
          </AnimatedElement>
        </div>
      </Section>

      {/* Visi & Misi */}
      <Section padding="xl" background="dark">
        <div className="grid md:grid-cols-2 gap-12">
          <AnimatedElement animation="fadeInUp">
            <Card padding="lg" className="h-full">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center">
                  <svg
                    className="w-6 h-6 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                    />
                  </svg>
                </div>
                <h2 className="text-2xl font-bold text-white">Visi</h2>
              </div>
              <p className="text-lg text-zinc-300 italic leading-relaxed">
                "Menjadi penggerak kemandirian ekonomi desa berbasis komunitas dan teknologi yang
                berkelanjutan untuk kesejahteraan masyarakat."
              </p>
            </Card>
          </AnimatedElement>

          <AnimatedElement animation="fadeInUp" delay={200}>
            <Card padding="lg" className="h-full">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center">
                  <svg
                    className="w-6 h-6 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"
                    />
                  </svg>
                </div>
                <h2 className="text-2xl font-bold text-white">Misi</h2>
              </div>
              <ul className="space-y-4 text-zinc-300">
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-amber-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Mengembangkan potensi lokal melalui unit usaha produktif</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-amber-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Meningkatkan kesejahteraan masyarakat lewat kolaborasi</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-amber-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Menumbuhkan literasi digital di desa</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-amber-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span>Menyediakan produk dan layanan yang berkualitas</span>
                </li>
              </ul>
            </Card>
          </AnimatedElement>
        </div>
      </Section>

      {/* Unit Usaha */}
      <Section padding="xl">
        <div className="text-center mb-12">
          <AnimatedElement animation="fadeInUp">
            <h2 className="text-responsive-lg mb-4">Unit Usaha Kami</h2>
            <p className="text-lg text-zinc-400 max-w-3xl mx-auto">
              Berbagai unit usaha yang kami kelola untuk pemberdayaan masyarakat desa
            </p>
          </AnimatedElement>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[
            {
              name: 'Cigi Farm',
              desc: 'Peternakan ayam petelur, ikan lele & nila',
              icon: '🌱',
              color: 'from-green-500 to-green-600',
            },
            {
              name: 'Cigi Net',
              desc: 'Layanan internet desa',
              icon: '🌐',
              color: 'from-blue-500 to-blue-600',
            },
            {
              name: 'Cigi Mart',
              desc: 'Supermarket & sembako',
              icon: '🏪',
              color: 'from-purple-500 to-purple-600',
            },
            {
              name: 'Cigi Food',
              desc: 'Produk makanan & minuman',
              icon: '🍽️',
              color: 'from-orange-500 to-orange-600',
            },
            {
              name: 'Cigi FC',
              desc: 'Klub sepak bola desa',
              icon: '⚽',
              color: 'from-red-500 to-red-600',
            },
            {
              name: 'Cigi Panahan',
              desc: 'Kegiatan panahan',
              icon: '🏹',
              color: 'from-yellow-500 to-yellow-600',
            },
            {
              name: 'Cigi Bulu Tangkis',
              desc: 'Klub badminton',
              icon: '🏸',
              color: 'from-pink-500 to-pink-600',
            },
            {
              name: 'Cigi Media',
              desc: 'Dokumentasi dan promosi desa',
              icon: '📸',
              color: 'from-indigo-500 to-indigo-600',
            },
          ].map((unit, index) => (
            <AnimatedElement key={index} animation="fadeInUp" delay={index * 100}>
              <Card className="text-center group" padding="lg">
                <div
                  className={`w-16 h-16 bg-gradient-to-br ${unit.color} rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}
                >
                  <span className="text-2xl">{unit.icon}</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-2 group-hover:text-amber-400 transition-colors">
                  {unit.name}
                </h3>
                <p className="text-sm text-zinc-400 group-hover:text-zinc-300 transition-colors">
                  {unit.desc}
                </p>
              </Card>
            </AnimatedElement>
          ))}
        </div>
      </Section>

      {/* Nilai-Nilai */}
      <Section padding="xl" background="gradient">
        <div className="text-center mb-12">
          <AnimatedElement animation="fadeInUp">
            <h2 className="text-responsive-lg mb-4">Nilai-Nilai Kami</h2>
            <p className="text-lg text-zinc-400 max-w-3xl mx-auto">
              Prinsip-prinsip yang menjadi fondasi dalam setiap kegiatan usaha kami
            </p>
          </AnimatedElement>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {[
            { name: 'Gotong Royong', icon: '🤝', desc: 'Bekerja sama untuk kemajuan bersama' },
            {
              name: 'Inovasi Lokal',
              icon: '💡',
              desc: 'Mengembangkan solusi kreatif berbasis lokal',
            },
            { name: 'Kejujuran', icon: '✨', desc: 'Transparansi dalam setiap kegiatan' },
            { name: 'Pemberdayaan', icon: '🚀', desc: 'Memberdayakan potensi masyarakat desa' },
          ].map((value, index) => (
            <AnimatedElement key={index} animation="fadeInUp" delay={index * 150}>
              <Card className="text-center group" padding="lg">
                <div className="w-16 h-16 bg-gradient-to-br from-amber-500 to-amber-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-2xl">{value.icon}</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-3 group-hover:text-amber-400 transition-colors">
                  {value.name}
                </h3>
                <p className="text-zinc-400 group-hover:text-zinc-300 transition-colors">
                  {value.desc}
                </p>
              </Card>
            </AnimatedElement>
          ))}
        </div>

        {/* CTA */}
        <AnimatedElement animation="fadeInUp" delay={600}>
          <div className="text-center">
            <Card className="max-w-2xl mx-auto" padding="xl">
              <h3 className="text-2xl font-bold text-white mb-4">
                Ingin Bergabung atau Mendukung Kami?
              </h3>
              <p className="text-zinc-400 mb-8">
                Mari bersama-sama membangun kemandirian ekonomi desa yang berkelanjutan
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                    />
                  </svg>
                  Hubungi Kami
                </Button>
                <Button variant="secondary" size="lg">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  Pelajari Lebih Lanjut
                </Button>
              </div>
            </Card>
          </div>
        </AnimatedElement>
      </Section>
    </>
  )
}
