/**
 * useBusinessUnit - React hook for fetching business unit data
 * Provides loading states, error handling, caching, and performance optimizations
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import businessUnitService from '../services/BusinessUnitService'

export const useBusinessUnit = (slug, fallbackData = null, options = {}) => {
  const { enablePreloading = true, backgroundRefresh = true } = options

  const [data, setData] = useState(fallbackData)
  const [loading, setLoading] = useState(!fallbackData)
  const [error, setError] = useState(null)
  const [isStale, setIsStale] = useState(false)

  const abortControllerRef = useRef(null)
  const backgroundRefreshTimeoutRef = useRef(null)

  // Cleanup function
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
      if (backgroundRefreshTimeoutRef.current) {
        clearTimeout(backgroundRefreshTimeoutRef.current)
      }
    }
  }, [])

  const fetchBusinessUnit = useCallback(
    async (forceRefresh = false, isBackground = false) => {
      if (!slug) return

      // Abort previous request if still pending
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
      abortControllerRef.current = new AbortController()

      try {
        if (!isBackground) {
          setLoading(true)
          setError(null)
        }

        const businessUnitData = await businessUnitService.getBusinessUnit(slug)

        // Check if request was aborted
        if (abortControllerRef.current?.signal.aborted) {
          return
        }

        setData(businessUnitData)
        setIsStale(false)

        // Schedule background refresh if enabled (reduced frequency)
        if (backgroundRefresh && !isBackground) {
          backgroundRefreshTimeoutRef.current = setTimeout(() => {
            fetchBusinessUnit(false, true)
          }, 300000) // Refresh every 5 minutes in background
        }
      } catch (err) {
        // Check if request was aborted
        if (abortControllerRef.current?.signal.aborted) {
          return
        }

        console.error('Failed to fetch business unit:', err)

        if (!isBackground) {
          setError(err.message || 'Failed to load business unit data')

          // If we have fallback data and API fails, use fallback
          setData((currentData) => {
            if (fallbackData && !currentData) {
              return fallbackData
            }
            // Mark data as stale if we have existing data
            if (currentData) {
              setIsStale(true)
            }
            return currentData
          })
        }
      } finally {
        if (!isBackground) {
          setLoading(false)
        }
      }
    },
    [slug, fallbackData, backgroundRefresh]
  )

  useEffect(() => {
    fetchBusinessUnit()
  }, [fetchBusinessUnit])

  const refetch = useCallback(
    async (forceRefresh = true) => {
      if (!slug) return



      await fetchBusinessUnit(forceRefresh)
    },
    [slug, fetchBusinessUnit]
  )

  const preloadRelated = useCallback(
    async (relatedSlugs = []) => {
      // Preloading removed for simplicity
      console.log('Preloading disabled for simplicity')
    },
    [enablePreloading]
  )

  return {
    data,
    loading,
    error,
    isStale,
    refetch,
    preloadRelated,
  }
}

export const useAllBusinessUnits = (options = {}) => {
  const { backgroundRefresh = true, autoPreload = true } = options

  const [data, setData] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [isStale, setIsStale] = useState(false)

  const abortControllerRef = useRef(null)
  const backgroundRefreshTimeoutRef = useRef(null)

  // Cleanup function
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
      if (backgroundRefreshTimeoutRef.current) {
        clearTimeout(backgroundRefreshTimeoutRef.current)
      }
    }
  }, [])

  const fetchAllBusinessUnits = useCallback(
    async (forceRefresh = false, isBackground = false) => {
      // Abort previous request if still pending
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
      abortControllerRef.current = new AbortController()

      try {
        if (!isBackground) {
          setLoading(true)
          setError(null)
        }

        const businessUnits = await businessUnitService.getAllBusinessUnits()

        // Check if request was aborted
        if (abortControllerRef.current?.signal.aborted) {
          return
        }

        setData(businessUnits)
        setIsStale(false)

        // Preload individual business units if enabled
        // Auto-preloading removed for simplicity

        // Schedule background refresh if enabled (reduced frequency)
        if (backgroundRefresh && !isBackground) {
          backgroundRefreshTimeoutRef.current = setTimeout(() => {
            fetchAllBusinessUnits(false, true)
          }, 600000) // Refresh every 10 minutes in background
        }
      } catch (err) {
        // Check if request was aborted
        if (abortControllerRef.current?.signal.aborted) {
          return
        }

        console.error('Failed to fetch business units:', err)

        if (!isBackground) {
          setError(err.message || 'Failed to load business units')

          // Mark data as stale if we have existing data
          if (data.length > 0) {
            setIsStale(true)
          }
        }
      } finally {
        if (!isBackground) {
          setLoading(false)
        }
      }
    },
    [data.length, autoPreload, backgroundRefresh]
  )

  useEffect(() => {
    fetchAllBusinessUnits()
  }, [fetchAllBusinessUnits])

  const refetch = useCallback(
    async () => {
      // Cache clearing removed for simplicity

      await fetchAllBusinessUnits(forceRefresh)
    },
    [fetchAllBusinessUnits]
  )

  return {
    data,
    loading,
    error,
    isStale,
    refetch,
  }
}
