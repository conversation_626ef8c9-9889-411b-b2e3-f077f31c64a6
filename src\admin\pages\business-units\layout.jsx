/**
 * BusinessUnitsLayout - Layout component for business units management
 */

import { Outlet, useLocation, useNavigate } from 'react-router-dom'
import Button from '../../../components/ui/Button.jsx'
import Badge from '../../../components/ui/Badge.jsx'

export default function BusinessUnitsLayout() {
  const location = useLocation()
  const navigate = useNavigate()

  const getPageTitle = () => {
    const path = location.pathname
    if (path.includes('/create')) return 'Create Business Unit'
    if (path.includes('/edit')) return 'Edit Business Unit'
    if (path.match(/\/\d+$/) && !path.includes('/edit')) return 'View Business Unit'
    return 'Business Units Management'
  }

  const getPageDescription = () => {
    const path = location.pathname
    if (path.includes('/create')) return 'Create a new business unit with all necessary details'
    if (path.includes('/edit')) return 'Edit business unit details and content'
    if (path.match(/\/\d+$/) && !path.includes('/edit')) return 'View business unit details and information'
    return 'Manage your business unit details and content'
  }

  const showBackButton = () => {
    const path = location.pathname
    return path !== '/admin/business-units'
  }

  const handleBack = () => {
    if (location.pathname.includes('/edit') || location.pathname.includes('/create')) {
      navigate('/admin/business-units')
    } else if (location.pathname.match(/\/\d+$/)) {
      navigate('/admin/business-units')
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">{getPageTitle()}</h1>
          <p className="text-zinc-400">{getPageDescription()}</p>
        </div>
        {showBackButton() && (
          <Button variant="secondary" onClick={handleBack}>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to List
          </Button>
        )}
      </div>

      {/* Breadcrumb */}
      <nav className="flex items-center space-x-2 text-sm text-zinc-400">
        <button
          onClick={() => navigate('/admin/business-units')}
          className="hover:text-white transition-colors"
        >
          Business Units
        </button>
        {location.pathname !== '/admin/business-units' && (
          <>
            <span>/</span>
            <span className="text-white">{getPageTitle()}</span>
          </>
        )}
      </nav>

      {/* Page Content */}
      <Outlet />
    </div>
  )
}
