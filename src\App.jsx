import { BrowserRouter, Route, Routes, useLocation } from 'react-router-dom'
import { Suspense, lazy } from 'react'
import PreLoader from './components/PreLoader'
import Navbar from './components/Navbar'
import Footer from './components/Footer'
import ErrorBoundary from './components/ErrorBoundary'
// Debug components removed for simplicity

// Lazy load pages for better performance
const Home = lazy(() => import('./Pages/Home'))
const About = lazy(() => import('./Pages/About'))
const Contact = lazy(() => import('./Pages/Contact'))
const Usaha = lazy(() => import('./Pages/Usaha'))
const News = lazy(() => import('./Pages/News'))
const Cigifarm = lazy(() => import('./Pages/Detailusaha/cigifarm'))
const Ciginet = lazy(() => import('./Pages/Detailusaha/ciginet'))
const Cigimart = lazy(() => import('./Pages/Detailusaha/cigimart'))
const Cigifood = lazy(() => import('./Pages/Detailusaha/cigifood'))
const PBcigi = lazy(() => import('./Pages/Detailusaha/pbcigi'))
const Cigiarchery = lazy(() => import('./Pages/Detailusaha/cigiarchery'))
const CigiFC = lazy(() => import('./Pages/Detailusaha/cigifc'))
const NotFound = lazy(() => import('./Pages/NotFound'))

// News components
const NewsArticle = lazy(() => import('./components/news/NewsArticle'))

// Admin components (simplified)
const AdminLogin = lazy(() => import('./admin/components/AdminLogin'))
const AdminLayout = lazy(() => import('./admin/components/AdminLayout'))
const AdminDashboard = lazy(() => import('./admin/pages/AdminDashboard'))
const NewsManagement = lazy(() => import('./admin/pages/NewsManagement'))
const SiteSettings = lazy(() => import('./admin/pages/SiteSettings'))
const BusinessUnitsManagement = lazy(() => import('./admin/pages/BusinessUnitsManagement'))

// Business Units components
const BusinessUnitsLayout = lazy(() => import('./admin/pages/business-units/layout'))
const BusinessUnitsIndex = lazy(() => import('./admin/pages/business-units/index'))
const BusinessUnitsCreate = lazy(() => import('./admin/pages/business-units/create'))
const BusinessUnitsEdit = lazy(() => import('./admin/pages/business-units/edit'))
const BusinessUnitsShow = lazy(() => import('./admin/pages/business-units/show'))

// Loading component
const PageLoader = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="w-8 h-8 border-4 border-amber-600 border-t-transparent rounded-full animate-spin"></div>
  </div>
)

// Component to handle conditional layout
function AppContent() {
  const location = useLocation()
  const isAdminRoute = location.pathname.startsWith('/admin')

  return (
    <div className="min-h-screen bg-black text-white">
      {!isAdminRoute && <PreLoader />}
      {!isAdminRoute && <Navbar />}
      <main className={!isAdminRoute ? 'pt-16' : ''}>
        <Suspense fallback={<PageLoader />}>
          <Routes>
            {/* Public routes */}
            <Route path="/" element={<Home />} />
            <Route path="/about" element={<About />} />
            <Route path="/usaha" element={<Usaha />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/news" element={<News />} />
            <Route path="/news/:slug" element={<NewsArticle />} />
            <Route path="/cigifarm" element={<Cigifarm />} />
            <Route path="/ciginet" element={<Ciginet />} />
            <Route path="/cigimart" element={<Cigimart />} />
            <Route path="/cigifood" element={<Cigifood />} />
            <Route path="/pbcigi" element={<PBcigi />} />
            <Route path="/cigiarchery" element={<Cigiarchery />} />
            <Route path="/cigifc" element={<CigiFC />} />
            <Route path="/krlcigi" element={<NotFound />} />

            {/* Admin routes */}
            <Route path="/admin/login" element={<AdminLogin />} />
            <Route path="/admin" element={<AdminLayout />}>
              <Route path="dashboard" element={<AdminDashboard />} />
              <Route path="business-units" element={<BusinessUnitsLayout />}>
                <Route index element={<BusinessUnitsIndex />} />
                <Route path="create" element={<BusinessUnitsCreate />} />
                <Route path=":id" element={<BusinessUnitsShow />} />
                <Route path=":id/edit" element={<BusinessUnitsEdit />} />
              </Route>
              <Route path="news" element={<NewsManagement />} />
              <Route path="settings" element={<SiteSettings />} />
              <Route index element={<AdminDashboard />} />
            </Route>

            <Route path="*" element={<NotFound />} />
          </Routes>
        </Suspense>
      </main>
      {!isAdminRoute && <Footer />}
    </div>
  )
}

function App() {
  return (
    <ErrorBoundary>
      <BrowserRouter>
        <AppContent />
      </BrowserRouter>
    </ErrorBoundary>
  )
}

export default App
