import { useState, useEffect } from 'react'

const PreLoader = () => {
  const [loading, setLoading] = useState(true)
  const [fadeOut, setFadeOut] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => {
      setFadeOut(true)
      setTimeout(() => setLoading(false), 500)
    }, 2000)

    return () => clearTimeout(timer)
  }, [])

  if (!loading) return null

  return (
    <div
      className={`fixed inset-0 z-[9999] flex items-center justify-center bg-gradient-to-br from-black via-zinc-900 to-black transition-opacity duration-500 ${fadeOut ? 'opacity-0' : 'opacity-100'}`}
    >
      <div className="text-center">
        {/* Modern Logo Animation */}
        <div className="relative mb-8">
          <div className="w-20 h-20 mx-auto relative">
            {/* Outer ring */}
            <div className="absolute inset-0 border-4 border-amber-600/30 rounded-full animate-spin"></div>
            {/* Inner ring */}
            <div
              className="absolute inset-2 border-4 border-amber-500/50 rounded-full animate-spin"
              style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}
            ></div>
            {/* Center dot */}
            <div className="absolute inset-6 bg-amber-500 rounded-full animate-pulse"></div>
          </div>
        </div>

        {/* Brand Name */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold text-white">
            Cigi<span className="text-amber-500">Global</span>
          </h1>
          <p className="text-zinc-400 text-sm">Loading amazing experience...</p>
        </div>

        {/* Progress Bar */}
        <div className="mt-8 w-64 mx-auto">
          <div className="h-1 bg-zinc-800 rounded-full overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-amber-600 to-amber-400 rounded-full animate-pulse"
              style={{
                animation: 'loading-progress 2s ease-in-out forwards',
                width: '0%',
              }}
            ></div>
          </div>
        </div>
      </div>

      <style>{`
        @keyframes loading-progress {
          0% {
            width: 0%;
          }
          50% {
            width: 60%;
          }
          100% {
            width: 100%;
          }
        }
      `}</style>
    </div>
  )
}

export default PreLoader
