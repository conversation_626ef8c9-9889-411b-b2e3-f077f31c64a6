import { Link } from 'react-router-dom'
import Section from '../components/ui/Section'
import Card from '../components/ui/Card'
import Badge from '../components/ui/Badge'
import Button from '../components/ui/Button'
import AnimatedElement from '../components/ui/AnimatedElement'

const unitUsahaData = [
  {
    nama: 'Cigi Farm',
    gambar: 'https://picsum.photos/600/400',
    deskripsi: 'Peternakan ayam petelur, ikan lele, dan ikan nila untuk ketahanan pangan desa.',
    link: '/Detailusaha/Cigifarm',
    icon: '🌱',
    category: 'Agrikultur',
    status: 'Aktif',
  },
  {
    nama: 'Cigi Net',
    gambar: 'https://picsum.photos/600/400',
    deskripsi: 'Layanan internet desa untuk konektivitas masyarakat Cimande Girang.',
    link: '/Detailusaha/Ciginet',
    icon: '🌐',
    category: 'Teknologi',
    status: 'Aktif',
  },
  {
    nama: 'Cigi Mart',
    gambar: 'https://picsum.photos/300/200',
    deskripsi: 'Supermarket lokal menyediakan sembako dan kebutuhan harian warga.',
    link: '/Detailusaha/Cigimart',
    icon: '🏪',
    category: 'Retail',
    status: 'Segera',
  },
  {
    nama: 'Cigi Food',
    gambar: 'https://picsum.photos/300/200',
    deskripsi: 'Produk makanan dan minuman olahan khas desa yang lezat dan higienis.',
    link: '/Detailusaha/Cigifood',
    icon: '🍽️',
    category: 'F&B',
    status: 'Segera',
  },
  {
    nama: 'Cigi FC',
    gambar: 'https://picsum.photos/300/200',
    deskripsi: 'Klub sepak bola desa untuk pembinaan dan kegiatan olahraga pemuda.',
    icon: '⚽',
    category: 'Olahraga',
    status: 'Aktif',
  },
  {
    nama: 'Cigi Panahan',
    gambar: 'https://picsum.photos/300/200',
    deskripsi: 'Unit olahraga panahan untuk kegiatan positif dan tradisional desa.',
    icon: '🏹',
    category: 'Olahraga',
    status: 'Aktif',
  },
  {
    nama: 'Cigi Bulu Tangkis',
    gambar: 'https://picsum.photos/300/200',
    deskripsi: 'Klub bulu tangkis untuk anak-anak dan remaja desa.',
    icon: '🏸',
    category: 'Olahraga',
    status: 'Aktif',
  },
  {
    nama: 'Cigi Media',
    gambar: 'https://picsum.photos/300/200',
    deskripsi: 'Media dokumentasi dan promosi kegiatan UMKM dan warga.',
    icon: '📸',
    category: 'Media',
    status: 'Aktif',
  },
]

export default function Usaha() {
  return (
    <>
      {/* Hero Section */}
      <Section padding="xl">
        <div className="text-center max-w-4xl mx-auto mb-16">
          <AnimatedElement animation="fadeInUp">
            <Badge variant="primary" size="lg" className="mb-6">
              Unit Usaha
            </Badge>
            <h1 className="text-responsive-xl mb-6">
              Ekosistem <span className="gradient-text">Bisnis Desa</span>
            </h1>
            <p className="text-xl text-zinc-300 leading-relaxed">
              Berbagai unit usaha yang kami kelola untuk pemberdayaan dan kemandirian ekonomi
              masyarakat desa
            </p>
          </AnimatedElement>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-16">
          <AnimatedElement animation="fadeInUp" delay={100}>
            <Card className="text-center" padding="lg">
              <div className="w-16 h-16 bg-gradient-to-br from-amber-500 to-amber-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-white">8</span>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Unit Usaha</h3>
              <p className="text-zinc-400">Berbagai bidang usaha</p>
            </Card>
          </AnimatedElement>

          <AnimatedElement animation="fadeInUp" delay={200}>
            <Card className="text-center" padding="lg">
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-white">6</span>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Aktif Beroperasi</h3>
              <p className="text-zinc-400">Unit yang sudah berjalan</p>
            </Card>
          </AnimatedElement>

          <AnimatedElement animation="fadeInUp" delay={300}>
            <Card className="text-center" padding="lg">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-white">100+</span>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Lapangan Kerja</h3>
              <p className="text-zinc-400">Peluang kerja tercipta</p>
            </Card>
          </AnimatedElement>
        </div>

        {/* Unit Usaha Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {unitUsahaData.map((unit, index) => {
            const CardContent = (
              <Card className="group overflow-hidden h-full" padding="none">
                <div className="aspect-video overflow-hidden relative">
                  <img
                    src={unit.gambar}
                    alt={unit.nama}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                    loading="lazy"
                  />
                  <div className="absolute top-4 left-4">
                    <div className="w-12 h-12 bg-white/90 backdrop-blur-sm rounded-xl flex items-center justify-center">
                      <span className="text-xl">{unit.icon}</span>
                    </div>
                  </div>
                  <div className="absolute top-4 right-4">
                    <Badge variant={unit.status === 'Aktif' ? 'success' : 'warning'} size="sm">
                      {unit.status}
                    </Badge>
                  </div>
                </div>

                <div className="p-6 flex-1 flex flex-col">
                  <div className="flex items-center gap-2 mb-3">
                    <Badge variant="default" size="sm">
                      {unit.category}
                    </Badge>
                  </div>

                  <h2 className="text-xl font-bold text-white mb-3 group-hover:text-amber-400 transition-colors">
                    {unit.nama}
                  </h2>

                  <p className="text-zinc-400 leading-relaxed mb-6 flex-1">{unit.deskripsi}</p>

                  {unit.link ? (
                    <Button variant="primary" className="w-full group-hover:bg-amber-500">
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                        />
                      </svg>
                      Lihat Detail
                    </Button>
                  ) : (
                    <Button variant="secondary" className="w-full" disabled>
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                      Segera Hadir
                    </Button>
                  )}
                </div>
              </Card>
            )

            return (
              <AnimatedElement key={index} animation="fadeInUp" delay={index * 100}>
                {unit.link ? (
                  <Link to={unit.link} className="block h-full">
                    {CardContent}
                  </Link>
                ) : (
                  <div className="h-full">{CardContent}</div>
                )}
              </AnimatedElement>
            )
          })}
        </div>
      </Section>

      {/* CTA Section */}
      <Section padding="xl" background="gradient">
        <AnimatedElement animation="fadeInUp">
          <Card className="max-w-4xl mx-auto text-center" padding="xl">
            <h2 className="text-responsive-lg mb-6">Tertarik Berkolaborasi?</h2>
            <p className="text-lg text-zinc-400 mb-8 max-w-2xl mx-auto">
              Mari bergabung dengan kami dalam membangun ekosistem bisnis desa yang berkelanjutan
              dan memberdayakan masyarakat lokal
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                  />
                </svg>
                Hubungi Kami
              </Button>
              <Button variant="secondary" size="lg">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                Pelajari Lebih Lanjut
              </Button>
            </div>
          </Card>
        </AnimatedElement>
      </Section>
    </>
  )
}
