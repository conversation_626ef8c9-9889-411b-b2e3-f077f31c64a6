import { forwardRef } from 'react'
import Container from './Container'

const Section = forwardRef(
  (
    {
      children,
      className = '',
      containerSize = 'default',
      padding = 'default',
      background = 'transparent',
      ...props
    },
    ref
  ) => {
    const baseClasses = 'relative'

    const paddingClasses = {
      none: '',
      sm: 'py-12',
      default: 'py-16 sm:py-20',
      lg: 'py-20 sm:py-24',
      xl: 'py-24 sm:py-32',
    }

    const backgroundClasses = {
      transparent: '',
      dark: 'bg-zinc-900',
      darker: 'bg-black',
      gradient: 'bg-gradient-to-br from-zinc-900 via-zinc-800 to-zinc-900',
    }

    const classes = `${baseClasses} ${paddingClasses[padding]} ${backgroundClasses[background]} ${className}`

    return (
      <section ref={ref} className={classes} {...props}>
        <Container size={containerSize}>{children}</Container>
      </section>
    )
  }
)

Section.displayName = 'Section'

export default Section
