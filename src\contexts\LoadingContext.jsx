/**
 * LoadingContext - Context for managing loading states across the application
 */

import { createContext, useState, useCallback } from 'react'

const LoadingContext = createContext()

export const LoadingProvider = ({ children }) => {
  const [loadingStates, setLoadingStates] = useState({})

  const setLoading = useCallback((key, isLoading) => {
    setLoadingStates((prev) => ({
      ...prev,
      [key]: isLoading,
    }))
  }, [])

  const isLoading = useCallback(
    (key) => {
      return Boolean(loadingStates[key])
    },
    [loadingStates]
  )

  const isAnyLoading = useCallback(() => {
    return Object.values(loadingStates).some(Boolean)
  }, [loadingStates])

  const clearLoading = useCallback((key) => {
    setLoadingStates((prev) => {
      const newState = { ...prev }
      delete newState[key]
      return newState
    })
  }, [])

  const clearAllLoading = useCallback(() => {
    setLoadingStates({})
  }, [])

  const value = {
    setLoading,
    isLoading,
    isAnyLoading,
    clearLoading,
    clearAllLoading,
    loadingStates,
  }

  return <LoadingContext.Provider value={value}>{children}</LoadingContext.Provider>
}

export default LoadingContext
