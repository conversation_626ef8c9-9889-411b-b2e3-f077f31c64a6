# Design Document

## Overview

This design implements a comprehensive content management system for business unit (detail usaha) data within the existing Cigi Global admin interface. The solution will transform the current static JavaScript data files into a dynamic, database-driven system that allows administrators to edit business unit information through a web interface.

The design leverages the existing admin infrastructure (authentication, layout, routing) and extends it with new business unit management capabilities. The system will maintain backward compatibility with the existing DetailUsahaTemplate component while adding real-time data management features.

## Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Admin UI      │    │   API Layer     │    │   Data Layer    │
│                 │    │                 │    │                 │
│ - Business Unit │◄──►│ - CRUD APIs     │◄──►│ - MySQL DB      │
│   Management    │    │ - Validation    │    │ - File Storage  │
│ - Form Editor   │    │ - Auth Check    │    │ - Data Models   │
│ - Preview Mode  │    │ - Error Handle  │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Data Flow

1. **Read Operations**: Admin UI → API → Database → JSON Response → UI Rendering
2. **Write Operations**: Admin Form → Client Validation → API → Server Validation → Database → Cache Invalidation → Success Response → UI Update
3. **Public Display**: DetailUsahaTemplate → API → Database → Rendered Content
4. **Preview Flow**: Edit Form → Preview Modal → DetailUsahaTemplate (with unsaved changes) → Preview Display
5. **Publishing Flow**: Preview Mode → Publish Action → API → Database → Cache Clear → Live Site Update
6. **Navigation Flow**: Admin Dashboard → Business Units List → Edit Form → Preview → Publish/Cancel

## Components and Interfaces

### 1. Database Schema

**business_units table:**

```sql
CREATE TABLE business_units (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(100) UNIQUE NOT NULL,
  badge_text VARCHAR(255),
  badge_variant VARCHAR(50) DEFAULT 'primary',
  badge_icon VARCHAR(255),
  description TEXT,
  theme_color_from VARCHAR(7),
  theme_color_to VARCHAR(7),
  theme_color_primary VARCHAR(7),
  layout_config JSON,
  about_image VARCHAR(500),
  about_content TEXT,
  highlight_text TEXT,
  highlight_class VARCHAR(255),
  vision TEXT,
  mission JSON,
  badges JSON,
  contact_address TEXT,
  contact_phone VARCHAR(50),
  contact_email VARCHAR(255),
  contact_hours VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_slug (slug),
  INDEX idx_name (name)
);
```

**products table:**

```sql
CREATE TABLE products (
  id VARCHAR(36) PRIMARY KEY,
  business_unit_id VARCHAR(36) NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  image VARCHAR(500),
  price VARCHAR(100),
  features JSON,
  sort_order INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (business_unit_id) REFERENCES business_units (id) ON DELETE CASCADE,
  INDEX idx_business_unit_sort (business_unit_id, sort_order)
);
```

**statistics table:**

```sql
CREATE TABLE statistics (
  id VARCHAR(36) PRIMARY KEY,
  business_unit_id VARCHAR(36) NOT NULL,
  title VARCHAR(255),
  description TEXT,
  items JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (business_unit_id) REFERENCES business_units (id) ON DELETE CASCADE,
  INDEX idx_business_unit (business_unit_id)
);
```

**gallery_images table:**

```sql
CREATE TABLE gallery_images (
  id VARCHAR(36) PRIMARY KEY,
  business_unit_id VARCHAR(36) NOT NULL,
  src VARCHAR(500) NOT NULL,
  alt VARCHAR(255),
  title VARCHAR(255),
  description TEXT,
  sort_order INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (business_unit_id) REFERENCES business_units (id) ON DELETE CASCADE,
  INDEX idx_business_unit_sort (business_unit_id, sort_order)
);
```

### 2. API Endpoints

**Business Units Management:**

- `GET /api/business-units` - List all business units with name, badge text, and last modified date
- `GET /api/business-units/:slug` - Get specific business unit data for editing or public display
- `POST /api/business-units` - Create new business unit (admin only)
- `PUT /api/business-units/:id` - Update business unit with immediate cache invalidation
- `DELETE /api/business-units/:id` - Delete business unit with confirmation
- `POST /api/business-units/:id/publish` - Publish changes from preview mode

**Products Management:**

- `GET /api/business-units/:id/products` - Get products for business unit with sort order
- `POST /api/business-units/:id/products` - Add product with validation
- `PUT /api/products/:id` - Update product including features list
- `DELETE /api/products/:id` - Delete product with confirmation
- `PUT /api/products/reorder` - Batch reorder products with drag-and-drop support

**Content Management:**

- `PUT /api/business-units/:id/about` - Update about section (content, vision, mission, badges)
- `PUT /api/business-units/:id/statistics` - Update statistics with title, description, and items
- `GET /api/business-units/:id/gallery` - Get gallery images with metadata
- `POST /api/business-units/:id/gallery` - Add gallery image with file validation
- `PUT /api/gallery/:id` - Update gallery image metadata
- `DELETE /api/gallery/:id` - Delete gallery image
- `PUT /api/business-units/:id/contact` - Update contact information

**Preview & Publishing:**

- `POST /api/business-units/:id/preview` - Generate preview data with unsaved changes
- `POST /api/business-units/:id/publish` - Publish changes and clear caches
- `GET /api/business-units/:slug/public` - Get published data for public display

### 3. Admin UI Components

**BusinessUnitsList Component:**

- Displays list of all business units (pbcigi, ciginet, cigimart, cigifood, cigifc, cigifarm, cigiarchery)
- Shows unit name, badge text, and last modified date for each unit
- Provides edit and delete actions with proper confirmation dialogs
- Includes search and filter functionality for easy navigation
- Displays appropriate empty state message when no business units exist
- Integrates with admin dashboard navigation as "Business Units" option

**BusinessUnitEditor Component:**

- Tabbed interface for different sections (Basic Info, About, Products, Statistics, Gallery, Contact)
- Comprehensive form validation with real-time error display
- Auto-save functionality with unsaved changes warning before navigation
- Preview mode integration with publish workflow
- Cancel functionality that discards unsaved changes and returns to list
- Success/error messaging for all operations

**BasicInfoForm Component:**

- Form fields for name, description, badge text, and theme colors
- Color picker component for theme color selection (from, to, primary)
- Badge variant selector with live preview
- Real-time validation with specific error messages
- Input validation for required fields and format constraints

**AboutSectionForm Component:**

- Rich text editor or textarea for about content editing
- Vision statement text field
- Dynamic mission statements list with add/remove/reorder functionality
- Highlight text and CSS class configuration fields
- About image URL field with preview capability
- Dynamic badges management with text and variant editing

**ProductManager Component:**

- Dynamic product list with add/edit/remove functionality
- Confirmation dialogs for product deletion
- Product form with title, description, price, image, and features fields
- Dynamic features list management for each product
- Drag-and-drop reordering with alternative up/down controls
- Sort order persistence and API integration

**StatisticsManager Component:**

- Form fields for statistics title and description
- Dynamic statistics items list with value and label editing
- Add/remove functionality for individual statistics
- Data validation for statistics format

**GalleryManager Component:**

- Gallery images list with add/remove functionality
- Image upload validation for file types and sizes
- Title, description, and alt text fields for each image
- Image reordering with drag-and-drop or controls
- Preview functionality for uploaded images

**ContactInfoForm Component:**

- Form fields for address, phone, email, and hours
- Email format validation
- Phone number format validation
- Contact information preview display

**PreviewModal Component:**

- Renders business unit using DetailUsahaTemplate with unsaved changes
- Clear preview mode indication to distinguish from live version
- Publish functionality accessible from preview interface
- Return to edit mode capability
- Responsive preview modes for different screen sizes
- Publishing confirmation and success/error handling

### 4. Data Service Layer

**BusinessUnitService:**

```javascript
class BusinessUnitService {
  // Core CRUD operations
  async getAllBusinessUnits()
  async getBusinessUnit(slug)
  async createBusinessUnit(data)
  async updateBusinessUnit(id, data)
  async deleteBusinessUnit(id)

  // Content management
  async updateAboutSection(id, aboutData)
  async updateBasicInfo(id, basicData)
  async updateContactInfo(id, contactData)

  // Products management
  async getBusinessUnitProducts(id)
  async addProduct(businessUnitId, productData)
  async updateProduct(productId, productData)
  async deleteProduct(productId)
  async reorderProducts(businessUnitId, productIds)

  // Statistics management
  async updateStatistics(id, statisticsData)

  // Gallery management
  async getBusinessUnitGallery(id)
  async addGalleryImage(businessUnitId, imageData)
  async updateGalleryImage(imageId, imageData)
  async deleteGalleryImage(imageId)
  async reorderGalleryImages(businessUnitId, imageIds)

  // Preview and publishing
  async generatePreviewData(id, unsavedChanges)
  async publishChanges(id)
  async validateBusinessUnitData(data)

  // Cache management
  async invalidateCache(businessUnitId)
  async warmCache(businessUnitId)
}
```

## Data Models

### BusinessUnit Model

```javascript
{
  id: string,
  name: string,
  slug: string,
  badgeText: string,
  badgeVariant: 'primary' | 'secondary' | 'success' | 'warning' | 'info',
  badgeIcon: string,
  description: string,
  themeColor: {
    from: string,
    to: string,
    primary: string
  },
  layoutConfig: {
    productsLayout: 'grid' | 'carousel' | 'showcase' | 'list' | 'pricing'
  },
  aboutImage: string,
  aboutContent: string,
  highlightText: string,
  highlightClass: string,
  vision: string,
  mission: string[],
  badges: Array<{text: string, variant: string}>,
  products: {
    title: string,
    description: string,
    items: Product[],
    cta: {text: string}
  },
  statistics: {
    title: string,
    description: string,
    items: Array<{value: string, label: string}>
  },
  gallery: {
    title: string,
    description: string,
    images: GalleryImage[]
  },
  contact: {
    address: string,
    phone: string,
    email: string,
    hours: string
  },
  createdAt: Date,
  updatedAt: Date
}
```

### Product Model

```javascript
{
  id: string,
  businessUnitId: string,
  title: string,
  description: string,
  image: string,
  price: string,
  features: string[],
  sortOrder: number
}
```

## Error Handling

### Validation Rules

- **Business Unit Name**: Required, 3-100 characters, unique across system
- **Slug**: Required, unique, URL-safe format, auto-generated from name
- **Description**: Required, 10-500 characters for basic info
- **Badge Text**: Optional, max 50 characters
- **Badge Variant**: Must be one of: primary, secondary, success, warning, info
- **Theme Colors**: Valid hex color format (#RRGGBB) for from, to, and primary colors
- **About Content**: Optional, rich text with HTML sanitization
- **Vision**: Optional, max 1000 characters
- **Mission Items**: Each item max 500 characters, minimum 1 item if mission section used
- **Product Title**: Required, 3-100 characters
- **Product Price**: Optional, max 50 characters
- **Product Features**: Each feature max 200 characters
- **Statistics Values**: Required for each stat item, max 50 characters
- **Statistics Labels**: Required for each stat item, max 100 characters
- **Contact Email**: Valid email format if provided
- **Contact Phone**: Valid phone format if provided
- **Image URLs**: Valid URL format, supported image types (jpg, jpeg, png, webp)
- **Image File Upload**: Max 5MB, supported formats only
- **Gallery Image Alt Text**: Required for accessibility, max 200 characters

### Error Response Format

```javascript
{
  success: false,
  error: string,
  details?: {
    field: string,
    message: string
  }[]
}
```

### Error Handling Strategy

- **Client-side**: Form validation with real-time feedback and specific field-level error messages
- **Server-side**: Comprehensive validation with detailed error messages and proper HTTP status codes
- **Database**: Transaction rollback on failures with proper error logging
- **UI**: User-friendly error messages with recovery suggestions and clear action paths
- **Navigation**: Unsaved changes warning before leaving edit mode
- **File Upload**: Validation for image file types, sizes, and format requirements
- **Confirmation Dialogs**: Required for destructive actions like deletion
- **Cache Management**: Automatic cache invalidation on successful updates

## Testing Strategy

### Unit Tests

- **Data Models**: Validation, serialization, relationships
- **API Endpoints**: CRUD operations, authentication, error handling
- **Business Logic**: Data transformation, validation rules
- **UI Components**: Form interactions, state management

### Integration Tests

- **API Integration**: End-to-end API workflows
- **Database Operations**: Data persistence and retrieval
- **Authentication**: Session validation and authorization
- **File Operations**: Image upload and storage

### User Acceptance Tests

- **Admin Workflows**: Complete business unit management flows
- **Public Display**: Verify changes appear correctly on public pages
- **Error Scenarios**: Graceful handling of various error conditions
- **Performance**: Response times for data operations

### Test Data Strategy

- **Seed Data**: Migrate existing static data as initial test data
- **Mock Data**: Generate realistic test data for development
- **Cleanup**: Automated test data cleanup procedures

## Database Configuration

### MySQL Connection Setup

```javascript
const mysql = require('mysql2/promise')

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME || 'cigi_global',
  charset: 'utf8mb4',
  timezone: '+00:00',
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
}

const pool = mysql.createPool({
  ...dbConfig,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
})
```

### JSON Column Handling

- MySQL 5.7+ native JSON support for complex data structures (mission, badges, features, statistics items)
- Automatic JSON validation and indexing capabilities
- Efficient querying of JSON fields using MySQL JSON functions
- Proper handling of JSON data serialization/deserialization in the application layer

### Performance Considerations

- Connection pooling for efficient database connections
- Proper indexing on frequently queried columns (slug, business_unit_id, sort_order)
- JSON column indexing for nested data queries
- Query optimization for list views and search functionality

## Admin Integration Strategy

### Navigation Integration

The business units management system will be integrated into the existing admin interface through:

**Admin Dashboard Integration:**

- Add "Business Units" navigation option to the admin sidebar
- Display business units overview widget on admin dashboard
- Show recent updates and quick action buttons
- Maintain consistent admin UI styling and layout

**Routing Strategy:**

- `/admin/business-units` - Business units list page
- `/admin/business-units/:id/edit` - Business unit editor
- `/admin/business-units/:id/preview` - Preview mode
- `/admin/business-units/new` - Create new business unit (if needed)

**Authentication & Authorization:**

- Leverage existing admin authentication system
- Ensure only authenticated admin users can access business units management
- Maintain session validation for all business unit operations

**UI Consistency:**

- Use existing admin component library and styling
- Follow established admin interface patterns
- Maintain responsive design principles
- Integrate with existing notification/toast system

### Cache Management Strategy

**Client-Side Caching:**

- Implement React Query or similar for API response caching
- Cache business unit list data with automatic invalidation
- Cache individual business unit data during editing sessions
- Clear relevant caches on successful updates

**Server-Side Caching:**

- Implement Redis or memory caching for frequently accessed data
- Cache compiled business unit data for public display
- Automatic cache invalidation on data updates
- Cache warming strategies for better performance

**Public Site Integration:**

- Ensure DetailUsahaTemplate receives updated data immediately after publishing
- Implement fallback mechanisms for cache failures
- Monitor cache hit rates and performance metrics

## Migration Strategy

### Phase 1: MySQL Database Setup and Data Migration

1. Set up MySQL database connection and connection pooling
2. Create database tables with proper MySQL data types and JSON columns
3. Create indexes for optimal query performance
4. Migrate existing static data to MySQL database with proper JSON formatting
5. Create data access layer with MySQL-specific optimizations and transaction handling
6. Implement basic CRUD operations with proper error handling

### Phase 2: Admin Interface Development

1. Create business units list page
2. Implement basic edit forms
3. Add validation and error handling
4. Integrate with existing admin layout

### Phase 3: Advanced Features

1. Add preview functionality
2. Implement drag-and-drop reordering
3. Add image upload capabilities
4. Create advanced form components

### Phase 4: Integration and Testing

1. Update DetailUsahaTemplate to use API data
2. Implement caching strategy
3. Add comprehensive testing
4. Performance optimization

### Preview and Publishing Workflow

**Preview System Design:**
The preview system allows administrators to see exactly how their changes will appear on the public website before publishing:

1. **Preview Generation**: When user clicks preview, system creates a temporary data object combining saved data with current form state
2. **Preview Rendering**: PreviewModal renders the business unit using the same DetailUsahaTemplate component used on public site
3. **Preview Indicators**: Clear visual indicators distinguish preview mode from live content
4. **Responsive Preview**: Preview supports different screen sizes to ensure mobile compatibility

**Publishing Workflow:**

1. **Preview Review**: Administrator reviews changes in preview mode
2. **Publish Action**: Click publish button from preview interface
3. **Data Validation**: Server validates all data before committing changes
4. **Database Update**: Transaction-based update ensures data consistency
5. **Cache Invalidation**: Clear all relevant caches to ensure fresh data delivery
6. **Success Confirmation**: User receives confirmation that changes are live

**Unsaved Changes Management:**

- Track form state changes in real-time
- Display warning dialog when attempting to navigate away with unsaved changes
- Provide clear save/discard options
- Auto-save functionality with configurable intervals
- Visual indicators for unsaved changes status

### Backward Compatibility

- Maintain existing static data files during transition period
- Implement fallback mechanism to static data if API fails
- Gradual migration approach with feature flags for safe rollout
- Preserve existing URL structure and routing patterns
- Ensure DetailUsahaTemplate works with both static and dynamic data sources
