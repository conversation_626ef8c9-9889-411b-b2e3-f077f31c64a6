/**
 * AdminSkeletons - Skeleton loading components for admin interface
 */

import { LoadingPulse } from './LoadingIndicator.jsx'

export const BusinessUnitsListSkeleton = () => (
  <div className="space-y-4">
    <div className="flex justify-between items-center">
      <LoadingPulse className="h-8 w-48" />
      <LoadingPulse className="h-10 w-32" />
    </div>

    <div className="grid gap-4">
      {Array.from({ length: 6 }).map((_, index) => (
        <div key={index} className="bg-zinc-800 rounded-lg p-6 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <LoadingPulse className="w-12 h-12 rounded-full" />
            <div className="space-y-2">
              <LoadingPulse className="h-5 w-32" />
              <LoadingPulse className="h-4 w-48" />
              <LoadingPulse className="h-3 w-24" />
            </div>
          </div>
          <div className="flex space-x-2">
            <LoadingPulse className="h-8 w-16" />
            <LoadingPulse className="h-8 w-16" />
          </div>
        </div>
      ))}
    </div>
  </div>
)

export const BusinessUnitEditorSkeleton = () => (
  <div className="space-y-6">
    {/* Header */}
    <div className="flex justify-between items-center">
      <LoadingPulse className="h-8 w-64" />
      <div className="flex space-x-2">
        <LoadingPulse className="h-10 w-20" />
        <LoadingPulse className="h-10 w-24" />
      </div>
    </div>

    {/* Tabs */}
    <div className="flex space-x-4 border-b border-zinc-700">
      {Array.from({ length: 6 }).map((_, index) => (
        <LoadingPulse key={index} className="h-10 w-20" />
      ))}
    </div>

    {/* Form Content */}
    <div className="bg-zinc-800 rounded-lg p-6 space-y-6">
      <div className="grid md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <LoadingPulse className="h-4 w-16" />
          <LoadingPulse className="h-10 w-full" />
        </div>
        <div className="space-y-4">
          <LoadingPulse className="h-4 w-20" />
          <LoadingPulse className="h-10 w-full" />
        </div>
      </div>

      <div className="space-y-4">
        <LoadingPulse className="h-4 w-24" />
        <LoadingPulse className="h-24 w-full" />
      </div>

      <div className="grid md:grid-cols-3 gap-4">
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="space-y-2">
            <LoadingPulse className="h-4 w-20" />
            <LoadingPulse className="h-10 w-full" />
          </div>
        ))}
      </div>
    </div>
  </div>
)

export const ProductManagerSkeleton = () => (
  <div className="space-y-4">
    <div className="flex justify-between items-center">
      <LoadingPulse className="h-6 w-32" />
      <LoadingPulse className="h-8 w-24" />
    </div>

    <div className="grid gap-4">
      {Array.from({ length: 3 }).map((_, index) => (
        <div key={index} className="bg-zinc-700 rounded-lg p-4">
          <div className="flex items-start space-x-4">
            <LoadingPulse className="w-16 h-16 rounded" />
            <div className="flex-1 space-y-3">
              <LoadingPulse className="h-5 w-3/4" />
              <LoadingPulse className="h-4 w-full" />
              <LoadingPulse className="h-4 w-2/3" />
              <div className="space-y-1">
                <LoadingPulse className="h-3 w-1/2" />
                <LoadingPulse className="h-3 w-2/3" />
                <LoadingPulse className="h-3 w-1/3" />
              </div>
            </div>
            <div className="flex flex-col space-y-2">
              <LoadingPulse className="h-8 w-8" />
              <LoadingPulse className="h-8 w-8" />
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>
)

export const GalleryManagerSkeleton = () => (
  <div className="space-y-4">
    <div className="flex justify-between items-center">
      <LoadingPulse className="h-6 w-32" />
      <LoadingPulse className="h-8 w-28" />
    </div>

    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
      {Array.from({ length: 6 }).map((_, index) => (
        <div key={index} className="bg-zinc-700 rounded-lg overflow-hidden">
          <LoadingPulse className="w-full h-32" />
          <div className="p-3 space-y-2">
            <LoadingPulse className="h-4 w-3/4" />
            <LoadingPulse className="h-3 w-full" />
            <div className="flex justify-between items-center">
              <LoadingPulse className="h-6 w-16" />
              <LoadingPulse className="h-6 w-6" />
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>
)

export const StatisticsManagerSkeleton = () => (
  <div className="space-y-6">
    <div className="grid md:grid-cols-2 gap-4">
      <div className="space-y-2">
        <LoadingPulse className="h-4 w-16" />
        <LoadingPulse className="h-10 w-full" />
      </div>
      <div className="space-y-2">
        <LoadingPulse className="h-4 w-20" />
        <LoadingPulse className="h-10 w-full" />
      </div>
    </div>

    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <LoadingPulse className="h-5 w-28" />
        <LoadingPulse className="h-8 w-20" />
      </div>

      <div className="grid md:grid-cols-2 gap-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="bg-zinc-700 rounded-lg p-4 flex items-center justify-between">
            <div className="space-y-2">
              <LoadingPulse className="h-4 w-16" />
              <LoadingPulse className="h-6 w-24" />
            </div>
            <LoadingPulse className="h-8 w-8" />
          </div>
        ))}
      </div>
    </div>
  </div>
)

export const PreviewModalSkeleton = () => (
  <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
    <div className="bg-zinc-900 rounded-lg w-full max-w-6xl max-h-[90vh] overflow-hidden">
      <div className="flex justify-between items-center p-4 border-b border-zinc-700">
        <LoadingPulse className="h-6 w-32" />
        <div className="flex space-x-2">
          <LoadingPulse className="h-8 w-20" />
          <LoadingPulse className="h-8 w-8" />
        </div>
      </div>

      <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
        <div className="space-y-8">
          {/* Hero Section */}
          <div className="text-center space-y-4">
            <LoadingPulse className="h-8 w-48 mx-auto" />
            <LoadingPulse className="h-12 w-96 mx-auto" />
            <div className="space-y-2 max-w-2xl mx-auto">
              <LoadingPulse className="h-4 w-full" />
              <LoadingPulse className="h-4 w-3/4 mx-auto" />
            </div>
          </div>

          {/* Content Sections */}
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="bg-zinc-800 rounded-lg p-6 space-y-4">
              <LoadingPulse className="h-6 w-40" />
              <div className="grid md:grid-cols-2 gap-6">
                <LoadingPulse className="h-32 w-full rounded" />
                <div className="space-y-3">
                  <LoadingPulse className="h-4 w-full" />
                  <LoadingPulse className="h-4 w-5/6" />
                  <LoadingPulse className="h-4 w-4/6" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  </div>
)

export const DashboardSkeleton = () => (
  <div className="space-y-6">
    <LoadingPulse className="h-8 w-48" />

    {/* Stats Cards */}
    <div className="grid md:grid-cols-3 gap-6">
      {Array.from({ length: 3 }).map((_, index) => (
        <div key={index} className="bg-zinc-800 rounded-lg p-6 space-y-3">
          <div className="flex items-center justify-between">
            <LoadingPulse className="h-5 w-24" />
            <LoadingPulse className="w-8 h-8 rounded" />
          </div>
          <LoadingPulse className="h-8 w-16" />
          <LoadingPulse className="h-3 w-32" />
        </div>
      ))}
    </div>

    {/* Recent Activity */}
    <div className="bg-zinc-800 rounded-lg p-6 space-y-4">
      <LoadingPulse className="h-6 w-32" />
      <div className="space-y-3">
        {Array.from({ length: 5 }).map((_, index) => (
          <div key={index} className="flex items-center space-x-3 p-3 bg-zinc-700 rounded">
            <LoadingPulse className="w-8 h-8 rounded-full" />
            <div className="flex-1 space-y-1">
              <LoadingPulse className="h-4 w-3/4" />
              <LoadingPulse className="h-3 w-1/2" />
            </div>
            <LoadingPulse className="h-3 w-16" />
          </div>
        ))}
      </div>
    </div>
  </div>
)
