import { forwardRef } from 'react'

const Card = forwardRef(
  ({ children, className = '', hover = true, padding = 'md', ...props }, ref) => {
    const baseClasses =
      'bg-zinc-800/90 backdrop-blur-sm border border-zinc-700/50 transition-all duration-300'

    const hoverClasses = hover
      ? 'hover:bg-zinc-800 hover:border-amber-600/30 hover:shadow-2xl hover:-translate-y-1'
      : ''

    const paddingClasses = {
      none: '',
      sm: 'p-4',
      md: 'p-6',
      lg: 'p-8',
      xl: 'p-10',
    }

    const classes = `${baseClasses} ${hoverClasses} ${paddingClasses[padding]} rounded-xl ${className}`

    return (
      <div ref={ref} className={classes} {...props}>
        {children}
      </div>
    )
  }
)

Card.displayName = 'Card'

export default Card
