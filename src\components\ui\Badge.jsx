import { forwardRef } from 'react'

const Badge = forwardRef(
  ({ children, variant = 'default', size = 'md', className = '', ...props }, ref) => {
    const baseClasses = 'inline-flex items-center font-medium transition-colors'

    const variants = {
      default: 'bg-zinc-700 text-zinc-200 border border-zinc-600',
      primary: 'bg-amber-600/20 text-amber-400 border border-amber-600/30',
      success: 'bg-green-600/20 text-green-400 border border-green-600/30',
      warning: 'bg-yellow-600/20 text-yellow-400 border border-yellow-600/30',
      danger: 'bg-red-600/20 text-red-400 border border-red-600/30',
      info: 'bg-blue-600/20 text-blue-400 border border-blue-600/30',
    }

    const sizes = {
      sm: 'px-2 py-0.5 text-xs rounded-md',
      md: 'px-2.5 py-1 text-sm rounded-lg',
      lg: 'px-3 py-1.5 text-base rounded-lg',
    }

    const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`

    return (
      <span ref={ref} className={classes} {...props}>
        {children}
      </span>
    )
  }
)

Badge.displayName = 'Badge'

export default Badge
