/**
 * BusinessUnitApiService - Simple service for business unit API operations
 * Handles API calls to fetch business unit data with basic error handling
 */

import fallbackDataService from './FallbackDataService'

class BusinessUnitApiService {
  constructor() {
    this.baseUrl = '/api'
    this.retryAttempts = 3
    this.retryDelay = 1000 // 1 second
  }



  /**
   * Make API request with error handling and retry logic
   * @param {string} url - API endpoint URL
   * @param {object} options - Fetch options
   * @param {number} attempt - Current retry attempt
   * @returns {Promise<object>} API response data
   */
  async makeRequest(url, options = {}, attempt = 1) {
    return this._executeRequest(url, options, attempt)
  }

  async _executeRequest(url, options, attempt) {
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 second timeout

      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        signal: controller.signal,
        ...options,
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.error || 'API request failed')
      }

      return data.data
    } catch (error) {
      // Retry logic for network errors
      if (
        attempt < this.retryAttempts &&
        (error.name === 'AbortError' ||
          error.message.includes('fetch') ||
          error.message.includes('network'))
      ) {
        console.warn(`Request failed, retrying (${attempt}/${this.retryAttempts}):`, error.message)
        await this.delay(this.retryDelay * attempt)
        return this._executeRequest(url, options, attempt + 1)
      }

      console.error('API request failed:', error)
      throw error
    }
  }

  /**
   * Delay utility for retry logic
   */
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  /**
   * Get business unit data by slug
   * @param {string} slug - Business unit slug
   * @returns {Promise<object>} Business unit data
   */
  async getBusinessUnit(slug) {
    try {
      // Fetch from API
      const data = await this.makeRequest(`${this.baseUrl}/business-units/${slug}`)
      return data
    } catch (error) {
      // Fallback to static data service
      try {
        console.warn('API failed, using static data fallback:', error.message)
        const staticData = await fallbackDataService.getBusinessUnit(slug)
        return staticData
      } catch (fallbackError) {
        console.error('All fallback methods failed:', fallbackError)
        throw error
      }
    }
  }

  /**
   * Get all business units
   * @returns {Promise<Array>} List of business units
   */
  async getAllBusinessUnits() {
    try {
      const data = await this.makeRequest(`${this.baseUrl}/business-units`)
      return data
    } catch (error) {
      // Fallback to static data service
      try {
        console.warn('API failed, using static data fallback for all business units')
        const staticData = await fallbackDataService.getAllBusinessUnits()
        return staticData
      } catch (fallbackError) {
        console.error('All fallback methods failed:', fallbackError)
        throw error
      }
    }
  }


}

// Create and export singleton instance
const businessUnitApiService = new BusinessUnitApiService()
export default businessUnitApiService
