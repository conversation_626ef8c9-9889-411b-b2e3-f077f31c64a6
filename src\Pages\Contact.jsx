import Section from '../components/ui/Section'
import Card from '../components/ui/Card'
import Badge from '../components/ui/Badge'
import Button from '../components/ui/Button'
import AnimatedElement from '../components/ui/AnimatedElement'

export default function Contact() {
  return (
    <>
      {/* Hero Section */}
      <Section padding="xl">
        <div className="text-center max-w-4xl mx-auto mb-16">
          <AnimatedElement animation="fadeInUp">
            <Badge variant="primary" size="lg" className="mb-6">
              Hubung<PERSON> Ka<PERSON>
            </Badge>
            <h1 className="text-responsive-xl mb-6">
              Mari <span className="gradient-text">Berkolaborasi</span>
            </h1>
            <p className="text-xl text-zinc-300 leading-relaxed">
              Ka<PERSON> siap membantu kebutuhan digital Anda. Silakan tinggalkan pesan atau hubungi
              kontak di bawah.
            </p>
          </AnimatedElement>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Contact Info */}
          <AnimatedElement animation="fadeInUp">
            <Card padding="lg" className="h-fit">
              <h2 className="text-2xl font-bold text-white mb-6">Informasi Kontak</h2>

              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center flex-shrink-0">
                    <svg
                      className="w-6 h-6 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-white mb-1">Email</h3>
                    <p className="text-zinc-400"><EMAIL></p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center flex-shrink-0">
                    <svg
                      className="w-6 h-6 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-white mb-1">Telepon</h3>
                    <p className="text-zinc-400">+62 21 1234 5678</p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center flex-shrink-0">
                    <svg
                      className="w-6 h-6 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-white mb-1">Alamat</h3>
                    <p className="text-zinc-400">Cimande, Bogor, Jawa Barat</p>
                  </div>
                </div>
              </div>

              <div className="mt-8 pt-6 border-t border-zinc-700">
                <Button className="w-full" size="lg">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                  Email Kami
                </Button>
              </div>
            </Card>
          </AnimatedElement>

          {/* Contact Form */}
          <AnimatedElement animation="fadeInUp" delay={200} className="lg:col-span-2">
            <Card padding="lg">
              <h2 className="text-2xl font-bold text-white mb-6">Kirim Pesan</h2>

              <form
                action="https://formsubmit.co/<EMAIL>"
                method="POST"
                autoComplete="off"
              >
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-6">
                  <div>
                    <label className="block text-sm font-semibold text-white mb-2">Nama</label>
                    <input
                      type="text"
                      name="nama"
                      placeholder="Nama lengkap"
                      className="w-full px-4 py-3 bg-zinc-700 border border-zinc-600 rounded-lg text-white placeholder-zinc-400 focus:border-amber-500 focus:ring-2 focus:ring-amber-500/20 transition-colors"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-semibold text-white mb-2">Email</label>
                    <input
                      type="email"
                      name="email"
                      placeholder="<EMAIL>"
                      className="w-full px-4 py-3 bg-zinc-700 border border-zinc-600 rounded-lg text-white placeholder-zinc-400 focus:border-amber-500 focus:ring-2 focus:ring-amber-500/20 transition-colors"
                      required
                    />
                  </div>
                </div>

                <div className="mb-6">
                  <label className="block text-sm font-semibold text-white mb-2">Subjek</label>
                  <input
                    type="text"
                    name="subjek"
                    placeholder="Judul pesan"
                    className="w-full px-4 py-3 bg-zinc-700 border border-zinc-600 rounded-lg text-white placeholder-zinc-400 focus:border-amber-500 focus:ring-2 focus:ring-amber-500/20 transition-colors"
                  />
                </div>

                <div className="mb-6">
                  <label className="block text-sm font-semibold text-white mb-2">Pesan</label>
                  <textarea
                    name="pesan"
                    rows="6"
                    placeholder="Tulis pesan Anda..."
                    className="w-full px-4 py-3 bg-zinc-700 border border-zinc-600 rounded-lg text-white placeholder-zinc-400 focus:border-amber-500 focus:ring-2 focus:ring-amber-500/20 transition-colors resize-none"
                    required
                  ></textarea>
                </div>

                <Button type="submit" size="lg" className="w-full">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                    />
                  </svg>
                  Kirim Pesan
                </Button>
              </form>
            </Card>
          </AnimatedElement>
        </div>
      </Section>

      {/* Map Section */}
      <Section padding="xl" background="dark">
        <AnimatedElement animation="fadeInUp">
          <Card padding="none" className="overflow-hidden">
            <div className="p-6 border-b border-zinc-700">
              <h2 className="text-2xl font-bold text-white mb-2">Lokasi Kami</h2>
              <p className="text-zinc-400">Kunjungi kantor kami di Cimande, Bogor</p>
            </div>
            <div className="aspect-video">
              <iframe
                className="w-full h-full"
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d126923.05799363125!2d106.73124176077644!3d-6.595038918405893!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x2e69c5eeb8b5b2e9%3A0x301e8f1fc28b9d0!2sBogor%2C%20West%20Java!5e0!3m2!1sen!2sid!4v1691136744746!5m2!1sen!2sid"
                allowFullScreen=""
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                title="Lokasi Cigi Global"
              ></iframe>
            </div>
          </Card>
        </AnimatedElement>
      </Section>
    </>
  )
}
