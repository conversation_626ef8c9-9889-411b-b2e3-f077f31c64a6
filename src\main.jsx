import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.jsx'

import 'remixicon/fonts/remixicon.css'
import 'animate.css'
import AOS from 'aos'
import 'aos/dist/aos.css'

// Initialize AOS (Animate On Scroll) with default settings
AOS.init({
  duration: 800,
  easing: 'ease-in-out',
  once: true,
  mirror: false,
})

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <App />
  </StrictMode>
)
