/**
 * DetailUsahaTemplateDynamic - Enhanced version of DetailUsahaTemplate with dynamic data loading
 * Supports both static data (backward compatibility) and API data fetching with performance optimizations
 */

import { useMemo, useEffect } from 'react'
import { useBusinessUnit } from '../hooks/useBusinessUnit'
import DetailUsahaTemplate from './DetailUsahaTemplate'
import { BusinessUnitSkeleton } from './ui/SkeletonLoader.jsx'
import ErrorMessage from './ui/ErrorMessage'
// Cache utilities removed for simplicity

const DetailUsahaTemplateDynamic = ({
  unitData = null,
  slug = null,
  enableDynamicLoading = true,
  // Cache and performance monitoring removed for simplicity
}) => {
  // If static data is provided and dynamic loading is disabled, use static data
  const shouldUseDynamicData = enableDynamicLoading && slug && !unitData

  // Fetch dynamic data if needed with performance optimizations
  const {
    data: dynamicData,
    loading,
    error,
    isStale,
    refetch,
    // preloadRelated,
  } = useBusinessUnit(
    shouldUseDynamicData ? slug : null,
    unitData, // Use static data as fallback
    {
      enablePreloading: false, // Preloading disabled for simplicity
      staleWhileRevalidate: true,
      backgroundRefresh: true,
    }
  )

  // Effects removed for simplicity

  // Determine which data to use
  const finalData = useMemo(() => {
    if (unitData && !shouldUseDynamicData) {
      // Use static data when provided and dynamic loading is disabled
      return unitData
    }

    if (dynamicData) {
      // Use dynamic data when available
      return dynamicData
    }

    if (unitData) {
      // Fallback to static data if dynamic loading fails
      return unitData
    }

    return null
  }, [unitData, dynamicData, shouldUseDynamicData])

  // Show loading state with improved UX
  if (shouldUseDynamicData && loading && !finalData) {
    return (
      <div className="min-h-screen">
        <BusinessUnitSkeleton />
      </div>
    )
  }

  // Show error state with retry option
  if (shouldUseDynamicData && error && !finalData) {
    return <ErrorMessage title="Failed to Load Business Unit" message={error} onRetry={refetch} />
  }

  // Show error if no data is available at all
  if (!finalData) {
    return (
      <ErrorMessage
        title="Business Unit Not Found"
        message="The requested business unit could not be found."
      />
    )
  }

  // Show stale data indicator in development
  const showStaleIndicator = isStale && import.meta.env.DEV
  const showDevTools = import.meta.env.DEV

  // Render the template with the final data
  return (
    <>
      {showStaleIndicator && (
        <div className="bg-yellow-600 text-white text-center py-2 text-sm">
          ⚠️ Data may be outdated - Background refresh in progress
        </div>
      )}

      {showDevTools && finalData && (
        <div className="fixed bottom-4 right-4 z-50">
          <button
            onClick={() => refetch(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-xs shadow-lg transition-colors"
            title="Refresh data from API"
          >
            🔄 Refresh
          </button>
        </div>
      )}

      <DetailUsahaTemplate unitData={finalData} />
    </>
  )
}

// Related business units functionality removed for simplicity

export default DetailUsahaTemplateDynamic
