# MySQL Database Setup for Business Units

This directory contains the MySQL database configuration, schema initialization, and data migration scripts for the business units management system.

## Prerequisites

1. **MySQL Server**: Make sure MySQL 5.7+ is installed and running
2. **Node.js**: Node.js 16+ with ES modules support
3. **Environment Variables**: Create a `.env` file with your database credentials

## Environment Configuration

Create a `.env` file in the project root with the following variables:

```env
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password_here
DB_NAME=cigi_global
```

## Database Schema

The system creates four main tables:

### business_units

- Stores main business unit information (name, description, theme, contact info)
- Uses JSON columns for complex data (mission, badges, layout config)
- Primary key: `id` (UUID)
- Unique key: `slug`

### products

- Stores products for each business unit
- Foreign key relationship to business_units
- Uses JSON column for features array
- Supports sorting with `sort_order`

### statistics

- Stores statistics data for each business unit
- Uses <PERSON><PERSON><PERSON> column for statistics items array
- One-to-one relationship with business_units

### gallery_images

- Stores gallery images for each business unit
- Supports sorting with `sort_order`
- Includes metadata (title, description, alt text)

## Available Commands

### NPM Scripts

```bash
# Complete setup (recommended for first time)
npm run db:setup

# Test database connection
npm run db:test

# Initialize schema only
npm run db:init

# Migrate data only
npm run db:migrate

# Verify schema and data
npm run db:verify

# Reset database (clear and re-migrate)
npm run db:reset

# Drop all tables (destructive)
npm run db:drop

# Run comprehensive test
npm run test:mysql
```

### Direct Node Commands

```bash
# Complete setup
node src/server/database/index.js setup

# Test connection
node src/server/database/index.js test

# Initialize schema
node src/server/database/index.js init

# Migrate data
node src/server/database/index.js migrate

# Verify setup
node src/server/database/index.js verify

# Reset database
node src/server/database/index.js reset

# Drop tables
node src/server/database/index.js drop
```

## Files Overview

### mysql-config.js

- Database connection configuration
- Connection pool setup
- Query execution utilities
- Transaction support

### mysql-init.js

- Database schema creation
- Table definitions with proper indexes
- Schema verification functions
- Table management utilities

### mysql-migrate.js

- Data migration from static files
- Data transformation functions
- Migration verification
- Data cleanup utilities

### index.js

- Main entry point combining all functionality
- CLI interface for database operations
- Orchestrates setup, migration, and verification

## Migration Process

**Note: Static data files have been removed.** All business unit data is now managed through the database and admin interface:

1. **Import Static Data**: Reads existing JavaScript data files
2. **Transform Data**: Converts to database-compatible format
3. **Insert Records**: Creates database records with proper relationships
4. **Verify Migration**: Confirms all data was migrated correctly

## Data Structure Mapping

### Static Data → Database

- `name` → `business_units.name`
- `badgeText` → `business_units.badge_text`
- `themeColor` → `business_units.theme_color_*`
- `products.items[]` → `products` table records
- `statistics.items[]` → JSON in `statistics.items`
- `gallery.images[]` → `gallery_images` table records
- `mission[]` → JSON in `business_units.mission`
- `badges[]` → JSON in `business_units.badges`

## Troubleshooting

### Connection Issues

1. Verify MySQL is running: `mysql -u root -p`
2. Check credentials in `.env` file
3. Ensure database user has proper permissions
4. Test connection: `npm run db:test`

### Migration Issues

1. Check if static data files exist in expected locations
2. Verify file permissions for reading data files
3. Check MySQL user has CREATE, INSERT, UPDATE, DELETE permissions
4. Review error logs for specific issues

### Schema Issues

1. Ensure MySQL version is 5.7+ for JSON column support
2. Check if database exists and is accessible
3. Verify foreign key constraints are properly handled
4. Use `npm run db:drop` and `npm run db:setup` to recreate schema

## Performance Considerations

- Connection pooling is configured for optimal performance
- Proper indexes are created on frequently queried columns
- JSON columns are used efficiently for complex data structures
- Foreign key constraints ensure data integrity
- Transactions are used for data consistency during migrations

## Security Notes

- Never commit `.env` file with real credentials
- Use strong passwords for database users
- Limit database user permissions to only what's needed
- Consider using SSL connections for production
- Regularly backup your database

## Next Steps

After successful database setup:

1. Implement API endpoints for CRUD operations
2. Create admin UI components for data management
3. Update DetailUsahaTemplate to use API data
4. Implement caching strategy for better performance
