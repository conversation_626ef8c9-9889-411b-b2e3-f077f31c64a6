/**
 * AuthManager - Enhanced authentication system with server-side session management
 * Now uses secure session management with SQLite storage
 */

class AuthManager {
  constructor() {
    this.isAuthenticated = false
    this.user = null
    this.sessionId = null
    this.apiBaseUrl = 'http://localhost:3002/api'
    this.sessionKey = 'cigi-admin-session-id'

    // Fallback credentials for development
    this.fallbackCredentials = {
      username: 'admin',
      password: 'cigi2024!',
    }
  }

  /**
   * Initialize authentication manager
   */
  async init() {
    await this.checkExistingSession()
  }

  /**
   * Check for existing session
   */
  async checkExistingSession() {
    try {
      const sessionId = localStorage.getItem(this.sessionKey)
      if (sessionId) {
        const isValid = await this.validateSession(sessionId)
        if (isValid) {
          return true
        } else {
          // Session expired or invalid
          this.logout()
        }
      }
    } catch (error) {
      console.error('Error checking session:', error)
      this.logout()
    }
    return false
  }

  /**
   * Login with username and password
   */
  async login(username, password) {
    try {
      // Try server-side authentication first
      const response = await fetch(`${this.apiBaseUrl}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      })

      if (response.ok) {
        const result = await response.json()

        if (result.success) {
          this.sessionId = result.sessionId
          this.isAuthenticated = true
          this.user = result.user

          // Store session ID in localStorage
          localStorage.setItem(this.sessionKey, result.sessionId)

          console.log('Admin login successful (server-side)')
          return { success: true, user: result.user }
        }
      }

      // Fallback to client-side authentication for development
      if (
        username === this.fallbackCredentials.username &&
        password === this.fallbackCredentials.password
      ) {
        const user = {
          id: 1,
          username: username,
          role: 'admin',
          permissions: ['read', 'write', 'delete', 'manage_modules', 'manage_users'],
        }

        this.isAuthenticated = true
        this.user = user

        console.log('Admin login successful (fallback)')
        return { success: true, user }
      }

      return { success: false, error: 'Invalid credentials' }
    } catch (error) {
      console.error('Login error:', error)

      // Fallback authentication if server is unavailable
      if (
        username === this.fallbackCredentials.username &&
        password === this.fallbackCredentials.password
      ) {
        const user = {
          id: 1,
          username: username,
          role: 'admin',
          permissions: ['read', 'write', 'delete', 'manage_modules', 'manage_users'],
        }

        this.isAuthenticated = true
        this.user = user

        console.log('Admin login successful (offline fallback)')
        return { success: true, user }
      }

      return { success: false, error: 'Login failed' }
    }
  }

  /**
   * Validate session with server
   */
  async validateSession(sessionId) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/auth/validate`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${sessionId}`,
        },
      })

      if (response.ok) {
        const result = await response.json()
        if (result.valid) {
          this.sessionId = sessionId
          this.isAuthenticated = true
          this.user = result.user
          return true
        }
      }
    } catch (error) {
      console.error('Session validation error:', error)
    }
    return false
  }

  /**
   * Logout user
   */
  async logout() {
    try {
      if (this.sessionId) {
        // Try to logout from server
        await fetch(`${this.apiBaseUrl}/auth/logout`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${this.sessionId}`,
          },
        })
      }
    } catch (error) {
      console.error('Server logout error:', error)
    }

    // Clear local state
    localStorage.removeItem(this.sessionKey)
    this.isAuthenticated = false
    this.user = null
    this.sessionId = null
    console.log('Admin logout successful')
  }

  /**
   * Check if user is authenticated
   */
  isLoggedIn() {
    return this.isAuthenticated
  }

  /**
   * Get current user
   */
  getCurrentUser() {
    return this.user
  }

  /**
   * Check if user has permission
   */
  hasPermission(permission) {
    if (!this.isAuthenticated || !this.user) {
      return false
    }

    return this.user.permissions.includes(permission) || this.user.role === 'admin'
  }

  /**
   * Require authentication (throws error if not authenticated)
   */
  requireAuth() {
    if (!this.isAuthenticated) {
      throw new Error('Authentication required')
    }
  }

  /**
   * Require specific permission
   */
  requirePermission(permission) {
    this.requireAuth()

    if (!this.hasPermission(permission)) {
      throw new Error(`Permission '${permission}' required`)
    }
  }

  /**
   * Extend session
   */
  extendSession() {
    if (!this.isAuthenticated) return false

    try {
      const session = localStorage.getItem(this.sessionKey)
      if (session) {
        const sessionData = JSON.parse(session)
        sessionData.expiresAt = new Date().getTime() + 24 * 60 * 60 * 1000 // Extend by 24 hours
        localStorage.setItem(this.sessionKey, JSON.stringify(sessionData))
        return true
      }
    } catch (error) {
      console.error('Error extending session:', error)
    }
    return false
  }

  /**
   * Get session info
   */
  getSessionInfo() {
    if (!this.isAuthenticated) return null

    try {
      const session = localStorage.getItem(this.sessionKey)
      if (session) {
        const sessionData = JSON.parse(session)
        return {
          loginTime: new Date(sessionData.loginTime),
          expiresAt: new Date(sessionData.expiresAt),
          timeRemaining: sessionData.expiresAt - new Date().getTime(),
        }
      }
    } catch (error) {
      console.error('Error getting session info:', error)
    }
    return null
  }

  /**
   * Change password (basic implementation)
   */
  async changePassword(currentPassword, newPassword) {
    this.requireAuth()

    if (currentPassword !== this.defaultCredentials.password) {
      return { success: false, error: 'Current password is incorrect' }
    }

    if (newPassword.length < 6) {
      return { success: false, error: 'New password must be at least 6 characters' }
    }

    // In production, this would update the password in a secure database
    this.defaultCredentials.password = newPassword

    console.log('Password changed successfully')
    return { success: true }
  }

  /**
   * Get authentication status
   */
  getAuthStatus() {
    return {
      isAuthenticated: this.isAuthenticated,
      user: this.user,
      sessionInfo: this.getSessionInfo(),
    }
  }
}

// Create singleton instance
const authManager = new AuthManager()

export default authManager
