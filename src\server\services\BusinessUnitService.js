/**
 * BusinessUnitService - Service layer for business unit CRUD operations
 * Handles data validation, error handling, and database operations for business units
 */

import { executeQuery } from '../database/mysql-config.js'
import { v4 as uuidv4 } from 'uuid'

class BusinessUnitService {
  /**
   * Get all business units with basic information
   * @returns {Promise<Array>} List of business units with name, badge, and last modified date
   */
  async getAllBusinessUnits() {
    try {
      const query = `
        SELECT 
          id,
          name,
          slug,
          badge_text,
          badge_variant,
          badge_icon,
          description,
          updated_at
        FROM business_units 
        ORDER BY name ASC
      `

      const results = await executeQuery(query)

      return results.map((unit) => ({
        id: unit.id,
        name: unit.name,
        slug: unit.slug,
        badgeText: unit.badge_text,
        badgeVariant: unit.badge_variant,
        badgeIcon: unit.badge_icon,
        description: unit.description,
        lastModified: unit.updated_at,
      }))
    } catch (error) {
      console.error('Error getting all business units:', error)
      throw new Error('Failed to retrieve business units')
    }
  }

  /**
   * Get a specific business unit by slug with all related data
   * @param {string} slug - Business unit slug
   * @returns {Promise<Object>} Complete business unit data
   */
  async getBusinessUnit(slug) {
    try {
      // Get business unit basic data
      const businessUnitQuery = `
        SELECT * FROM business_units WHERE slug = ?
      `
      const businessUnits = await executeQuery(businessUnitQuery, [slug])

      if (businessUnits.length === 0) {
        throw new Error('Business unit not found')
      }

      const businessUnit = businessUnits[0]

      // Get products
      const productsQuery = `
        SELECT * FROM products 
        WHERE business_unit_id = ? 
        ORDER BY sort_order ASC, created_at ASC
      `
      const products = await executeQuery(productsQuery, [businessUnit.id])

      // Get statistics
      const statisticsQuery = `
        SELECT * FROM statistics WHERE business_unit_id = ?
      `
      const statistics = await executeQuery(statisticsQuery, [businessUnit.id])

      // Get gallery images
      const galleryQuery = `
        SELECT * FROM gallery_images 
        WHERE business_unit_id = ? 
        ORDER BY sort_order ASC, created_at ASC
      `
      const galleryImages = await executeQuery(galleryQuery, [businessUnit.id])

      // Transform data to match expected format
      return this._transformBusinessUnitData(businessUnit, products, statistics, galleryImages)
    } catch (error) {
      console.error('Error getting business unit:', error)
      throw new Error(`Failed to retrieve business unit: ${error.message}`)
    }
  }

  /**
   * Create a new business unit
   * @param {Object} data - Business unit data
   * @returns {Promise<Object>} Created business unit
   */
  async createBusinessUnit(data) {
    try {
      // Validate required fields
      this._validateBusinessUnitData(data)

      const id = uuidv4()
      const slug = this._generateSlug(data.name)

      // Check if slug already exists
      const existingUnit = await executeQuery('SELECT id FROM business_units WHERE slug = ?', [
        slug,
      ])
      if (existingUnit.length > 0) {
        throw new Error('Business unit with this name already exists')
      }

      const insertQuery = `
        INSERT INTO business_units (
          id, name, slug, badge_text, badge_variant, badge_icon, description,
          theme_color_from, theme_color_to, theme_color_primary,
          layout_config, about_image, about_content, highlight_text, highlight_class,
          vision, mission, badges, contact_address, contact_phone, contact_email, contact_hours
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `

      const values = [
        id,
        data.name,
        slug,
        data.badgeText || '',
        data.badgeVariant || 'primary',
        data.badgeIcon || '',
        data.description || '',
        data.themeColor?.from || '#3b82f6',
        data.themeColor?.to || '#1d4ed8',
        data.themeColor?.primary || '#3b82f6',
        JSON.stringify(data.layoutConfig || { productsLayout: 'grid' }),
        data.aboutImage || '',
        data.aboutContent || '',
        data.highlightText || '',
        data.highlightClass || '',
        data.vision || '',
        JSON.stringify(data.mission || []),
        JSON.stringify(data.badges || []),
        data.contact?.address || '',
        data.contact?.phone || '',
        data.contact?.email || '',
        data.contact?.hours || '',
      ]

      await executeQuery(insertQuery, values)

      return await this.getBusinessUnit(slug)
    } catch (error) {
      console.error('Error creating business unit:', error)
      throw new Error(`Failed to create business unit: ${error.message}`)
    }
  }

  /**
   * Update an existing business unit
   * @param {string} id - Business unit ID
   * @param {Object} data - Updated business unit data
   * @returns {Promise<Object>} Updated business unit
   */
  async updateBusinessUnit(id, data) {
    try {
      // Validate data
      this._validateBusinessUnitData(data, false)

      // Check if business unit exists
      const existing = await executeQuery('SELECT slug FROM business_units WHERE id = ?', [id])
      if (existing.length === 0) {
        throw new Error('Business unit not found')
      }

      const updateQuery = `
        UPDATE business_units SET
          name = ?, badge_text = ?, badge_variant = ?, badge_icon = ?, description = ?,
          theme_color_from = ?, theme_color_to = ?, theme_color_primary = ?,
          layout_config = ?, about_image = ?, about_content = ?, highlight_text = ?, highlight_class = ?,
          vision = ?, mission = ?, badges = ?, contact_address = ?, contact_phone = ?, contact_email = ?, contact_hours = ?,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `

      const values = [
        data.name,
        data.badgeText || '',
        data.badgeVariant || 'primary',
        data.badgeIcon || '',
        data.description || '',
        data.themeColor?.from || '#3b82f6',
        data.themeColor?.to || '#1d4ed8',
        data.themeColor?.primary || '#3b82f6',
        JSON.stringify(data.layoutConfig || { productsLayout: 'grid' }),
        data.aboutImage || '',
        data.aboutContent || '',
        data.highlightText || '',
        data.highlightClass || '',
        data.vision || '',
        JSON.stringify(data.mission || []),
        JSON.stringify(data.badges || []),
        data.contact?.address || '',
        data.contact?.phone || '',
        data.contact?.email || '',
        data.contact?.hours || '',
        id,
      ]

      await executeQuery(updateQuery, values)

      return await this.getBusinessUnit(existing[0].slug)
    } catch (error) {
      console.error('Error updating business unit:', error)
      throw new Error(`Failed to update business unit: ${error.message}`)
    }
  }

  /**
   * Delete a business unit and all related data
   * @param {string} id - Business unit ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteBusinessUnit(id) {
    try {
      // Check if business unit exists
      const existing = await executeQuery('SELECT id FROM business_units WHERE id = ?', [id])
      if (existing.length === 0) {
        throw new Error('Business unit not found')
      }

      // Delete business unit (cascade will handle related data)
      await executeQuery('DELETE FROM business_units WHERE id = ?', [id])

      return true
    } catch (error) {
      console.error('Error deleting business unit:', error)
      throw new Error(`Failed to delete business unit: ${error.message}`)
    }
  }

  /**
   * Get products for a business unit
   * @param {string} businessUnitId - Business unit ID
   * @returns {Promise<Array>} List of products
   */
  async getBusinessUnitProducts(businessUnitId) {
    try {
      const query = `
        SELECT * FROM products 
        WHERE business_unit_id = ? 
        ORDER BY sort_order ASC, created_at ASC
      `
      const products = await executeQuery(query, [businessUnitId])

      return products.map((product) => ({
        id: product.id,
        title: product.title,
        description: product.description,
        image: product.image,
        price: product.price,
        features: JSON.parse(product.features || '[]'),
        sortOrder: product.sort_order,
      }))
    } catch (error) {
      console.error('Error getting business unit products:', error)
      throw new Error('Failed to retrieve products')
    }
  }

  /**
   * Add a product to a business unit
   * @param {string} businessUnitId - Business unit ID
   * @param {Object} productData - Product data
   * @returns {Promise<Object>} Created product
   */
  async addProduct(businessUnitId, productData) {
    try {
      this._validateProductData(productData)

      const id = uuidv4()
      const query = `
        INSERT INTO products (id, business_unit_id, title, description, image, price, features, sort_order)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `

      const sortOrder = productData.sortOrder || 0
      const values = [
        id,
        businessUnitId,
        productData.title,
        productData.description || '',
        productData.image || '',
        productData.price || '',
        JSON.stringify(productData.features || []),
        sortOrder,
      ]

      await executeQuery(query, values)

      // Return the created product
      const createdProduct = await executeQuery('SELECT * FROM products WHERE id = ?', [id])
      return this._transformProductData(createdProduct[0])
    } catch (error) {
      console.error('Error adding product:', error)
      throw new Error(`Failed to add product: ${error.message}`)
    }
  }

  /**
   * Update a product
   * @param {string} productId - Product ID
   * @param {Object} productData - Updated product data
   * @returns {Promise<Object>} Updated product
   */
  async updateProduct(productId, productData) {
    try {
      this._validateProductData(productData, false)

      const query = `
        UPDATE products SET
          title = ?, description = ?, image = ?, price = ?, features = ?, sort_order = ?,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `

      const values = [
        productData.title,
        productData.description || '',
        productData.image || '',
        productData.price || '',
        JSON.stringify(productData.features || []),
        productData.sortOrder || 0,
        productId,
      ]

      await executeQuery(query, values)

      // Return the updated product
      const updatedProduct = await executeQuery('SELECT * FROM products WHERE id = ?', [productId])
      if (updatedProduct.length === 0) {
        throw new Error('Product not found')
      }

      return this._transformProductData(updatedProduct[0])
    } catch (error) {
      console.error('Error updating product:', error)
      throw new Error(`Failed to update product: ${error.message}`)
    }
  }

  /**
   * Delete a product
   * @param {string} productId - Product ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteProduct(productId) {
    try {
      const result = await executeQuery('DELETE FROM products WHERE id = ?', [productId])

      if (result.affectedRows === 0) {
        throw new Error('Product not found')
      }

      return true
    } catch (error) {
      console.error('Error deleting product:', error)
      throw new Error(`Failed to delete product: ${error.message}`)
    }
  }

  /**
   * Update statistics for a business unit
   * @param {string} businessUnitId - Business unit ID
   * @param {Object} statisticsData - Statistics data
   * @returns {Promise<Object>} Updated statistics
   */
  async updateStatistics(businessUnitId, statisticsData) {
    try {
      this._validateStatisticsData(statisticsData)

      // Check if statistics exist for this business unit
      const existing = await executeQuery('SELECT id FROM statistics WHERE business_unit_id = ?', [
        businessUnitId,
      ])

      if (existing.length > 0) {
        // Update existing statistics
        const query = `
          UPDATE statistics SET
            title = ?, description = ?, items = ?, updated_at = CURRENT_TIMESTAMP
          WHERE business_unit_id = ?
        `

        const values = [
          statisticsData.title || '',
          statisticsData.description || '',
          JSON.stringify(statisticsData.items || []),
          businessUnitId,
        ]

        await executeQuery(query, values)
      } else {
        // Create new statistics
        const id = uuidv4()
        const query = `
          INSERT INTO statistics (id, business_unit_id, title, description, items)
          VALUES (?, ?, ?, ?, ?)
        `

        const values = [
          id,
          businessUnitId,
          statisticsData.title || '',
          statisticsData.description || '',
          JSON.stringify(statisticsData.items || []),
        ]

        await executeQuery(query, values)
      }

      // Return updated statistics
      const updated = await executeQuery('SELECT * FROM statistics WHERE business_unit_id = ?', [
        businessUnitId,
      ])
      return this._transformStatisticsData(updated[0])
    } catch (error) {
      console.error('Error updating statistics:', error)
      throw new Error(`Failed to update statistics: ${error.message}`)
    }
  }

  /**
   * Get gallery images for a business unit
   * @param {string} businessUnitId - Business unit ID
   * @returns {Promise<Array>} List of gallery images
   */
  async getBusinessUnitGallery(businessUnitId) {
    try {
      const query = `
        SELECT * FROM gallery_images 
        WHERE business_unit_id = ? 
        ORDER BY sort_order ASC, created_at ASC
      `
      const images = await executeQuery(query, [businessUnitId])

      return images.map((image) => ({
        id: image.id,
        src: image.src,
        alt: image.alt,
        title: image.title,
        description: image.description,
        sortOrder: image.sort_order,
      }))
    } catch (error) {
      console.error('Error getting gallery images:', error)
      throw new Error('Failed to retrieve gallery images')
    }
  }

  /**
   * Add a gallery image
   * @param {string} businessUnitId - Business unit ID
   * @param {Object} imageData - Image data
   * @returns {Promise<Object>} Created image
   */
  async addGalleryImage(businessUnitId, imageData) {
    try {
      this._validateGalleryImageData(imageData)

      const id = uuidv4()
      const query = `
        INSERT INTO gallery_images (id, business_unit_id, src, alt, title, description, sort_order)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `

      const values = [
        id,
        businessUnitId,
        imageData.src,
        imageData.alt || '',
        imageData.title || '',
        imageData.description || '',
        imageData.sortOrder || 0,
      ]

      await executeQuery(query, values)

      // Return the created image
      const createdImage = await executeQuery('SELECT * FROM gallery_images WHERE id = ?', [id])
      return this._transformGalleryImageData(createdImage[0])
    } catch (error) {
      console.error('Error adding gallery image:', error)
      throw new Error(`Failed to add gallery image: ${error.message}`)
    }
  }

  /**
   * Update a gallery image
   * @param {string} imageId - Image ID
   * @param {Object} imageData - Updated image data
   * @returns {Promise<Object>} Updated image
   */
  async updateGalleryImage(imageId, imageData) {
    try {
      this._validateGalleryImageData(imageData, false)

      const query = `
        UPDATE gallery_images SET
          src = ?, alt = ?, title = ?, description = ?, sort_order = ?,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `

      const values = [
        imageData.src,
        imageData.alt || '',
        imageData.title || '',
        imageData.description || '',
        imageData.sortOrder || 0,
        imageId,
      ]

      await executeQuery(query, values)

      // Return the updated image
      const updatedImage = await executeQuery('SELECT * FROM gallery_images WHERE id = ?', [
        imageId,
      ])
      if (updatedImage.length === 0) {
        throw new Error('Gallery image not found')
      }

      return this._transformGalleryImageData(updatedImage[0])
    } catch (error) {
      console.error('Error updating gallery image:', error)
      throw new Error(`Failed to update gallery image: ${error.message}`)
    }
  }

  /**
   * Delete a gallery image
   * @param {string} imageId - Image ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteGalleryImage(imageId) {
    try {
      const result = await executeQuery('DELETE FROM gallery_images WHERE id = ?', [imageId])

      if (result.affectedRows === 0) {
        throw new Error('Gallery image not found')
      }

      return true
    } catch (error) {
      console.error('Error deleting gallery image:', error)
      throw new Error(`Failed to delete gallery image: ${error.message}`)
    }
  }

  // Private helper methods

  /**
   * Transform business unit data from database format to API format
   */
  _transformBusinessUnitData(businessUnit, products, statistics, galleryImages) {
    const statisticsData = statistics.length > 0 ? statistics[0] : null

    return {
      id: businessUnit.id,
      name: businessUnit.name,
      slug: businessUnit.slug,
      badgeText: businessUnit.badge_text,
      badgeVariant: businessUnit.badge_variant,
      badgeIcon: businessUnit.badge_icon,
      description: businessUnit.description,
      themeColor: {
        from: businessUnit.theme_color_from,
        to: businessUnit.theme_color_to,
        primary: businessUnit.theme_color_primary,
      },
      layoutConfig: JSON.parse(businessUnit.layout_config || '{"productsLayout":"grid"}'),
      aboutImage: businessUnit.about_image,
      aboutContent: businessUnit.about_content,
      highlightText: businessUnit.highlight_text,
      highlightClass: businessUnit.highlight_class,
      vision: businessUnit.vision,
      mission: JSON.parse(businessUnit.mission || '[]'),
      badges: JSON.parse(businessUnit.badges || '[]'),
      products: {
        title: 'Produk Unggulan',
        description: 'Produk berkualitas tinggi dari unit usaha kami',
        items: products.map((product) => this._transformProductData(product)),
        cta: { text: 'Lihat Semua Produk' },
      },
      statistics: statisticsData
        ? {
          title: statisticsData.title,
          description: statisticsData.description,
          items: JSON.parse(statisticsData.items || '[]'),
        }
        : null,
      gallery: {
        title: 'Galeri',
        description: 'Dokumentasi kegiatan dan fasilitas',
        images: galleryImages.map((image) => this._transformGalleryImageData(image)),
      },
      contact: {
        address: businessUnit.contact_address,
        phone: businessUnit.contact_phone,
        email: businessUnit.contact_email,
        hours: businessUnit.contact_hours,
      },
      createdAt: businessUnit.created_at,
      updatedAt: businessUnit.updated_at,
    }
  }

  /**
   * Transform product data from database format
   */
  _transformProductData(product) {
    return {
      id: product.id,
      title: product.title,
      description: product.description,
      image: product.image,
      price: product.price,
      features: JSON.parse(product.features || '[]'),
      sortOrder: product.sort_order,
    }
  }

  /**
   * Transform statistics data from database format
   */
  _transformStatisticsData(statistics) {
    return {
      id: statistics.id,
      title: statistics.title,
      description: statistics.description,
      items: JSON.parse(statistics.items || '[]'),
    }
  }

  /**
   * Transform gallery image data from database format
   */
  _transformGalleryImageData(image) {
    return {
      id: image.id,
      src: image.src,
      alt: image.alt,
      title: image.title,
      description: image.description,
      sortOrder: image.sort_order,
    }
  }

  /**
   * Generate URL-friendly slug from name
   */
  _generateSlug(name) {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '')
  }

  /**
   * Validate business unit data
   */
  _validateBusinessUnitData(data, isCreate = true) {
    if (isCreate && !data.name) {
      throw new Error('Business unit name is required')
    }

    if (data.name && (data.name.length < 3 || data.name.length > 100)) {
      throw new Error('Business unit name must be between 3 and 100 characters')
    }

    if (data.badgeText && data.badgeText.length > 50) {
      throw new Error('Badge text must be 50 characters or less')
    }

    if (
      data.badgeVariant &&
      !['primary', 'secondary', 'success', 'warning', 'info'].includes(data.badgeVariant)
    ) {
      throw new Error('Invalid badge variant')
    }

    if (data.contact?.email && !this._isValidEmail(data.contact.email)) {
      throw new Error('Invalid email format')
    }
  }

  /**
   * Validate product data
   */
  _validateProductData(data, isCreate = true) {
    if (isCreate && !data.title) {
      throw new Error('Product title is required')
    }

    if (data.title && (data.title.length < 3 || data.title.length > 100)) {
      throw new Error('Product title must be between 3 and 100 characters')
    }

    if (data.price && data.price.length > 50) {
      throw new Error('Product price must be 50 characters or less')
    }
  }

  /**
   * Validate statistics data
   */
  _validateStatisticsData(data) {
    if (data.items && !Array.isArray(data.items)) {
      throw new Error('Statistics items must be an array')
    }

    if (data.items) {
      for (const item of data.items) {
        if (!item.value || !item.label) {
          throw new Error('Each statistics item must have value and label')
        }
      }
    }
  }

  /**
   * Validate gallery image data
   */
  _validateGalleryImageData(data, isCreate = true) {
    if (isCreate && !data.src) {
      throw new Error('Image source URL is required')
    }

    if (data.src && !this._isValidUrl(data.src)) {
      throw new Error('Invalid image URL format')
    }
  }

  /**
   * Validate email format
   */
  _isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  /**
   * Validate URL format
   */
  _isValidUrl(url) {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }
}

export default BusinessUnitService
